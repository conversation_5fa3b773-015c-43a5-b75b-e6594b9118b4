"""
OmniLink 认证工具模块
版本: 1.0
创建日期: 2024-12-19
描述: JWT认证和用户验证工具
"""

from datetime import datetime, timedelta
from typing import Optional, Union
from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
import os
import logging

from database import get_db
from models import User, UserSession

logger = logging.getLogger(__name__)

# JWT配置 - 安全性增强
import secrets
SECRET_KEY = os.getenv("JWT_SECRET_KEY")
if not SECRET_KEY:
    # 生产环境必须设置JWT_SECRET_KEY环境变量
    if os.getenv("ENVIRONMENT") == "production":
        raise ValueError("生产环境必须设置JWT_SECRET_KEY环境变量")
    # 开发环境生成随机密钥
    SECRET_KEY = secrets.token_urlsafe(32)
    logger.warning("使用随机生成的JWT密钥，生产环境请设置JWT_SECRET_KEY环境变量")

ALGORITHM = os.getenv("JWT_ALGORITHM", "HS256")
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("JWT_ACCESS_TOKEN_EXPIRE_MINUTES", "30"))

# 密码加密
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# HTTP Bearer认证
security = HTTPBearer()

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    try:
        # 清理哈希值 - 移除可能的转义字符
        cleaned_hash = hashed_password
        if cleaned_hash.startswith('\\'):
            # 移除开头的反斜杠转义字符
            cleaned_hash = cleaned_hash.replace('\\\\', '\\').replace('\\$', '$')
            logger.info(f"🔧 清理哈希值: 原始={hashed_password[:20]}... -> 清理后={cleaned_hash[:20]}...")

        # 移除调试信息，避免密码信息泄露
        result = pwd_context.verify(plain_password, cleaned_hash)
        return result
    except Exception as e:
        logger.error(f"密码验证失败")  # 不记录具体错误信息
        # 如果清理后的哈希仍然失败，尝试原始哈希
        try:
            if cleaned_hash != hashed_password:
                result = pwd_context.verify(plain_password, hashed_password)
                return result
        except Exception:
            pass  # 静默处理，不记录敏感信息
        return False

def get_password_hash(password: str) -> str:
    """生成密码哈希"""
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """创建访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> Optional[dict]:
    """验证令牌"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            return None
        return payload
    except JWTError:
        return None

async def get_user_by_username(db: AsyncSession, username: str) -> Optional[User]:
    """根据用户名获取用户"""
    try:
        result = await db.execute(select(User).where(User.username == username))
        return result.scalar_one_or_none()
    except Exception as e:
        logger.error(f"获取用户失败: {e}")
        return None

async def get_user_by_id(db: AsyncSession, user_id: int) -> Optional[User]:
    """根据用户ID获取用户"""
    try:
        result = await db.execute(select(User).where(User.id == user_id))
        return result.scalar_one_or_none()
    except Exception as e:
        logger.error(f"获取用户失败: {e}")
        return None

async def authenticate_user(db: AsyncSession, username: str, password: str) -> Optional[User]:
    """验证用户身份"""
    user = await get_user_by_username(db, username)
    if not user:
        return None
    if not verify_password(password, user.hashed_password):
        return None
    if not user.is_active:
        return None
    return user

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> User:
    """获取当前用户"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无法验证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        # 验证令牌
        payload = verify_token(credentials.credentials)
        if payload is None:
            raise credentials_exception
        
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        
        # 获取用户
        user = await get_user_by_username(db, username)
        if user is None:
            raise credentials_exception

        # 🔧 调试：记录用户信息
        logger.info(f"🔍 get_current_user调试 - 查询到用户: ID={user.id}, username={user.username}, org_id={user.organization_id}")

        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户账号已被禁用"
            )

        # 🔧 调试：返回前再次检查用户信息
        logger.info(f"🔍 get_current_user调试 - 返回用户: ID={user.id}, username={user.username}, org_id={user.organization_id}")

        return user
        
    except JWTError:
        raise credentials_exception
    except Exception as e:
        logger.error(f"获取当前用户失败: {e}")
        raise credentials_exception

async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """获取当前活跃用户"""
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账号已被禁用"
        )
    return current_user

async def get_current_superuser(current_user: User = Depends(get_current_user)) -> User:
    """获取当前超级用户"""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要超级管理员权限"
        )
    return current_user

async def create_user_session(
    db: AsyncSession,
    user_id: int,
    session_id: str,
    ip_address: str,
    user_agent: str,
    expires_at: datetime
) -> UserSession:
    """创建用户会话"""
    try:
        session = UserSession(
            session_id=session_id,
            user_id=user_id,
            ip_address=ip_address,
            user_agent=user_agent,
            expires_at=expires_at
        )
        db.add(session)
        await db.commit()
        await db.refresh(session)
        return session
    except Exception as e:
        logger.error(f"创建用户会话失败: {e}")
        await db.rollback()
        raise

async def update_user_login_info(
    db: AsyncSession,
    user: User,
    ip_address: str
) -> None:
    """更新用户登录信息"""
    try:
        user.last_login_ip = user.current_login_ip
        user.current_login_ip = ip_address
        user.last_login_at = datetime.utcnow()
        user.login_count = (user.login_count or 0) + 1
        
        await db.commit()
        await db.refresh(user)
    except Exception as e:
        logger.error(f"更新用户登录信息失败: {e}")
        await db.rollback()
        raise

async def require_superuser(
    current_user: User = Depends(get_current_user)
) -> User:
    """要求超级管理员权限"""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有超级管理员可以执行此操作"
        )
    return current_user

async def get_current_user_from_token(token: str, db: AsyncSession) -> Optional[User]:
    """从token获取当前用户（用于WebSocket）"""
    try:
        payload = verify_token(token)
        if payload is None:
            return None

        username: str = payload.get("sub")
        if username is None:
            return None

        user = await get_user_by_username(db, username)
        if user is None:
            return None

        if not user.is_active:
            return None

        return user
    except Exception as e:
        logger.error(f"从token获取用户失败: {e}")
        return None


async def check_user_permission(db: AsyncSession, user_id: int, permission_code: str) -> bool:
    """
    检查用户是否具有指定权限
    """
    try:
        # 获取用户信息
        user_query = select(User).where(User.id == user_id)
        user_result = await db.execute(user_query)
        user = user_result.scalar_one_or_none()

        if not user:
            return False

        # 基于角色ID进行权限检查
        # 全域管理员(role_id=1)和超级管理员(role_id=2)拥有所有权限
        if user.role_id in [1, 2]:
            return True

        # 管理员(role_id=3)拥有大部分权限
        if user.role_id == 3:
            allowed_permissions = [
                'device.view', 'device.connect', 'device.manage', 'device.force_disconnect',
                'user.view', 'user.create', 'user.edit',
                'application.view', 'application.process'
            ]
            return permission_code in allowed_permissions

        # 普通用户(role_id=4)拥有基础权限
        if user.role_id == 4:
            allowed_permissions = [
                'device.view', 'device.connect',
                'application.view'
            ]
            return permission_code in allowed_permissions

        # 新用户(role_id=5)没有特殊权限
        return False

    except Exception as e:
        logger.error(f"检查用户权限失败: {str(e)}")
        return False


async def check_device_force_disconnect_permission(db: AsyncSession, user_id: int, device_id: int) -> bool:
    """
    检查用户是否有权限强制断开指定设备
    基于角色层级和设备分配情况
    """
    try:
        # 首先检查基础权限
        if not await check_user_permission(db, user_id, "device.force_disconnect"):
            return False

        # 获取用户信息
        user_query = select(User).where(User.id == user_id)
        user_result = await db.execute(user_query)
        user = user_result.scalar_one_or_none()

        if not user:
            return False

        # 全域管理员和超级管理员有权限操作所有设备
        if user.role_id in [1, 2]:  # 全域管理员、超级管理员
            return True

        # 管理员需要检查设备是否在其管理范围内
        if user.role_id == 3:  # 管理员
            # 检查设备是否分配给用户所在的组织层级
            return await check_device_in_user_organization(db, user_id, device_id)

        # 普通用户和新用户没有强制断开权限
        return False

    except Exception as e:
        logger.error(f"检查设备强制断开权限失败: {str(e)}")
        return False


async def check_device_in_user_organization(db: AsyncSession, user_id: int, device_id: int) -> bool:
    """
    检查设备是否在用户的组织管理范围内
    """
    try:
        # 获取用户的组织信息
        user_query = select(User).where(User.id == user_id)
        user_result = await db.execute(user_query)
        user = user_result.scalar_one_or_none()

        if not user:
            return False

        # 暂时简化逻辑：管理员及以上可以访问所有设备
        # 后续可以根据具体的组织层级结构来完善
        if user.role_id in [1, 2, 3]:  # 全域管理员、超级管理员、管理员
            return True

        # 普通用户和新用户暂时不允许强制断开设备
        return False

    except Exception as e:
        logger.error(f"检查设备组织权限失败: {str(e)}")
        return False
