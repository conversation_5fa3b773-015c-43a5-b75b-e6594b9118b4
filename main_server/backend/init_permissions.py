#!/usr/bin/env python3
"""
权限系统初始化脚本
版本: 1.0
创建日期: 2025-07-10
描述: 初始化权限数据，为角色分配相应权限
"""

import asyncio
import sys
import os
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import AsyncSessionLocal

# 基础权限定义
PERMISSIONS = [
    # 用户管理权限
    {"name": "用户查看", "code": "user.view", "description": "查看用户信息", "category": "用户管理"},
    {"name": "用户创建", "code": "user.create", "description": "创建新用户", "category": "用户管理"},
    {"name": "用户编辑", "code": "user.edit", "description": "编辑用户信息", "category": "用户管理"},
    {"name": "用户删除", "code": "user.delete", "description": "删除用户", "category": "用户管理"},
    
    # 组织管理权限
    {"name": "组织查看", "code": "organization.view", "description": "查看组织架构", "category": "组织管理"},
    {"name": "组织管理", "code": "organization.manage", "description": "管理组织架构", "category": "组织管理"},
    
    # 申请管理权限
    {"name": "申请查看", "code": "application.view", "description": "查看申请信息", "category": "申请管理"},
    {"name": "申请处理", "code": "application.process", "description": "处理申请", "category": "申请管理"},
    {"name": "申请统计", "code": "application.stats", "description": "查看申请统计", "category": "申请管理"},
    
    # 设备管理权限
    {"name": "设备查看", "code": "device.view", "description": "查看设备信息", "category": "设备管理"},
    {"name": "设备连接", "code": "device.connect", "description": "连接设备", "category": "设备管理"},
    {"name": "设备管理", "code": "device.manage", "description": "管理设备配置", "category": "设备管理"},
    {"name": "设备分配", "code": "device.assign", "description": "分配设备权限", "category": "设备管理"},
    {"name": "强制断开", "code": "device.force_disconnect", "description": "强制断开设备连接", "category": "设备管理"},
    
    # 角色管理权限
    {"name": "角色查看", "code": "role.view", "description": "查看角色信息", "category": "角色管理"},
    {"name": "角色管理", "code": "role.manage", "description": "管理角色", "category": "角色管理"},
    
    # 系统管理权限
    {"name": "系统配置", "code": "system.config", "description": "系统配置管理", "category": "系统管理"},
    {"name": "审计日志", "code": "system.audit", "description": "查看审计日志", "category": "系统管理"},
]

# 角色权限分配
ROLE_PERMISSIONS = {
    1: [  # 超级管理员 - 所有权限
        "user.view", "user.create", "user.edit", "user.delete",
        "organization.view", "organization.manage",
        "application.view", "application.process", "application.stats",
        "device.view", "device.connect", "device.manage", "device.assign", "device.force_disconnect",
        "role.view", "role.manage",
        "system.config", "system.audit"
    ],
    2: [  # 管理员 - 基础管理权限
        "user.view", "user.create", "user.edit",
        "organization.view",
        "application.view", "application.process", "application.stats",
        "device.view", "device.connect", "device.manage", "device.force_disconnect"
    ],
    3: [  # 普通用户 - 基础查看权限
        "user.view",
        "organization.view",
        "application.view",
        "device.view", "device.connect"
    ],
    4: [  # 新用户 - 最小权限
        "application.view"
    ]
}

async def create_permissions():
    """创建基础权限"""
    async with AsyncSessionLocal() as session:
        try:
            print("🔑 开始创建基础权限...")
            
            for perm_data in PERMISSIONS:
                # 检查权限是否已存在
                result = await session.execute(
                    text("SELECT id FROM permissions WHERE code = :code"),
                    {"code": perm_data["code"]}
                )
                existing_perm = result.scalar_one_or_none()
                
                if existing_perm:
                    print(f"⚠️ 权限 {perm_data['code']} 已存在，跳过创建")
                    continue
                
                # 创建权限
                await session.execute(text("""
                    INSERT INTO permissions (name, code, description, category, is_system_permission, is_active, created_at, updated_at) 
                    VALUES (:name, :code, :description, :category, true, true, NOW(), NOW())
                """), {
                    "name": perm_data["name"],
                    "code": perm_data["code"],
                    "description": perm_data["description"],
                    "category": perm_data["category"]
                })
                
                print(f"✅ 创建权限: {perm_data['name']} ({perm_data['code']})")
            
            await session.commit()
            print("✅ 基础权限创建完成")
            
        except Exception as e:
            await session.rollback()
            print(f"❌ 权限创建失败: {e}")
            raise

async def assign_role_permissions():
    """为角色分配权限"""
    async with AsyncSessionLocal() as session:
        try:
            print("🔗 开始分配角色权限...")
            
            for role_id, permission_codes in ROLE_PERMISSIONS.items():
                print(f"📋 为角色 {role_id} 分配权限...")
                
                for perm_code in permission_codes:
                    # 获取权限ID
                    result = await session.execute(
                        text("SELECT id FROM permissions WHERE code = :code"),
                        {"code": perm_code}
                    )
                    perm_id = result.scalar_one_or_none()
                    
                    if not perm_id:
                        print(f"⚠️ 权限 {perm_code} 不存在，跳过分配")
                        continue
                    
                    # 检查是否已分配
                    result = await session.execute(
                        text("SELECT 1 FROM role_permissions WHERE role_id = :role_id AND permission_id = :perm_id"),
                        {"role_id": role_id, "perm_id": perm_id}
                    )
                    existing = result.scalar_one_or_none()
                    
                    if existing:
                        continue
                    
                    # 分配权限
                    await session.execute(text("""
                        INSERT INTO role_permissions (role_id, permission_id, granted_at) 
                        VALUES (:role_id, :perm_id, NOW())
                    """), {
                        "role_id": role_id,
                        "perm_id": perm_id
                    })
                    
                    print(f"  ✅ 分配权限: {perm_code}")
            
            await session.commit()
            print("✅ 角色权限分配完成")
            
        except Exception as e:
            await session.rollback()
            print(f"❌ 角色权限分配失败: {e}")
            raise

async def check_permissions_status():
    """检查权限状态"""
    async with AsyncSessionLocal() as session:
        try:
            # 检查权限数量
            result = await session.execute(text("SELECT COUNT(*) FROM permissions"))
            perm_count = result.scalar()
            
            # 检查角色权限分配数量
            result = await session.execute(text("SELECT COUNT(*) FROM role_permissions"))
            role_perm_count = result.scalar()
            
            # 检查各角色权限数量
            result = await session.execute(text("""
                SELECT r.name, COUNT(rp.permission_id) as perm_count
                FROM roles r 
                LEFT JOIN role_permissions rp ON r.id = rp.role_id 
                GROUP BY r.id, r.name 
                ORDER BY r.id
            """))
            role_stats = result.fetchall()
            
            print(f"📊 权限系统状态:")
            print(f"   - 总权限数: {perm_count}")
            print(f"   - 角色权限分配数: {role_perm_count}")
            print(f"   - 各角色权限数:")
            for role_name, perm_count in role_stats:
                print(f"     * {role_name}: {perm_count} 个权限")
            
            return perm_count, role_perm_count
            
        except Exception as e:
            print(f"❌ 检查权限状态失败: {e}")
            return 0, 0

async def main():
    """主函数"""
    try:
        print("🚀 开始初始化权限系统...")
        print("="*60)
        
        # 1. 检查初始状态
        print("🔍 检查权限系统初始状态...")
        await check_permissions_status()
        
        # 2. 创建基础权限
        await create_permissions()
        
        # 3. 分配角色权限
        await assign_role_permissions()
        
        # 4. 检查最终状态
        print("\n🔍 检查最终权限系统状态...")
        final_perm_count, final_role_perm_count = await check_permissions_status()
        
        print("\n🎉 权限系统初始化完成！")
        print(f"📋 初始化总结:")
        print(f"   - 创建权限: {final_perm_count} 个")
        print(f"   - 角色权限分配: {final_role_perm_count} 个")
        
    except Exception as e:
        print(f"❌ 权限系统初始化失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
