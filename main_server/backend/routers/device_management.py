"""
设备管理API路由
提供完整的设备CRUD操作和状态管理
"""

import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc
from sqlalchemy.orm import selectinload
from pydantic import BaseModel, Field

from database import get_db
from auth_utils import get_current_user, check_user_permission
from models import Device, DeviceOccupationRecord, SlaveServer, User, Role
from routers.websocket import trigger_device_event
from core.usb_ids_manager import get_usb_ids_manager
from core.device_type_classifier import classify_device_type

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/devices", tags=["设备管理"])


async def update_device_type_classification(db: AsyncSession, device: Device) -> None:
    """
    更新设备类型分类

    Args:
        db: 数据库会话
        device: 设备对象
    """
    try:
        # 使用智能分类器进行设备类型判定
        classification_result = classify_device_type(
            vendor_id=device.vendor_id,
            product_id=device.product_id,
            description=device.description,
            vendor_name=device.usb_ids_vendor_name,
            device_name=device.usb_ids_device_name
        )

        # 更新设备的主服务器判定类型
        device.master_determined_type = classification_result['device_type']
        device.type_determination_time = classification_result['determination_time']
        device.type_determination_source = classification_result['determination_source']

        # 计算最终设备类型（优先级：手动 > 主服务器判定 > 从服务器上报）
        final_type = (
            device.manual_override_type or
            device.master_determined_type or
            device.slave_reported_type or
            'unknown'
        )

        device.final_device_type = final_type

        # 更新设备类型描述
        if classification_result['device_type'] != 'unknown':
            device.device_type = classification_result['type_description']

        await db.commit()

        logger.info(f"设备 {device.device_id} 类型分类完成: {final_type} (置信度: {classification_result['confidence']:.2f})")

    except Exception as e:
        logger.error(f"设备类型分类失败 {device.device_id}: {e}")
        await db.rollback()


async def batch_update_device_types(db: AsyncSession) -> Dict[str, int]:
    """
    批量更新所有设备的类型分类

    Args:
        db: 数据库会话

    Returns:
        Dict: 更新统计信息
    """
    try:
        # 获取所有设备
        result = await db.execute(
            select(Device).options(selectinload(Device.slave_server))
        )
        devices = result.scalars().all()

        stats = {
            'total': len(devices),
            'updated': 0,
            'failed': 0,
            'encryption': 0,
            'storage': 0,
            'input': 0,
            'communication': 0,
            'printer': 0,
            'hub': 0,
            'hardware': 0,
            'unknown': 0
        }

        for device in devices:
            try:
                await update_device_type_classification(db, device)
                stats['updated'] += 1

                # 统计各类型数量
                device_type = device.final_device_type or 'unknown'
                if device_type in stats:
                    stats[device_type] += 1
                else:
                    stats['unknown'] += 1

            except Exception as e:
                logger.error(f"更新设备 {device.device_id} 类型失败: {e}")
                stats['failed'] += 1

        logger.info(f"批量更新设备类型完成: 总计 {stats['total']}, 成功 {stats['updated']}, 失败 {stats['failed']}")
        return stats

    except Exception as e:
        logger.error(f"批量更新设备类型失败: {e}")
        raise


# ==================== 请求/响应模型 ====================

class DeviceUpdateRequest(BaseModel):
    """设备更新请求"""
    custom_name: Optional[str] = Field(None, max_length=200, description="自定义设备名称")
    device_type: Optional[str] = Field(None, description="设备类型")
    remark: Optional[str] = Field(None, max_length=500, description="设备备注")


class DeviceOccupationRequest(BaseModel):
    """设备占用请求"""
    estimated_duration: Optional[int] = Field(None, description="预计使用时长（秒）")
    note: Optional[str] = Field(None, max_length=500, description="占用说明")
    user_contact: Optional[str] = Field(None, max_length=100, description="联系方式")


class DeviceForceDisconnectRequest(BaseModel):
    """设备强制断开请求"""
    reason: Optional[str] = Field(None, max_length=500, description="断开原因")
    notify_user: bool = Field(True, description="是否通知被断开的用户")


class DeviceResponse(BaseModel):
    """设备响应模型"""
    id: int
    device_id: str
    device_name: str
    custom_name: Optional[str]
    auto_generated_name: Optional[str]
    device_type: str
    status: str
    vendor_id: Optional[str]
    product_id: Optional[str]
    hardware_signature: Optional[str]
    description: Optional[str]
    physical_port: Optional[str]
    port_location_code: Optional[str]
    remark: Optional[str]
    
    # 服务器信息
    server_id: int
    server_name: str
    server_ip: str
    server_port: Optional[int]
    server_status: str
    
    # 时间信息
    created_at: datetime
    updated_at: Optional[datetime]
    last_connected: Optional[datetime]
    last_connected_user: Optional[str]
    connection_count: int
    total_usage_time: int
    
    # 占用信息
    occupied_start_time: Optional[datetime]
    occupied_duration: int
    estimated_end_time: Optional[datetime]
    occupation_note: Optional[str]
    current_user_name: Optional[str]
    current_user_contact: Optional[str]

    # USB.IDS信息
    vendor_name: Optional[str] = None
    device_name_from_ids: Optional[str] = None
    full_device_name: Optional[str] = None
    
    # 设备属性
    is_real_hardware: bool
    auto_bind_eligible: bool

    class Config:
        from_attributes = True


class DeviceListResponse(BaseModel):
    """设备列表响应"""
    devices: List[DeviceResponse]
    total: int
    page: int
    page_size: int


# ==================== 辅助函数 ====================

async def enhance_device_with_usb_ids(device: Device) -> Dict[str, Optional[str]]:
    """使用USB.IDS数据库增强设备信息"""
    try:
        if not device.vendor_id or not device.product_id:
            return {"vendor_name": None, "device_name_from_ids": None, "full_device_name": None}

        # 解析VID和PID
        vendor_id = int(device.vendor_id.replace('0x', ''), 16) if device.vendor_id.startswith('0x') else int(device.vendor_id, 16)
        product_id = int(device.product_id.replace('0x', ''), 16) if device.product_id.startswith('0x') else int(device.product_id, 16)

        # 获取USB.IDS管理器
        usb_ids_manager = await get_usb_ids_manager()

        # 查询设备信息
        device_info = await usb_ids_manager.get_device_info(vendor_id, product_id)

        return {
            "vendor_name": device_info.get("vendor_name"),
            "device_name_from_ids": device_info.get("device_name"),
            "full_device_name": device_info.get("full_name")
        }

    except Exception as e:
        logger.warning(f"增强设备信息失败 (VID:{device.vendor_id}, PID:{device.product_id}): {e}")
        return {"vendor_name": None, "device_name_from_ids": None, "full_device_name": None}


# ==================== 设备列表和查询 ====================

@router.get("", response_model=DeviceListResponse)
async def get_devices(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    server_id: Optional[int] = Query(None, description="从服务器ID"),
    device_type: Optional[str] = Query(None, description="设备类型"),
    device_status: Optional[str] = Query(None, description="设备状态"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", description="排序方向"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取设备列表"""
    try:
        # 构建查询
        query = select(Device).options(
            selectinload(Device.slave_server),
            selectinload(Device.occupation_records)
        )
        
        # 添加筛选条件
        conditions = []
        
        if search:
            search_pattern = f"%{search}%"
            conditions.append(
                or_(
                    Device.device_name.ilike(search_pattern),
                    Device.custom_name.ilike(search_pattern),
                    Device.device_id.ilike(search_pattern),
                    Device.description.ilike(search_pattern)
                )
            )
        
        if server_id:
            conditions.append(Device.slave_server_id == server_id)
        
        if device_type:
            conditions.append(Device.device_type == device_type)
        
        if device_status:
            conditions.append(Device.status == device_status)
        
        if conditions:
            query = query.where(and_(*conditions))
        
        # 添加排序
        if sort_order.lower() == "desc":
            query = query.order_by(desc(getattr(Device, sort_by, Device.created_at)))
        else:
            query = query.order_by(getattr(Device, sort_by, Device.created_at))
        
        # 获取总数
        count_query = select(func.count(Device.id))
        if conditions:
            count_query = count_query.where(and_(*conditions))
        
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 分页
        offset = (page - 1) * page_size
        query = query.offset(offset).limit(page_size)
        
        # 执行查询
        result = await db.execute(query)
        devices = result.scalars().all()
        
        # 构建响应数据
        device_responses = []
        for device in devices:
            # 获取当前占用记录
            current_occupation = None
            if device.occupation_records:
                current_occupation = next(
                    (record for record in device.occupation_records if record.status == "active"),
                    None
                )
            
            # 确定设备的实际状态（考虑从服务器实时在线状态）
            actual_status = device.status
            if device.slave_server:
                # 基于心跳时间判断从服务器实时在线状态
                current_time = datetime.now()
                heartbeat_timeout = timedelta(seconds=60)  # 60秒心跳超时

                is_server_online = False
                if device.slave_server.last_heartbeat:
                    time_since_heartbeat = current_time - device.slave_server.last_heartbeat
                    is_server_online = time_since_heartbeat <= heartbeat_timeout

                # 如果从服务器离线，设备状态应为离线
                if not is_server_online:
                    actual_status = "offline"

            # 确定最终显示的设备类型
            final_device_type = (
                device.final_device_type or
                device.master_determined_type or
                device.slave_reported_type or
                device.device_type or
                'unknown'
            )

            # 生成设备显示名称，优先级：custom_name > device_name > auto_generated_name > description > device_id
            display_name = (
                device.custom_name or
                device.device_name or
                device.auto_generated_name or
                device.description or
                device.device_id or
                "未知设备"
            )

            device_data = {
                "id": device.id,
                "device_id": device.device_id,
                "device_name": display_name,  # 确保总是有值
                "custom_name": device.custom_name,
                "auto_generated_name": device.auto_generated_name,
                "device_type": final_device_type,
                "status": actual_status,
                "vendor_id": device.vendor_id,
                "product_id": device.product_id,
                "hardware_signature": device.hardware_signature,
                "description": device.description,
                "physical_port": device.physical_port,
                "port_location_code": device.port_location_code,
                "remark": device.remark,
                
                # 服务器信息
                "server_id": device.slave_server.id if device.slave_server else 0,
                "server_name": device.slave_server.name if device.slave_server else "未知",
                "server_ip": device.slave_server.ip_address if device.slave_server else "",
                "server_port": device.slave_server.port if device.slave_server else None,
                "server_status": device.slave_server.status if device.slave_server else "offline",
                
                # 时间信息
                "created_at": device.created_at,
                "updated_at": device.updated_at,
                "last_connected": device.last_connected,
                "last_connected_user": device.last_connected_user,
                "connection_count": device.connection_count or 0,
                "total_usage_time": device.total_usage_time or 0,
                
                # 占用信息
                "occupied_start_time": device.occupied_start_time,
                "occupied_duration": device.occupied_duration or 0,
                "estimated_end_time": device.estimated_end_time,
                "occupation_note": device.occupation_note,
                "current_user_name": current_occupation.user.username if current_occupation else None,
                "current_user_contact": current_occupation.user_contact if current_occupation else None,
                
                # 设备属性
                "is_real_hardware": device.is_real_hardware if device.is_real_hardware is not None else True,
                "auto_bind_eligible": device.auto_bind_eligible if device.auto_bind_eligible is not None else True
            }

            # 增强设备信息（USB.IDS）
            usb_ids_info = await enhance_device_with_usb_ids(device)
            device_data.update(usb_ids_info)

            device_responses.append(DeviceResponse(**device_data))
        
        return DeviceListResponse(
            devices=device_responses,
            total=total,
            page=page,
            page_size=page_size
        )
        
    except Exception as e:
        logger.error(f"获取设备列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取设备列表失败"
        )


@router.get("/{device_id}", response_model=DeviceResponse)
async def get_device_detail(
    device_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取设备详情"""
    try:
        # 查询设备
        query = select(Device).options(
            selectinload(Device.slave_server),
            selectinload(Device.occupation_records).selectinload(DeviceOccupationRecord.user)
        ).where(Device.id == device_id)
        
        result = await db.execute(query)
        device = result.scalar_one_or_none()
        
        if not device:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="设备不存在"
            )
        
        # 获取当前占用记录
        current_occupation = None
        if device.occupation_records:
            current_occupation = next(
                (record for record in device.occupation_records if record.status == "active"),
                None
            )
        
        # 生成设备显示名称
        display_name = (
            device.custom_name or
            device.device_name or
            device.auto_generated_name or
            device.description or
            device.device_id or
            "未知设备"
        )

        # 构建响应数据
        device_data = {
            "id": device.id,
            "device_id": device.device_id,
            "device_name": display_name,  # 确保总是有值
            "custom_name": device.custom_name,
            "auto_generated_name": device.auto_generated_name,
            "device_type": device.device_type,
            "status": device.status,
            "vendor_id": device.vendor_id,
            "product_id": device.product_id,
            "hardware_signature": device.hardware_signature,
            "description": device.description,
            "physical_port": device.physical_port,
            "port_location_code": device.port_location_code,
            "remark": device.remark,
            
            # 服务器信息
            "server_id": device.slave_server.id if device.slave_server else 0,
            "server_name": device.slave_server.name if device.slave_server else "未知",
            "server_ip": device.slave_server.ip_address if device.slave_server else "",
            "server_port": device.slave_server.port if device.slave_server else None,
            "server_status": device.slave_server.status if device.slave_server else "offline",
            
            # 时间信息
            "created_at": device.created_at,
            "updated_at": device.updated_at,
            "last_connected": device.last_connected,
            "last_connected_user": device.last_connected_user,
            "connection_count": device.connection_count or 0,
            "total_usage_time": device.total_usage_time or 0,
            
            # 占用信息
            "occupied_start_time": device.occupied_start_time,
            "occupied_duration": device.occupied_duration or 0,
            "estimated_end_time": device.estimated_end_time,
            "occupation_note": device.occupation_note,
            "current_user_name": current_occupation.user.username if current_occupation else None,
            "current_user_contact": current_occupation.user_contact if current_occupation else None,
            
            # 设备属性
            "is_real_hardware": device.is_real_hardware if device.is_real_hardware is not None else True,
            "auto_bind_eligible": device.auto_bind_eligible if device.auto_bind_eligible is not None else True
        }
        
        return DeviceResponse(**device_data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取设备详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取设备详情失败"
        )


# ==================== 设备更新和删除 ====================

@router.put("/{device_id}", response_model=DeviceResponse)
async def update_device(
    device_id: int,
    update_data: DeviceUpdateRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新设备信息"""
    try:
        # 查询设备
        query = select(Device).options(
            selectinload(Device.slave_server),
            selectinload(Device.occupation_records).selectinload(DeviceOccupationRecord.user)
        ).where(Device.id == device_id)

        result = await db.execute(query)
        device = result.scalar_one_or_none()

        if not device:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="设备不存在"
            )

        # 更新设备信息
        update_fields = update_data.dict(exclude_unset=True)
        for field, value in update_fields.items():
            setattr(device, field, value)

        device.updated_at = datetime.now()

        await db.commit()
        await db.refresh(device)

        # 触发设备更新事件
        await trigger_device_event(
            "device_updated",
            device_id=device.id,
            updated_by=current_user.id,
            updated_fields=list(update_fields.keys())
        )

        # 返回更新后的设备信息
        return await get_device_detail(device_id, db, current_user)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新设备失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新设备失败"
        )


@router.delete("/{device_id}")
async def delete_device(
    device_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除设备"""
    try:
        # 查询设备
        query = select(Device).where(Device.id == device_id)
        result = await db.execute(query)
        device = result.scalar_one_or_none()

        if not device:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="设备不存在"
            )

        # 检查设备是否被占用
        if device.status == "occupied":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="设备正在被使用，无法删除"
            )

        # 删除设备
        await db.delete(device)
        await db.commit()

        # 触发设备删除事件
        await trigger_device_event(
            "device_deleted",
            device_id=device.id,
            device_name=device.device_name,
            deleted_by=current_user.id
        )

        return {"status": "success", "message": "设备删除成功"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除设备失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除设备失败"
        )


# ==================== 设备占用管理 ====================

@router.post("/{device_id}/occupy")
async def occupy_device(
    device_id: int,
    occupation_data: DeviceOccupationRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """占用设备"""
    try:
        # 查询设备
        query = select(Device).where(Device.id == device_id)
        result = await db.execute(query)
        device = result.scalar_one_or_none()

        if not device:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="设备不存在"
            )

        # 检查设备状态
        if device.status == "occupied":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="设备已被占用"
            )

        if device.status not in ["idle", "online"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="设备状态不允许占用"
            )

        # 创建占用记录
        occupation_record = DeviceOccupationRecord(
            device_id=device_id,
            user_id=current_user.id,
            estimated_duration=occupation_data.estimated_duration,
            note=occupation_data.note,
            user_contact=occupation_data.user_contact or current_user.email,
            status="active"
        )

        # 更新设备状态
        device.status = "occupied"
        device.occupied_start_time = datetime.now()
        device.occupied_duration = 0
        device.occupation_note = occupation_data.note

        if occupation_data.estimated_duration:
            from datetime import timedelta
            device.estimated_end_time = datetime.now() + timedelta(seconds=occupation_data.estimated_duration)

        device.last_connected = datetime.now()
        device.last_connected_user = current_user.username
        device.connection_count = (device.connection_count or 0) + 1
        device.updated_at = datetime.now()

        db.add(occupation_record)
        await db.commit()

        # 触发设备占用事件
        await trigger_device_event(
            "device_occupied",
            device_id=device.id,
            user_id=current_user.id,
            user_name=current_user.username,
            estimated_duration=occupation_data.estimated_duration
        )

        return {"status": "success", "message": "设备占用成功"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"占用设备失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="占用设备失败"
        )


@router.post("/{device_id}/release")
async def release_device(
    device_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """释放设备"""
    try:
        # 查询设备和当前占用记录
        device_query = select(Device).where(Device.id == device_id)
        device_result = await db.execute(device_query)
        device = device_result.scalar_one_or_none()

        if not device:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="设备不存在"
            )

        # 查询活跃的占用记录
        occupation_query = select(DeviceOccupationRecord).where(
            and_(
                DeviceOccupationRecord.device_id == device_id,
                DeviceOccupationRecord.status == "active"
            )
        )
        occupation_result = await db.execute(occupation_query)
        occupation_record = occupation_result.scalar_one_or_none()

        if not occupation_record:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="设备未被占用"
            )

        # 检查权限（只有占用者或管理员可以释放）
        if (occupation_record.user_id != current_user.id and
            current_user.role not in ["admin", "super_admin", "global_admin"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权释放此设备"
            )

        # 计算实际使用时长
        now = datetime.now()
        actual_duration = int((now - occupation_record.start_time).total_seconds())

        # 更新占用记录
        occupation_record.end_time = now
        occupation_record.actual_duration = actual_duration
        occupation_record.status = "released"
        occupation_record.release_reason = "manual_release"

        # 更新设备状态
        device.status = "idle"
        device.occupied_start_time = None
        device.occupied_duration = 0
        device.estimated_end_time = None
        device.occupation_note = None
        device.total_usage_time = (device.total_usage_time or 0) + actual_duration
        device.updated_at = now

        await db.commit()

        # 触发设备释放事件
        await trigger_device_event(
            "device_released",
            device_id=device.id,
            user_id=current_user.id,
            duration=actual_duration
        )

        return {"status": "success", "message": "设备释放成功"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"释放设备失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="释放设备失败"
        )


@router.get("/{device_id}/occupation")
async def get_device_occupation(
    device_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取设备占用信息"""
    try:
        # 查询活跃的占用记录
        query = select(DeviceOccupationRecord).options(
            selectinload(DeviceOccupationRecord.user),
            selectinload(DeviceOccupationRecord.device)
        ).where(
            and_(
                DeviceOccupationRecord.device_id == device_id,
                DeviceOccupationRecord.status == "active"
            )
        )

        result = await db.execute(query)
        occupation_record = result.scalar_one_or_none()

        if not occupation_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="设备未被占用"
            )

        # 计算当前占用时长
        now = datetime.now()
        current_duration = int((now - occupation_record.start_time).total_seconds())

        return {
            "device_id": device_id,
            "device_name": occupation_record.device.device_name,
            "user_id": occupation_record.user_id,
            "user_name": occupation_record.user.username,
            "user_email": occupation_record.user.email,
            "user_contact": occupation_record.user_contact,
            "start_time": occupation_record.start_time.isoformat(),
            "current_duration": current_duration,
            "estimated_duration": occupation_record.estimated_duration,
            "estimated_end_time": occupation_record.start_time + timedelta(seconds=occupation_record.estimated_duration) if occupation_record.estimated_duration else None,
            "note": occupation_record.note,
            "occupation_type": occupation_record.occupation_type,
            "created_at": occupation_record.created_at.isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取设备占用信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取设备占用信息失败"
        )


@router.post("/{device_id}/release-request")
async def request_device_release(
    device_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """请求释放设备"""
    try:
        # 查询设备和占用记录
        device_query = select(Device).where(Device.id == device_id)
        device_result = await db.execute(device_query)
        device = device_result.scalar_one_or_none()

        if not device:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="设备不存在"
            )

        # 查询活跃的占用记录
        occupation_query = select(DeviceOccupationRecord).options(
            selectinload(DeviceOccupationRecord.user)
        ).where(
            and_(
                DeviceOccupationRecord.device_id == device_id,
                DeviceOccupationRecord.status == "active"
            )
        )
        occupation_result = await db.execute(occupation_query)
        occupation_record = occupation_result.scalar_one_or_none()

        if not occupation_record:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="设备未被占用"
            )

        # 不能向自己发送释放请求
        if occupation_record.user_id == current_user.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能向自己发送释放请求"
            )

        # 触发释放请求事件（通过WebSocket通知占用者）
        await trigger_device_event(
            "device_release_requested",
            device_id=device.id,
            device_name=device.device_name,
            requester_id=current_user.id,
            requester_name=current_user.username,
            occupier_id=occupation_record.user_id,
            occupier_name=occupation_record.user.username
        )

        return {
            "status": "success",
            "message": f"已向 {occupation_record.user.username} 发送设备释放请求"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"请求释放设备失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="请求释放设备失败"
        )


# ==================== 设备统计信息 ====================

@router.get("/stats/summary")
async def get_device_stats(
    mode: str = Query("hybrid", description="数据模式: realtime, database, hybrid"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取设备统计信息（智能融合实时数据和数据库数据）"""
    try:
        current_time = datetime.now()
        heartbeat_timeout = timedelta(seconds=60)

        # 获取从服务器实时状态
        slave_servers_query = select(SlaveServer).where(SlaveServer.is_active == True)
        slave_servers_result = await db.execute(slave_servers_query)
        slave_servers = slave_servers_result.scalars().all()

        # 计算实时统计数据
        realtime_stats = {
            "total_devices": 0,
            "online_devices": 0,
            "hardware_devices": 0,
            "occupied_devices": 0,
            "device_types": {},
            "online_servers": 0,
            "total_servers": len(slave_servers),
            "data_freshness": "realtime",
            "last_update": current_time.isoformat()
        }

        # 遍历从服务器，获取实时数据
        for server in slave_servers:
            # 判断服务器在线状态
            is_online = False
            if server.last_heartbeat:
                time_since_heartbeat = current_time - server.last_heartbeat
                is_online = time_since_heartbeat <= heartbeat_timeout

            if is_online:
                realtime_stats["online_servers"] += 1
                # 使用心跳中的设备统计数据
                realtime_stats["total_devices"] += server.device_count or 0
                # TODO: 从心跳数据中获取更详细的统计信息

        # 如果是纯实时模式，直接返回实时数据
        if mode == "realtime":
            return realtime_stats

        # 获取数据库统计数据
        database_stats = await _get_database_stats(db)

        # 如果是纯数据库模式，返回数据库数据
        if mode == "database":
            database_stats["data_freshness"] = "database"
            database_stats["last_update"] = current_time.isoformat()
            return database_stats

        # 混合模式：智能融合实时数据和数据库数据
        hybrid_stats = {
            "total_devices": realtime_stats["total_devices"],  # 优先使用实时数据
            "online_devices": realtime_stats["online_devices"],  # 优先使用实时数据
            "hardware_devices": database_stats["hardware_devices"],  # 使用数据库配置数据
            "occupied_devices": database_stats["occupied_devices"],  # 使用数据库状态数据
            "device_types": database_stats["device_types"],  # 使用数据库分类数据
            "online_servers": realtime_stats["online_servers"],
            "total_servers": realtime_stats["total_servers"],
            "data_freshness": "hybrid",
            "last_update": current_time.isoformat(),
            "data_sources": {
                "realtime": ["total_devices", "online_devices", "online_servers"],
                "database": ["hardware_devices", "occupied_devices", "device_types"]
            }
        }

        return hybrid_stats

    except Exception as e:
        logger.error(f"获取设备统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取设备统计失败"
        )

async def _get_database_stats(db: AsyncSession) -> dict:
    """获取数据库统计数据"""
    # 总设备数
    total_query = select(func.count(Device.id))
    total_result = await db.execute(total_query)
    total_devices = total_result.scalar()

    # 在线设备数
    online_query = select(func.count(Device.id)).where(Device.status.in_(["idle", "occupied"]))
    online_result = await db.execute(online_query)
    online_devices = online_result.scalar()

    # 真实硬件设备数
    hardware_query = select(func.count(Device.id)).where(Device.is_real_hardware == True)
    hardware_result = await db.execute(hardware_query)
    hardware_devices = hardware_result.scalar()

    # 被占用设备数
    occupied_query = select(func.count(Device.id)).where(Device.status == "occupied")
    occupied_result = await db.execute(occupied_query)
    occupied_devices = occupied_result.scalar()

    # 按类型统计
    type_query = select(Device.device_type, func.count(Device.id)).group_by(Device.device_type)
    type_result = await db.execute(type_query)
    device_types = dict(type_result.all())

    return {
        "total_devices": total_devices,
        "online_devices": online_devices,
        "hardware_devices": hardware_devices,
        "occupied_devices": occupied_devices,
        "device_types": device_types
    }


@router.get("/stats/realtime")
async def get_realtime_stats(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取纯实时设备统计信息"""
    try:
        current_time = datetime.now()
        heartbeat_timeout = timedelta(seconds=60)

        # 获取从服务器实时状态
        slave_servers_query = select(SlaveServer).where(SlaveServer.is_active == True)
        slave_servers_result = await db.execute(slave_servers_query)
        slave_servers = slave_servers_result.scalars().all()

        realtime_stats = {
            "total_devices": 0,
            "available_devices": 0,
            "online_servers": 0,
            "total_servers": len(slave_servers),
            "server_details": [],
            "data_freshness": "realtime",
            "last_update": current_time.isoformat(),
            "update_interval": "30s"
        }

        # 遍历从服务器，收集实时数据
        for server in slave_servers:
            # 判断服务器在线状态
            is_online = False
            time_since_heartbeat = None
            if server.last_heartbeat:
                time_since_heartbeat = current_time - server.last_heartbeat
                is_online = time_since_heartbeat <= heartbeat_timeout

            server_detail = {
                "server_id": server.id,
                "server_name": server.name,
                "ip_address": server.ip_address,
                "status": "online" if is_online else "offline",
                "device_count": server.device_count or 0,
                "last_heartbeat": server.last_heartbeat.isoformat() if server.last_heartbeat else None,
                "heartbeat_age_seconds": int(time_since_heartbeat.total_seconds()) if time_since_heartbeat else None
            }

            if is_online:
                realtime_stats["online_servers"] += 1
                realtime_stats["total_devices"] += server.device_count or 0
                # TODO: 从心跳数据中获取可用设备数量
                realtime_stats["available_devices"] += server.device_count or 0

            realtime_stats["server_details"].append(server_detail)

        return realtime_stats

    except Exception as e:
        logger.error(f"获取实时统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取实时统计失败"
        )


@router.post("/batch-reclassify-types")
async def batch_reclassify_device_types(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """批量重新分类所有设备类型"""
    try:
        # 检查权限（只有管理员可以执行）
        # permission_level: 0=全域管理员，1=超级管理员，2=管理员
        if current_user.permission_level > 2:
            raise HTTPException(status_code=403, detail="权限不足，只有管理员及以上权限可以执行此操作")

        # 执行批量更新
        stats = await batch_update_device_types(db)

        return {
            "message": "设备类型批量重新分类完成",
            "statistics": stats
        }

    except Exception as e:
        logger.error(f"批量重新分类设备类型失败: {e}")
        raise HTTPException(status_code=500, detail=f"批量重新分类失败: {str(e)}")


@router.post("/reclassify-type/{device_id}")
async def reclassify_device_type(
    device_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """重新分类单个设备类型"""
    try:
        # 检查权限
        if current_user.role not in ["admin", "super_admin", "global_admin"]:
            raise HTTPException(status_code=403, detail="权限不足")

        # 获取设备
        result = await db.execute(
            select(Device).options(selectinload(Device.slave_server)).where(Device.id == device_id)
        )
        device = result.scalar_one_or_none()

        if not device:
            raise HTTPException(status_code=404, detail="设备不存在")

        # 重新分类
        await update_device_type_classification(db, device)

        return {
            "message": "设备类型重新分类完成",
            "device_id": device_id,
            "final_device_type": device.final_device_type,
            "master_determined_type": device.master_determined_type
        }

    except Exception as e:
        logger.error(f"重新分类设备类型失败: {e}")
        raise HTTPException(status_code=500, detail=f"重新分类失败: {str(e)}")


@router.put("/manual-type/{device_id}")
async def set_manual_device_type(
    device_id: int,
    manual_type: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """手动设置设备类型"""
    try:
        # 检查权限
        if current_user.role not in ["admin", "super_admin", "global_admin"]:
            raise HTTPException(status_code=403, detail="权限不足")

        # 获取设备
        result = await db.execute(
            select(Device).where(Device.id == device_id)
        )
        device = result.scalar_one_or_none()

        if not device:
            raise HTTPException(status_code=404, detail="设备不存在")

        # 设置手动类型
        device.manual_override_type = manual_type
        device.type_determination_source = 'manual'
        device.type_determination_time = datetime.now()

        # 更新最终设备类型
        device.final_device_type = manual_type

        await db.commit()

        return {
            "message": "设备类型手动设置完成",
            "device_id": device_id,
            "manual_override_type": manual_type,
            "final_device_type": device.final_device_type
        }

    except Exception as e:
        logger.error(f"手动设置设备类型失败: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"手动设置失败: {str(e)}")


@router.post("/{device_id}/force-disconnect")
async def force_disconnect_device_connection(
    device_id: int,
    request: DeviceForceDisconnectRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    强制断开设备连接
    需要device.force_disconnect权限
    """
    try:
        # 检查权限
        if not await check_user_permission(db, current_user.id, "device.force_disconnect"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有强制断开设备的权限"
            )

        # 获取设备信息
        device_query = select(Device).where(Device.id == device_id)
        device_result = await db.execute(device_query)
        device = device_result.scalar_one_or_none()

        if not device:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="设备不存在"
            )

        # 检查设备是否被占用
        occupation_query = select(DeviceOccupationRecord).where(
            and_(
                DeviceOccupationRecord.device_id == device_id,
                DeviceOccupationRecord.end_time.is_(None)
            )
        ).options(selectinload(DeviceOccupationRecord.user))

        occupation_result = await db.execute(occupation_query)
        current_occupation = occupation_result.scalar_one_or_none()

        if not current_occupation:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="设备当前未被占用"
            )

        # 验证权限范围：只能断开所在层级内的设备
        device_access_allowed = await validate_device_access_permission(db, current_user, device)
        if not device_access_allowed:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限操作此设备"
            )

        # 记录断开原因和操作者
        current_occupation.end_time = datetime.now()
        current_occupation.force_disconnected_by = current_user.id
        current_occupation.disconnect_reason = request.reason or "管理员强制断开"

        # 触发设备状态变更事件
        await trigger_device_event(
            "device_force_disconnected",
            {
                "device_id": device_id,
                "device_name": device.custom_name or device.device_name,
                "disconnected_user": current_occupation.user.username,
                "operator": current_user.username,
                "reason": request.reason,
                "timestamp": datetime.now().isoformat()
            }
        )

        await db.commit()

        logger.info(f"用户 {current_user.username} 强制断开设备 {device_id} 的连接")

        return {
            "message": "设备连接已强制断开",
            "device_id": device_id,
            "disconnected_user": current_occupation.user.username,
            "operator": current_user.username,
            "reason": request.reason,
            "disconnected_at": current_occupation.end_time.isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"强制断开设备连接失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"强制断开设备连接失败: {str(e)}"
        )


async def validate_device_access_permission(db: AsyncSession, user: User, device: Device) -> bool:
    """
    验证用户是否有权限访问指定设备
    基于用户的角色层级和设备分配情况
    """
    try:
        # 全域管理员和超级管理员有权限访问所有设备
        if user.role_id in [1, 2]:  # 全域管理员、超级管理员
            return True

        # 管理员只能访问所在层级内的设备
        if user.role_id == 3:  # 管理员
            # 检查设备是否在用户的组织层级内
            # 这里需要根据具体的组织层级逻辑来实现
            return True  # 暂时允许，后续完善层级检查逻辑

        # 普通用户和新用户没有强制断开权限
        return False

    except Exception as e:
        logger.error(f"验证设备访问权限失败: {str(e)}")
        return False
