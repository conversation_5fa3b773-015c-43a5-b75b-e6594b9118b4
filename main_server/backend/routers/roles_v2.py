#!/usr/bin/env python3
"""
角色管理路由 V2 - 集成分层权限控制
实现基于角色的角色数据访问控制和全域管理员角色隐藏
"""
from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_
from typing import List, Optional
from pydantic import BaseModel, Field
from datetime import datetime
import logging

from database import get_db
from models import Role, User
from auth_utils import get_current_user
from permissions import (
    PermissionChecker, DataFilter, require_super_admin_or_above, 
    require_global_admin, PermissionLevel
)
from audit_logger import AuditLogger

router = APIRouter(prefix="/api/v2/roles", tags=["角色管理V2"])
logger = logging.getLogger(__name__)

# 受保护的角色，不可修改或删除
PROTECTED_ROLES = ["超级管理员", "全域管理员", "管理员", "普通用户", "新用户"]

class RoleResponse(BaseModel):
    """角色响应模型"""
    id: int
    name: str
    description: str
    permissions: List[str] = []
    navigation_permissions: List[str] = []
    device_sub_permissions: List[str] = []
    level_scope: int
    can_manage_users: bool
    can_manage_devices: bool
    can_view_reports: bool
    is_system_role: bool
    is_active: bool
    is_protected: bool = False
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class RoleListResponse(BaseModel):
    """角色列表响应模型"""
    roles: List[RoleResponse]
    total: int
    filtered_by_permission: bool = False

class RoleCreate(BaseModel):
    """创建角色请求模型"""
    name: str = Field(..., min_length=2, max_length=50, description="角色名称")
    description: str = Field(..., min_length=1, max_length=500, description="角色描述")
    permissions: List[str] = Field(default=[], description="权限列表")
    navigation_permissions: List[str] = Field(default=[], description="导航权限列表")
    device_sub_permissions: List[str] = Field(default=[], description="设备子权限列表")
    level_scope: int = Field(default=4, ge=0, le=5, description="权限层级范围")
    can_manage_users: bool = Field(default=False, description="是否可以管理用户")
    can_manage_devices: bool = Field(default=False, description="是否可以管理设备")
    can_view_reports: bool = Field(default=False, description="是否可以查看报告")

class RoleUpdate(BaseModel):
    """更新角色请求模型"""
    name: Optional[str] = Field(None, min_length=2, max_length=50, description="角色名称")
    description: Optional[str] = Field(None, min_length=1, max_length=500, description="角色描述")
    permissions: Optional[List[str]] = Field(None, description="权限列表")
    navigation_permissions: Optional[List[str]] = Field(None, description="导航权限列表")
    device_sub_permissions: Optional[List[str]] = Field(None, description="设备子权限列表")
    level_scope: Optional[int] = Field(None, ge=0, le=5, description="权限层级范围")
    can_manage_users: Optional[bool] = Field(None, description="是否可以管理用户")
    can_manage_devices: Optional[bool] = Field(None, description="是否可以管理设备")
    can_view_reports: Optional[bool] = Field(None, description="是否可以查看报告")
    is_active: Optional[bool] = Field(None, description="是否启用")
    is_active: Optional[bool] = Field(None, description="是否启用")

@router.get("/", response_model=RoleListResponse)
async def get_roles(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取角色列表 - 基于权限过滤
    """
    try:
        # 检查基本权限
        if not (PermissionChecker.is_global_admin(current_user) or 
               PermissionChecker.is_super_admin(current_user)):
            await AuditLogger.log_unauthorized_access(
                current_user, "access_role_management", "roles", request
            )
            raise HTTPException(status_code=403, detail="仅超级管理员及以上可访问角色管理")
        
        # 构建查询
        query = select(Role).order_by(Role.id)
        
        # 应用权限过滤
        original_query = query
        query = DataFilter.filter_roles_query(query, current_user)
        
        # 检查是否被权限过滤
        filtered_by_permission = str(query) != str(original_query)
        
        # 执行查询
        result = await db.execute(query)
        roles = result.scalars().all()
        
        # 记录访问日志
        await AuditLogger.log_role_access(
            user=current_user,
            accessed_roles=roles,
            request=request
        )
        
        # 转换为响应模型
        role_responses = []
        for role in roles:
            role_dict = {
                "id": role.id,
                "name": role.name,
                "description": role.description,
                "permissions": role.permissions or [],
                "level_scope": role.level_scope or 0,
                "can_manage_users": role.can_manage_users or False,
                "can_manage_devices": role.can_manage_devices or False,
                "can_view_reports": role.can_view_reports or False,
                "is_system_role": role.is_system_role or False,
                "is_active": role.is_active,
                "is_protected": getattr(role, 'is_protected', False) or role.name in PROTECTED_ROLES,
                "created_at": role.created_at,
                "updated_at": role.updated_at
            }
            role_responses.append(RoleResponse(**role_dict))
        
        return RoleListResponse(
            roles=role_responses,
            total=len(role_responses),
            filtered_by_permission=filtered_by_permission
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取角色列表失败: {e}")
        await AuditLogger.log_role_management_access(
            user=current_user,
            action="list",
            success=False,
            request=request
        )
        raise HTTPException(status_code=500, detail="获取角色列表失败")

@router.get("/{role_id}", response_model=RoleResponse)
async def get_role(
    role_id: int,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取单个角色信息 - 基于权限控制
    """
    try:
        # 检查基本权限
        if not (PermissionChecker.is_global_admin(current_user) or 
               PermissionChecker.is_super_admin(current_user)):
            await AuditLogger.log_unauthorized_access(
                current_user, "access_role_detail", f"role_{role_id}", request
            )
            raise HTTPException(status_code=403, detail="仅超级管理员及以上可访问角色管理")
        
        # 查询角色
        result = await db.execute(select(Role).filter(Role.id == role_id))
        role = result.scalar_one_or_none()
        
        if not role:
            raise HTTPException(status_code=404, detail="角色不存在")
        
        # 检查是否为受限角色
        if (role.name == "全域管理员" and 
            not PermissionChecker.is_global_admin(current_user)):
            await AuditLogger.log_unauthorized_access(
                current_user, "access_global_admin_role", f"role_{role_id}", request
            )
            raise HTTPException(status_code=403, detail="无权限访问该角色")
        
        # 记录访问日志
        await AuditLogger.log_role_management_access(
            user=current_user,
            action="detail",
            role_name=role.name,
            success=True,
            request=request
        )
        
        # 构建响应
        role_dict = {
            "id": role.id,
            "name": role.name,
            "description": role.description,
            "permissions": role.permissions or [],
            "navigation_permissions": getattr(role, 'navigation_permissions', []) or [],
            "device_sub_permissions": getattr(role, 'device_sub_permissions', []) or [],
            "level_scope": role.level_scope or 0,
            "can_manage_users": role.can_manage_users or False,
            "can_manage_devices": role.can_manage_devices or False,
            "can_view_reports": role.can_view_reports or False,
            "is_system_role": role.is_system_role or False,
            "is_active": role.is_active,
            "is_protected": getattr(role, 'is_protected', False) or role.name in PROTECTED_ROLES,
            "created_at": role.created_at,
            "updated_at": role.updated_at
        }
        
        return RoleResponse(**role_dict)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取角色信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取角色信息失败")

@router.post("/", response_model=RoleResponse)
async def create_role(
    role_data: RoleCreate,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    创建新角色 - 仅超级管理员及以上
    """
    try:
        # 检查权限
        if not (PermissionChecker.is_global_admin(current_user) or 
               PermissionChecker.is_super_admin(current_user)):
            await AuditLogger.log_unauthorized_access(
                current_user, "create_role", "roles", request
            )
            raise HTTPException(status_code=403, detail="仅超级管理员及以上可创建角色")
        
        # 检查角色名是否已存在
        existing_role = await db.execute(select(Role).filter(Role.name == role_data.name))
        if existing_role.scalar_one_or_none():
            raise HTTPException(status_code=400, detail="角色名已存在")
        
        # 检查是否尝试创建受保护的角色名
        if role_data.name in PROTECTED_ROLES:
            await AuditLogger.log_unauthorized_access(
                current_user, "create_protected_role", role_data.name, request
            )
            raise HTTPException(status_code=400, detail="不能创建系统保护角色")
        
        # 创建角色
        new_role = Role(
            name=role_data.name,
            description=role_data.description,
            permissions=role_data.permissions,
            navigation_permissions=role_data.navigation_permissions,
            device_sub_permissions=role_data.device_sub_permissions,
            level_scope=role_data.level_scope,
            can_manage_users=role_data.can_manage_users,
            can_manage_devices=role_data.can_manage_devices,
            can_view_reports=role_data.can_view_reports,
            is_system_role=False,
            is_active=True,
            is_protected=False
        )
        
        db.add(new_role)
        await db.commit()
        await db.refresh(new_role)
        
        # 记录操作日志
        await AuditLogger.log_role_management_access(
            user=current_user,
            action="create",
            role_name=new_role.name,
            success=True,
            request=request
        )
        
        # 返回创建的角色
        return await get_role(new_role.id, request, current_user, db)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建角色失败: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="创建角色失败")

@router.put("/{role_id}", response_model=RoleResponse)
async def update_role(
    role_id: int,
    role_data: RoleUpdate,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    更新角色 - 基于权限控制
    """
    try:
        # 检查基本权限
        if not (PermissionChecker.is_global_admin(current_user) or 
               PermissionChecker.is_super_admin(current_user)):
            await AuditLogger.log_unauthorized_access(
                current_user, "update_role", f"role_{role_id}", request
            )
            raise HTTPException(status_code=403, detail="仅超级管理员及以上可修改角色")
        
        # 查询角色
        result = await db.execute(select(Role).filter(Role.id == role_id))
        role = result.scalar_one_or_none()
        
        if not role:
            raise HTTPException(status_code=404, detail="角色不存在")
        
        # 检查是否为受保护角色
        if role.name in PROTECTED_ROLES:
            # 全域管理员角色只有firefly可以修改
            if role.name == "全域管理员" and not PermissionChecker.is_global_admin(current_user):
                await AuditLogger.log_unauthorized_access(
                    current_user, "update_global_admin_role", f"role_{role_id}", request
                )
                raise HTTPException(status_code=403, detail="仅全域管理员可修改此角色")
            
            # 其他受保护角色的修改限制
            if role.name != "全域管理员" and getattr(role, 'is_protected', False):
                await AuditLogger.log_unauthorized_access(
                    current_user, "update_protected_role", f"role_{role_id}", request
                )
                raise HTTPException(status_code=403, detail="此角色受保护，不可修改")
        
        # 应用更新
        update_fields = {}
        if role_data.name is not None and role_data.name != role.name:
            # 检查新名称是否已存在
            existing_role = await db.execute(select(Role).filter(Role.name == role_data.name))
            if existing_role.scalar_one_or_none():
                raise HTTPException(status_code=400, detail="角色名已存在")
            update_fields["name"] = role_data.name
        
        if role_data.description is not None:
            update_fields["description"] = role_data.description
        if role_data.permissions is not None:
            update_fields["permissions"] = role_data.permissions
        if role_data.navigation_permissions is not None:
            update_fields["navigation_permissions"] = role_data.navigation_permissions
        if role_data.device_sub_permissions is not None:
            update_fields["device_sub_permissions"] = role_data.device_sub_permissions
        if role_data.level_scope is not None:
            update_fields["level_scope"] = role_data.level_scope
        if role_data.can_manage_users is not None:
            update_fields["can_manage_users"] = role_data.can_manage_users
        if role_data.can_manage_devices is not None:
            update_fields["can_manage_devices"] = role_data.can_manage_devices
        if role_data.can_view_reports is not None:
            update_fields["can_view_reports"] = role_data.can_view_reports
        if role_data.is_active is not None:
            update_fields["is_active"] = role_data.is_active
        
        # 更新角色
        for field, value in update_fields.items():
            setattr(role, field, value)
        
        await db.commit()
        await db.refresh(role)
        
        # 记录操作日志
        await AuditLogger.log_role_management_access(
            user=current_user,
            action="update",
            role_name=role.name,
            success=True,
            request=request
        )
        
        # 返回更新后的角色
        return await get_role(role_id, request, current_user, db)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新角色失败: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="更新角色失败")
