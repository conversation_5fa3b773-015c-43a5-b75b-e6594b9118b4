import{_ as T}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css               */import{u as A,a as E,r as N,o as j,c,d as s,z as p,t as l,p as _,e as a,w as n,E as V,a5 as q,a6 as B,s as k,h as u,i as d,n as R,a7 as U,a8 as z}from"./index-BALd70Fs.js";import{a as y}from"./applicationRequests-D6lO1yYh.js";/* empty css                 */const H={class:"dashboard-container"},I={class:"page-header"},L={class:"welcome-text"},O={class:"stats-grid"},F={class:"stat-card"},G={class:"stat-icon pending"},J={class:"stat-content"},K={class:"stat-number"},Q={class:"stat-card"},W={class:"stat-icon approved"},X={class:"stat-content"},Y={class:"stat-number"},Z={class:"stat-card"},ss={class:"stat-icon users"},ts={class:"stat-content"},es={class:"stat-number"},as={class:"stat-card"},os={class:"stat-icon devices"},ns={class:"stat-content"},is={class:"stat-number"},cs={class:"quick-actions"},ls={class:"action-grid"},ds={key:0,class:"recent-applications"},rs={class:"application-list"},ps=["onClick"],_s={class:"app-info"},us={class:"app-title"},vs={class:"app-meta"},ms={class:"app-applicant"},hs={class:"app-time"},fs={class:"app-status"},gs={key:0,class:"empty-state"},ks={__name:"Dashboard",setup(ys){const r=A(),v=E({pending_applications:0,approved_applications:0,total_users:0,online_devices:0}),m=N([]),b=async()=>{try{const e=await y.getApplicationStats(),t=e.data||e;Object.assign(v,t)}catch{k.error("加载统计数据失败")}},C=async()=>{try{const e=await y.getApplicationRequests({page:1,size:5});m.value=e.data||[]}catch{k.error("加载最近申请失败")}},w=e=>z(e).format("MM-DD HH:mm"),$=e=>({pending:"warning",approved:"success",rejected:"danger",cancelled:"info"})[e]||"info",x=e=>({pending:"待处理",approved:"已批准",rejected:"已拒绝",cancelled:"已取消"})[e]||e;return j(()=>{b(),r.hasPermission("application.view")&&C()}),(e,t)=>{const D=u("Clock"),i=V,P=u("Check"),h=u("User"),f=u("Monitor"),S=u("Plus"),g=u("Document"),M=U;return d(),c("div",H,[s("div",I,[t[4]||(t[4]=s("h2",{class:"page-title"},"工作台",-1)),s("p",L,"欢迎使用 OmniLink 全联通系统，"+l(_(r).userName)+"！",1)]),s("div",O,[s("div",F,[s("div",G,[a(i,null,{default:n(()=>[a(D)]),_:1})]),s("div",J,[s("div",K,l(v.pending_applications),1),t[5]||(t[5]=s("div",{class:"stat-label"},"待处理申请",-1))])]),s("div",Q,[s("div",W,[a(i,null,{default:n(()=>[a(P)]),_:1})]),s("div",X,[s("div",Y,l(v.approved_applications),1),t[6]||(t[6]=s("div",{class:"stat-label"},"已批准申请",-1))])]),s("div",Z,[s("div",ss,[a(i,null,{default:n(()=>[a(h)]),_:1})]),s("div",ts,[s("div",es,l(v.total_users),1),t[7]||(t[7]=s("div",{class:"stat-label"},"系统用户",-1))])]),s("div",as,[s("div",os,[a(i,null,{default:n(()=>[a(f)]),_:1})]),s("div",ns,[s("div",is,l(v.online_devices),1),t[8]||(t[8]=s("div",{class:"stat-label"},"在线设备",-1))])])]),s("div",cs,[t[13]||(t[13]=s("h3",{class:"section-title"},"快速操作",-1)),s("div",ls,[_(r).hasPermission("application.submit")?(d(),c("div",{key:0,class:"action-card",onClick:t[0]||(t[0]=o=>e.$router.push("/applications/create"))},[a(i,{class:"action-icon"},{default:n(()=>[a(S)]),_:1}),t[9]||(t[9]=s("span",{class:"action-text"},"创建申请",-1))])):p("",!0),_(r).hasPermission("application.view")?(d(),c("div",{key:1,class:"action-card",onClick:t[1]||(t[1]=o=>e.$router.push("/applications"))},[a(i,{class:"action-icon"},{default:n(()=>[a(g)]),_:1}),t[10]||(t[10]=s("span",{class:"action-text"},"处理事项",-1))])):p("",!0),_(r).hasPermission("user.view")?(d(),c("div",{key:2,class:"action-card",onClick:t[2]||(t[2]=o=>e.$router.push("/users"))},[a(i,{class:"action-icon"},{default:n(()=>[a(h)]),_:1}),t[11]||(t[11]=s("span",{class:"action-text"},"用户管理",-1))])):p("",!0),_(r).hasPermission("device.view")?(d(),c("div",{key:3,class:"action-card",onClick:t[3]||(t[3]=o=>e.$router.push("/devices"))},[a(i,{class:"action-icon"},{default:n(()=>[a(f)]),_:1}),t[12]||(t[12]=s("span",{class:"action-text"},"设备管理",-1))])):p("",!0)])]),_(r).hasPermission("application.view")?(d(),c("div",ds,[t[15]||(t[15]=s("h3",{class:"section-title"},"最近申请",-1)),s("div",rs,[(d(!0),c(q,null,B(m.value,o=>(d(),c("div",{key:o.id,class:"application-item",onClick:bs=>e.$router.push(`/applications/${o.id}`)},[s("div",_s,[s("div",us,l(o.title),1),s("div",vs,[s("span",ms,l(o.applicant_name),1),s("span",hs,l(w(o.created_at)),1)])]),s("div",fs,[a(M,{type:$(o.status)},{default:n(()=>[R(l(x(o.status)),1)]),_:2},1032,["type"])])],8,ps))),128)),m.value.length===0?(d(),c("div",gs,[a(i,{class:"empty-icon"},{default:n(()=>[a(g)]),_:1}),t[14]||(t[14]=s("p",{class:"empty-text"},"暂无申请记录",-1))])):p("",!0)])])):p("",!0)])}}},Ps=T(ks,[["__scopeId","data-v-607a45cc"]]);export{Ps as default};
