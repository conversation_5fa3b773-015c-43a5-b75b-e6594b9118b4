import{_ as A}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                   *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                     *//* empty css               *//* empty css                *//* empty css               *//* empty css                         *//* empty css                  */import{u as F,r as m,aN as G,o as J,c as b,d as l,e as t,w as s,m as O,db as Q,ai as W,dc as X,g as Z,s as y,i as d,n as i,E as ee,p as u,dd as te,cK as le,cL as se,t as o,a7 as ae,y as C,z as E,de as ne,c$ as $,df as oe,d6 as re,a9 as ie,ac as ue,af as de,aa as ve,cY as ce}from"./index-BGppeauV.js";import{a as _e,b as pe,c as fe,t as me}from"./slaveServers-BkzcLOUu.js";const ye={class:"slave-server-detail"},ge={class:"page-header"},be={key:0,class:"loading-container"},we={key:1,class:"detail-content"},ke={class:"card-header"},Se={class:"info-item"},Ce={class:"info-item"},Ee={class:"info-item"},De={class:"info-item"},Ie={class:"info-item"},Te={class:"info-item"},xe={class:"info-item"},Ve={class:"info-item"},$e={class:"info-item full-width"},Le={class:"control-buttons"},he={class:"card-header"},Be={key:0,class:"empty-devices"},Ne={key:2,class:"error-container"},ze={__name:"Detail",setup(Pe){const w=Z(),D=F(),I=m(!0),f=m(!1),T=m(!1),g=m(!1),n=m(null),_=m([]);G(()=>n.value?n.value.status==="online":!1);const x=async()=>{I.value=!0;try{const a=w.params.id,e=await _e(a);n.value=e.data}catch(a){y.error("获取从服务器信息失败"),console.error("获取从服务器信息失败:",a)}finally{I.value=!1}},L=async()=>{g.value=!0;try{const a=w.params.id,e=await pe(a);e.success&&e.data||e.data?_.value=e.data:_.value=[],console.log("设备列表获取成功:",_.value.length,"个设备")}catch(a){y.error("获取设备列表失败"),console.error("获取设备列表失败:",a),_.value=[]}finally{g.value=!1}},k=async a=>{f.value=!0;try{const e=w.params.id;await fe(e,a),y.success("控制命令已发送"),setTimeout(()=>{x()},2e3)}catch(e){y.error("控制命令发送失败"),console.error("控制从服务器失败:",e)}finally{f.value=!1}},z=async()=>{T.value=!0;try{const a=w.params.id;await me(a),y.success("连接测试成功")}catch(a){y.error("连接测试失败"),console.error("测试连接失败:",a)}finally{T.value=!1}},P=a=>{switch(a){case"online":return"success";case"offline":return"danger";default:return"warning"}},h=a=>{switch(a){case"online":return"在线";case"offline":return"离线";default:return"未知"}},H=a=>{switch(a){case"available":return"success";case"in_use":return"warning";case"error":return"danger";default:return"info"}},R=a=>{switch(a){case"available":return"可用";case"in_use":return"使用中";case"error":return"错误";default:return"未知"}},S=a=>a?new Date(a).toLocaleString():"无";return J(()=>{x(),L()}),(a,e)=>{const p=ee,v=O,M=Q,B=ae,N=se,K=le,V=W,c=de,U=ue,Y=ce,j=X,q=ve;return d(),b("div",ye,[l("div",ge,[t(v,{onClick:e[0]||(e[0]=r=>a.$router.back()),type:"text"},{default:s(()=>[t(p,null,{default:s(()=>[t(u(te))]),_:1}),e[5]||(e[5]=i(" 返回列表 ",-1))]),_:1,__:[5]}),e[6]||(e[6]=l("h2",null,"从服务器详情",-1))]),I.value?(d(),b("div",be,[t(M,{rows:8,animated:""})])):n.value?(d(),b("div",we,[t(V,{class:"info-card"},{header:s(()=>[l("div",ke,[e[7]||(e[7]=l("span",null,"基本信息",-1)),t(B,{type:P(n.value.status),size:"large"},{default:s(()=>[i(o(h(n.value.status)),1)]),_:1},8,["type"])])]),default:s(()=>[t(K,{gutter:20},{default:s(()=>[t(N,{span:12},{default:s(()=>[l("div",Se,[e[8]||(e[8]=l("label",null,"服务器ID:",-1)),l("span",null,o(n.value.server_id),1)]),l("div",Ce,[e[9]||(e[9]=l("label",null,"名称:",-1)),l("span",null,o(n.value.name||"未设置"),1)]),l("div",Ee,[e[10]||(e[10]=l("label",null,"IP地址:",-1)),l("span",null,o(n.value.ip_address),1)]),l("div",De,[e[11]||(e[11]=l("label",null,"端口:",-1)),l("span",null,o(n.value.port),1)])]),_:1}),t(N,{span:12},{default:s(()=>[l("div",Ie,[e[12]||(e[12]=l("label",null,"状态:",-1)),l("span",null,o(h(n.value.status)),1)]),l("div",Te,[e[13]||(e[13]=l("label",null,"最后心跳:",-1)),l("span",null,o(S(n.value.last_seen)),1)]),l("div",xe,[e[14]||(e[14]=l("label",null,"创建时间:",-1)),l("span",null,o(S(n.value.created_at)),1)]),l("div",Ve,[e[15]||(e[15]=l("label",null,"更新时间:",-1)),l("span",null,o(S(n.value.updated_at)),1)])]),_:1})]),_:1}),l("div",$e,[e[16]||(e[16]=l("label",null,"描述:",-1)),l("span",null,o(n.value.description||"无描述"),1)])]),_:1}),t(V,{class:"control-card"},{header:s(()=>e[17]||(e[17]=[l("span",null,"控制面板",-1)])),default:s(()=>[l("div",Le,[u(D).hasPermission("slave.control")?(d(),C(v,{key:0,type:"success",onClick:e[1]||(e[1]=r=>k("start_vh")),loading:f.value},{default:s(()=>[t(p,null,{default:s(()=>[t(u(ne))]),_:1}),e[18]||(e[18]=i(" 启动VirtualHere ",-1))]),_:1,__:[18]},8,["loading"])):E("",!0),u(D).hasPermission("slave.control")?(d(),C(v,{key:1,type:"warning",onClick:e[2]||(e[2]=r=>k("restart_vh")),loading:f.value},{default:s(()=>[t(p,null,{default:s(()=>[t(u($))]),_:1}),e[19]||(e[19]=i(" 重启VirtualHere ",-1))]),_:1,__:[19]},8,["loading"])):E("",!0),u(D).hasPermission("slave.control")?(d(),C(v,{key:2,type:"danger",onClick:e[3]||(e[3]=r=>k("stop_vh")),loading:f.value},{default:s(()=>[t(p,null,{default:s(()=>[t(u(oe))]),_:1}),e[20]||(e[20]=i(" 停止VirtualHere ",-1))]),_:1,__:[20]},8,["loading"])):E("",!0),t(v,{type:"primary",onClick:z,loading:T.value},{default:s(()=>[t(p,null,{default:s(()=>[t(u(re))]),_:1}),e[21]||(e[21]=i(" 测试连接 ",-1))]),_:1,__:[21]},8,["loading"]),t(v,{type:"info",onClick:e[4]||(e[4]=r=>k("refresh")),loading:f.value},{default:s(()=>[t(p,null,{default:s(()=>[t(u($))]),_:1}),e[22]||(e[22]=i(" 强制刷新 ",-1))]),_:1,__:[22]},8,["loading"])])]),_:1}),t(V,{class:"devices-card"},{header:s(()=>[l("div",he,[e[24]||(e[24]=l("span",null,"设备列表",-1)),t(v,{type:"primary",size:"small",onClick:L,loading:g.value},{default:s(()=>[t(p,null,{default:s(()=>[t(u($))]),_:1}),e[23]||(e[23]=i(" 刷新 ",-1))]),_:1,__:[23]},8,["loading"])])]),default:s(()=>[ie((d(),C(U,{data:_.value,stripe:"",style:{width:"100%"}},{default:s(()=>[t(c,{prop:"id",label:"ID",width:"80"}),t(c,{prop:"device_name",label:"设备名称",width:"150"}),t(c,{prop:"device_type",label:"设备类型",width:"120"}),t(c,{label:"厂商ID",width:"100"},{default:s(({row:r})=>[l("code",null,o(r.vendor_id),1)]),_:1}),t(c,{label:"产品ID",width:"100"},{default:s(({row:r})=>[l("code",null,o(r.product_id),1)]),_:1}),t(c,{prop:"serial_number",label:"序列号",width:"150"}),t(c,{label:"状态",width:"100"},{default:s(({row:r})=>[t(B,{type:H(r.status),size:"small"},{default:s(()=>[i(o(R(r.status)),1)]),_:2},1032,["type"])]),_:1}),t(c,{label:"创建时间",width:"160"},{default:s(({row:r})=>[i(o(S(r.created_at)),1)]),_:1})]),_:1},8,["data"])),[[q,g.value]]),_.value.length===0&&!g.value?(d(),b("div",Be,[t(Y,{description:"暂无设备"})])):E("",!0)]),_:1})])):(d(),b("div",Ne,[t(j,{icon:"error",title:"加载失败","sub-title":"无法获取从服务器信息"},{extra:s(()=>[t(v,{type:"primary",onClick:x},{default:s(()=>e[25]||(e[25]=[i("重试",-1)])),_:1,__:[25]})]),_:1})]))])}}},Xe=A(ze,[["__scopeId","data-v-10468eb4"]]);export{Xe as default};
