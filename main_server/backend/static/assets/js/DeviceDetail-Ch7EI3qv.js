import{_ as ie}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                   *//* empty css                 *//* empty css               *//* empty css                *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                  *//* empty css                         */import{u as de,r as T,g as re,a as R,o as ce,bN as ue,c as u,d as s,ab as _e,e as t,w as n,m as pe,t as l,a7 as fe,M as me,ac as ve,y as p,z as g,cK as ye,s as M,b as ge,h as be,i as d,n as c,E as he,p as r,dd as ke,L,S as O,dx as we,c$ as xe,cV as Me,O as Ce,P as De,Q as Ee,cL as $e,ad as Ie,k as Ne,ai as Te,aj as Se,dn as Ae,d6 as Ve,d3 as Be,R as Ue,di as ze,dk as <PERSON>,cY as <PERSON>,a5 as Oe,a6 as Ge,d8 as G,$ as je,a4 as Fe}from"./index-BALd70Fs.js";const Pe={class:"device-detail"},qe={class:"detail-header"},He={class:"header-left"},Ke={class:"device-title"},Qe={class:"header-right"},Ye={class:"detail-content"},Je={class:"card-header"},We={class:"info-content"},Xe={class:"info-item"},Ze={class:"info-value"},es={class:"info-item"},ss={key:1,class:"info-value"},ts={key:0,class:"info-item"},ns={class:"info-value secondary"},os={class:"info-item"},as={class:"info-item"},ls={class:"info-item"},is={key:1,class:"info-value"},ds={class:"card-header"},rs={class:"info-content"},cs={class:"info-item"},us={class:"info-value code"},_s={class:"info-item"},ps={class:"info-value code"},fs={class:"info-item"},ms={class:"info-value code"},vs={class:"info-item"},ys={class:"info-value"},gs={class:"info-item"},bs={class:"info-value code"},hs={class:"info-item"},ks={class:"info-value code"},ws={key:0,class:"usb-ids-section"},xs={key:0,class:"info-item"},Ms={class:"info-value highlight"},Cs={key:1,class:"info-item"},Ds={class:"info-value highlight"},Es={key:2,class:"info-item"},$s={class:"info-value"},Is={key:3,class:"info-item"},Ns={class:"card-header"},Ts={class:"info-content"},Ss={class:"info-item"},As={class:"info-value"},Vs={class:"info-item"},Bs={class:"info-value code"},Us={class:"info-item"},zs={class:"info-value code"},Rs={class:"info-item"},Ls={class:"card-header"},Os={class:"info-content"},Gs={class:"info-item"},js={class:"info-value"},Fs={class:"info-item"},Ps={class:"info-value"},qs={class:"info-item"},Hs={class:"info-value"},Ks={class:"info-item"},Qs={class:"info-value"},Ys={class:"info-item"},Js={class:"info-value"},Ws={class:"info-item"},Xs={class:"info-value"},Zs={class:"card-header"},et={class:"occupied-content"},st={class:"occupied-user"},tt={class:"user-avatar"},nt={class:"user-info"},ot={class:"user-name"},at={class:"user-contact"},lt={class:"occupied-details"},it={class:"detail-item"},dt={class:"detail-value"},rt={class:"detail-item"},ct={class:"detail-value"},ut={class:"detail-item"},_t={class:"detail-value"},pt={class:"occupied-actions"},ft={class:"card-header"},mt={class:"header-actions"},vt={class:"groups-content"},yt={key:0,class:"empty-groups"},gt={key:1,class:"groups-list"},bt={class:"group-device-count"},ht={__name:"DeviceDetail",setup(kt){const S=re(),m=ge(),j=de(),C=T(!1),v=T(!1),F=S.params.id,o=R({id:null,device_id:"",device_name:"",custom_name:"",device_type:"unknown",status:"idle",vendor_id:"",product_id:"",hardware_signature:"",description:"",physical_port:"",port_location_code:"",server_name:"",server_ip:"",server_port:"",server_status:"online",remark:"",created_at:null,updated_at:null,last_connected:null,last_connected_user:"",connection_count:0,total_usage_time:0,current_user_name:"",current_user_contact:"",occupied_start_time:null,occupied_duration:0,estimated_end_time:null}),f=R({custom_name:"",device_type:"unknown",remark:""}),$=T([]);let I=null;const P=()=>{const a=S.query.from_group;if(a){m.push(`/device-center/group/${a}`);return}const e=document.referrer;if(e&&e.includes("/device-center/group/")){const i=q(e);if(i){m.push(`/device-center/group/${i}`);return}}window.history.length>1?m.go(-1):m.push("/device-center")},q=a=>{try{const i=new URL(a).pathname.match(/\/device-center\/group\/(\d+)/);return i?i[1]:null}catch(e){return console.error("解析referrer URL失败:",e),null}},A=a=>({idle:"success",occupied:"warning",damaged:"danger",offline:"info"})[a]||"info",V=a=>({idle:"空闲",occupied:"被占用",damaged:"硬件损坏",offline:"离线"})[a]||"未知",H=a=>({encryption_key:"danger",storage:"warning",input:"info",communication:"success",hardware:"primary",unknown:""})[a]||"",K=a=>({encryption_key:"加密锁",storage:"存储设备",input:"输入设备",communication:"通信设备",hardware:"硬件设备",unknown:"未知设备"})[a]||"待补充",Q=a=>({usb_ids_enhanced:"success",usb_ids:"primary",local_rules:"warning"})[a]||"info",Y=a=>({usb_ids_enhanced:"USB.IDS增强识别",usb_ids:"USB.IDS标准识别",local_rules:"本地规则识别"})[a]||"未知来源",x=a=>a?new Date(a).toLocaleString():"N/A",B=a=>{if(!a)return"0分钟";const e=Math.floor(a/3600),i=Math.floor(a%3600/60);return e>0?`${e}小时${i}分钟`:`${i}分钟`},D=async()=>{C.value=!0;try{const a=await fetch(`/api/v1/devices/${F}`,{method:"GET",headers:{Authorization:`Bearer ${j.token}`,"Content-Type":"application/json"}});if(!a.ok)throw new Error(`HTTP ${a.status}: ${a.statusText}`);const e=await a.json();Object.assign(o,e),f.custom_name=o.custom_name||"",f.device_type=o.device_type||"unknown",f.remark=o.remark||"",$.value=[]}catch(a){console.error("获取设备详情失败:",a),M.error(`获取设备详情失败: ${a.message}`),a.message.includes("404")&&m.push("/device-center")}finally{C.value=!1}},J=async a=>{try{await Fe.confirm(`确定要执行 "${U(a)}" 操作吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),M.success(`${U(a)} 操作已执行`),a==="delete"?m.push("/device-center"):D()}catch{}},U=a=>({unbind:"解除绑定",reset:"重置设备",delete:"删除设备"})[a]||a,W=()=>{M.info(`联系用户: ${o.current_user_name} (${o.current_user_contact})`)},X=()=>{M.info("已发送设备释放请求")},Z=()=>{M.info("管理设备分组")},ee=a=>{m.push(`/device-center/device-group/${a.id}`)};return ce(()=>{D(),I=setInterval(D,3e4)}),ue(()=>{I&&clearInterval(I)}),(a,e)=>{const i=he,y=pe,se=be("OfflineOutlined"),b=fe,N=Ee,te=De,ne=me,z=Ne,h=Se,oe=Te,k=Ie,w=$e,E=ye,ae=Le,le=ve;return d(),u("div",Pe,[s("div",qe,[s("div",He,[t(y,{onClick:P,type:"text",class:"back-button"},{default:n(()=>[t(i,null,{default:n(()=>[t(r(ke))]),_:1}),e[4]||(e[4]=c(" 返回设备列表 ",-1))]),_:1,__:[4]}),s("div",Ke,[s("h2",null,l(o.custom_name||o.device_name||"设备详情"),1),t(b,{type:A(o.status),size:"large"},{default:n(()=>[t(i,{class:"status-icon"},{default:n(()=>[o.status==="idle"?(d(),p(r(L),{key:0})):o.status==="occupied"?(d(),p(r(O),{key:1})):o.status==="damaged"?(d(),p(r(we),{key:2})):(d(),p(se,{key:3}))]),_:1}),c(" "+l(V(o.status)),1)]),_:1},8,["type"])])]),s("div",Qe,[t(y,{onClick:D,loading:C.value,type:"primary"},{default:n(()=>[t(i,null,{default:n(()=>[t(r(xe))]),_:1}),e[5]||(e[5]=c(" 刷新数据 ",-1))]),_:1,__:[5]},8,["loading"]),t(y,{onClick:e[0]||(e[0]=_=>v.value=!v.value),type:v.value?"success":"warning"},{default:n(()=>[t(i,null,{default:n(()=>[t(r(Me))]),_:1}),c(" "+l(v.value?"保存修改":"修改信息"),1)]),_:1},8,["type"]),t(ne,{onCommand:J},{dropdown:n(()=>[t(te,null,{default:n(()=>[t(N,{command:"unbind"},{default:n(()=>e[7]||(e[7]=[c("解除绑定",-1)])),_:1,__:[7]}),t(N,{command:"reset"},{default:n(()=>e[8]||(e[8]=[c("重置设备",-1)])),_:1,__:[8]}),t(N,{divided:"",command:"delete"},{default:n(()=>e[9]||(e[9]=[c("删除设备",-1)])),_:1,__:[9]})]),_:1})]),default:n(()=>[t(y,{type:"danger"},{default:n(()=>[e[6]||(e[6]=c(" 设备操作 ",-1)),t(i,{class:"el-icon--right"},{default:n(()=>[t(r(Ce))]),_:1})]),_:1,__:[6]})]),_:1})])]),_e((d(),u("div",Ye,[t(E,{gutter:20,class:"content-sections"},{default:n(()=>[t(w,{span:12},{default:n(()=>[t(k,{class:"info-card"},{header:n(()=>[s("div",Je,[t(i,null,{default:n(()=>[t(r(Ae))]),_:1}),e[10]||(e[10]=s("span",null,"基础信息",-1))])]),default:n(()=>[s("div",We,[s("div",Xe,[e[11]||(e[11]=s("span",{class:"info-label"},"设备ID：",-1)),s("span",Ze,l(o.device_id),1)]),s("div",es,[e[12]||(e[12]=s("span",{class:"info-label"},"设备名称：",-1)),v.value?(d(),p(z,{key:0,modelValue:f.custom_name,"onUpdate:modelValue":e[1]||(e[1]=_=>f.custom_name=_),placeholder:"自定义设备名称",class:"edit-input"},null,8,["modelValue"])):(d(),u("span",ss,l(o.custom_name||o.device_name),1))]),o.custom_name?(d(),u("div",ts,[e[13]||(e[13]=s("span",{class:"info-label"},"自动生成名称：",-1)),s("span",ns,l(o.device_name),1)])):g("",!0),s("div",os,[e[14]||(e[14]=s("span",{class:"info-label"},"设备类型：",-1)),v.value?(d(),p(oe,{key:0,modelValue:f.device_type,"onUpdate:modelValue":e[2]||(e[2]=_=>f.device_type=_),placeholder:"选择设备类型",class:"edit-input"},{default:n(()=>[t(h,{label:"加密锁",value:"encryption_key"}),t(h,{label:"存储设备",value:"storage"}),t(h,{label:"输入设备",value:"input"}),t(h,{label:"通信设备",value:"communication"}),t(h,{label:"硬件设备",value:"hardware"}),t(h,{label:"未知设备",value:"unknown"})]),_:1},8,["modelValue"])):(d(),p(b,{key:1,type:H(o.device_type),size:"small"},{default:n(()=>[c(l(K(o.device_type)),1)]),_:1},8,["type"]))]),s("div",as,[e[15]||(e[15]=s("span",{class:"info-label"},"设备状态：",-1)),t(b,{type:A(o.status),size:"small"},{default:n(()=>[c(l(V(o.status)),1)]),_:1},8,["type"])]),s("div",ls,[e[16]||(e[16]=s("span",{class:"info-label"},"备注信息：",-1)),v.value?(d(),p(z,{key:0,modelValue:f.remark,"onUpdate:modelValue":e[3]||(e[3]=_=>f.remark=_),type:"textarea",rows:3,placeholder:"设备备注信息（最长100字符）",maxlength:"100","show-word-limit":"",class:"edit-input"},null,8,["modelValue"])):(d(),u("span",is,l(o.remark||"无备注"),1))])])]),_:1})]),_:1}),t(w,{span:12},{default:n(()=>[t(k,{class:"info-card"},{header:n(()=>[s("div",ds,[t(i,null,{default:n(()=>[t(r(Ve))]),_:1}),e[17]||(e[17]=s("span",null,"硬件信息",-1))])]),default:n(()=>[s("div",rs,[s("div",cs,[e[18]||(e[18]=s("span",{class:"info-label"},"厂商ID：",-1)),s("span",us,l(o.vendor_id||"N/A"),1)]),s("div",_s,[e[19]||(e[19]=s("span",{class:"info-label"},"产品ID：",-1)),s("span",ps,l(o.product_id||"N/A"),1)]),s("div",fs,[e[20]||(e[20]=s("span",{class:"info-label"},"硬件签名：",-1)),s("span",ms,l(o.hardware_signature||"N/A"),1)]),s("div",vs,[e[21]||(e[21]=s("span",{class:"info-label"},"设备描述：",-1)),s("span",ys,l(o.description||"N/A"),1)]),s("div",gs,[e[22]||(e[22]=s("span",{class:"info-label"},"物理端口：",-1)),s("span",bs,l(o.physical_port||"N/A"),1)]),s("div",hs,[e[23]||(e[23]=s("span",{class:"info-label"},"端口位置码：",-1)),s("span",ks,l(o.port_location_code||"N/A"),1)]),o.usb_ids_vendor_name||o.usb_ids_device_name?(d(),u("div",ws,[e[28]||(e[28]=s("div",{class:"info-divider"},[s("span",{class:"divider-text"},"USB.IDS 识别信息")],-1)),o.usb_ids_vendor_name?(d(),u("div",xs,[e[24]||(e[24]=s("span",{class:"info-label"},"厂商名称：",-1)),s("span",Ms,l(o.usb_ids_vendor_name),1)])):g("",!0),o.usb_ids_device_name?(d(),u("div",Cs,[e[25]||(e[25]=s("span",{class:"info-label"},"产品名称：",-1)),s("span",Ds,l(o.usb_ids_device_name),1)])):g("",!0),o.usb_ids_full_name?(d(),u("div",Es,[e[26]||(e[26]=s("span",{class:"info-label"},"完整名称：",-1)),s("span",$s,l(o.usb_ids_full_name),1)])):g("",!0),o.identification_source?(d(),u("div",Is,[e[27]||(e[27]=s("span",{class:"info-label"},"识别来源：",-1)),t(b,{type:Q(o.identification_source),size:"small"},{default:n(()=>[c(l(Y(o.identification_source)),1)]),_:1},8,["type"])])):g("",!0)])):g("",!0)])]),_:1})]),_:1})]),_:1}),t(E,{gutter:20,class:"content-sections"},{default:n(()=>[t(w,{span:12},{default:n(()=>[t(k,{class:"info-card"},{header:n(()=>[s("div",Ns,[t(i,null,{default:n(()=>[t(r(L))]),_:1}),e[29]||(e[29]=s("span",null,"所属服务器",-1))])]),default:n(()=>[s("div",Ts,[s("div",Ss,[e[30]||(e[30]=s("span",{class:"info-label"},"服务器名称：",-1)),s("span",As,l(o.server_name||"N/A"),1)]),s("div",Vs,[e[31]||(e[31]=s("span",{class:"info-label"},"服务器IP：",-1)),s("span",Bs,l(o.server_ip||"N/A"),1)]),s("div",Us,[e[32]||(e[32]=s("span",{class:"info-label"},"服务器端口：",-1)),s("span",zs,l(o.server_port||"N/A"),1)]),s("div",Rs,[e[33]||(e[33]=s("span",{class:"info-label"},"服务器状态：",-1)),t(b,{type:o.server_status==="online"?"success":"danger",size:"small"},{default:n(()=>[c(l(o.server_status==="online"?"在线":"离线"),1)]),_:1},8,["type"])])])]),_:1})]),_:1}),t(w,{span:12},{default:n(()=>[t(k,{class:"info-card"},{header:n(()=>[s("div",Ls,[t(i,null,{default:n(()=>[t(r(Be))]),_:1}),e[34]||(e[34]=s("span",null,"使用记录",-1))])]),default:n(()=>[s("div",Os,[s("div",Gs,[e[35]||(e[35]=s("span",{class:"info-label"},"创建时间：",-1)),s("span",js,l(x(o.created_at)),1)]),s("div",Fs,[e[36]||(e[36]=s("span",{class:"info-label"},"更新时间：",-1)),s("span",Ps,l(x(o.updated_at)),1)]),s("div",qs,[e[37]||(e[37]=s("span",{class:"info-label"},"最后连接：",-1)),s("span",Hs,l(x(o.last_connected)),1)]),s("div",Ks,[e[38]||(e[38]=s("span",{class:"info-label"},"连接用户：",-1)),s("span",Qs,l(o.last_connected_user||"N/A"),1)]),s("div",Ys,[e[39]||(e[39]=s("span",{class:"info-label"},"连接次数：",-1)),s("span",Js,l(o.connection_count||0)+" 次",1)]),s("div",Ws,[e[40]||(e[40]=s("span",{class:"info-label"},"总使用时长：",-1)),s("span",Xs,l(B(o.total_usage_time)),1)])])]),_:1})]),_:1})]),_:1}),o.status==="occupied"?(d(),p(E,{key:0,gutter:20,class:"content-sections"},{default:n(()=>[t(w,{span:24},{default:n(()=>[t(k,{class:"info-card occupied-info"},{header:n(()=>[s("div",Zs,[t(i,null,{default:n(()=>[t(r(O))]),_:1}),e[41]||(e[41]=s("span",null,"当前占用信息",-1))])]),default:n(()=>[s("div",et,[s("div",st,[s("div",tt,[t(i,null,{default:n(()=>[t(r(Ue))]),_:1})]),s("div",nt,[s("div",ot,l(o.current_user_name||"N/A"),1),s("div",at,l(o.current_user_contact||"N/A"),1)])]),s("div",lt,[s("div",it,[e[42]||(e[42]=s("span",{class:"detail-label"},"占用开始：",-1)),s("span",dt,l(x(o.occupied_start_time)),1)]),s("div",rt,[e[43]||(e[43]=s("span",{class:"detail-label"},"持续时间：",-1)),s("span",ct,l(B(o.occupied_duration)),1)]),s("div",ut,[e[44]||(e[44]=s("span",{class:"detail-label"},"预计结束：",-1)),s("span",_t,l(x(o.estimated_end_time)||"未设置"),1)])]),s("div",pt,[t(y,{type:"primary",onClick:W},{default:n(()=>[t(i,null,{default:n(()=>[t(r(ze))]),_:1}),e[45]||(e[45]=c(" 联系用户 ",-1))]),_:1,__:[45]}),t(y,{type:"warning",onClick:X},{default:n(()=>[t(i,null,{default:n(()=>[t(r(Re))]),_:1}),e[46]||(e[46]=c(" 请求释放 ",-1))]),_:1,__:[46]})])])]),_:1})]),_:1})]),_:1})):g("",!0),t(E,{gutter:20,class:"content-sections"},{default:n(()=>[t(w,{span:24},{default:n(()=>[t(k,{class:"info-card"},{header:n(()=>[s("div",ft,[t(i,null,{default:n(()=>[t(r(G))]),_:1}),e[48]||(e[48]=s("span",null,"所属分组",-1)),s("div",mt,[t(y,{size:"small",onClick:Z},{default:n(()=>[t(i,null,{default:n(()=>[t(r(je))]),_:1}),e[47]||(e[47]=c(" 管理分组 ",-1))]),_:1,__:[47]})])])]),default:n(()=>[s("div",vt,[$.value.length===0?(d(),u("div",yt,[t(ae,{description:"该设备未加入任何分组"})])):(d(),u("div",gt,[(d(!0),u(Oe,null,Ge($.value,_=>(d(),p(b,{key:_.id,size:"large",class:"group-tag",onClick:wt=>ee(_)},{default:n(()=>[t(i,null,{default:n(()=>[t(r(G))]),_:1}),c(" "+l(_.name)+" ",1),s("span",bt,"("+l(_.device_count)+"个设备)",1)]),_:2},1032,["onClick"]))),128))]))])]),_:1})]),_:1})]),_:1})])),[[le,C.value]])])}}},Bt=ie(ht,[["__scopeId","data-v-e44e642a"]]);export{Bt as default};
