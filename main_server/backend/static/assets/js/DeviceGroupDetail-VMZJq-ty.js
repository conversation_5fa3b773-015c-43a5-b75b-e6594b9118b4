import{_ as ce}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                   *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                     *//* empty css               *//* empty css                  *//* empty css                 *//* empty css               *//* empty css                *//* empty css                  *//* empty css                         */import{r as $,g as re,a as ue,a9 as B,o as _e,bN as ve,c as z,d as t,ab as pe,e as s,w as o,m as fe,t as i,a7 as me,M as ge,ac as he,ad as we,cK as be,s as v,b as ye,i as D,n as r,E as ke,p as _,dd as De,y as O,d8 as Ce,dr as xe,c$ as Ee,O as Te,P as $e,Q as Be,L as P,D as Ie,d6 as Ae,R as Me,cL as Se,dn as ze,a0 as je,ah as Ve,ak as Le,z as Ne,k as Ue,d2 as Ge,ai as Re,aj as Oe,a4 as j}from"./index-Bcq_EQlf.js";import f from"./websocket-C510UsRD.js";const Pe={class:"device-group-detail"},qe={class:"detail-header"},Fe={class:"header-left"},He={class:"group-title"},Je={class:"header-right"},Ke={class:"detail-content"},Qe={class:"overview-cards"},We={class:"card-content"},Xe={class:"card-icon devices"},Ye={class:"card-info"},Ze={class:"card-value"},et={class:"card-content"},tt={class:"card-icon online"},st={class:"card-info"},at={class:"card-value"},ot={class:"card-content"},nt={class:"card-icon servers"},lt={class:"card-info"},it={class:"card-value"},dt={class:"card-content"},ct={class:"card-icon users"},rt={class:"card-info"},ut={class:"card-value"},_t={class:"card-header"},vt={class:"info-content"},pt={class:"info-item"},ft={class:"info-value"},mt={class:"info-item"},gt={class:"info-value"},ht={class:"info-item"},wt={class:"info-value"},bt={class:"info-item"},yt={class:"info-value"},kt={class:"info-item"},Dt={class:"card-header"},Ct={class:"info-content"},xt={class:"info-item"},Et={class:"info-value"},Tt={class:"info-item"},$t={class:"info-value"},Bt={class:"info-item"},It={class:"info-value"},At={class:"info-item"},Mt={class:"info-value"},St={class:"info-item"},zt={class:"info-value"},jt={class:"card-header"},Vt={class:"header-actions"},Lt={class:"device-list"},Nt={class:"device-name"},Ut={class:"primary-name"},Gt={key:0,class:"secondary-name"},Rt={class:"device-source-single"},Ot={class:"action-buttons"},Pt={__name:"DeviceGroupDetail",setup(qt){const I=re(),C=ye(),k=$(!1),h=I.params.groupId,x=$(""),E=$(""),c=ue({id:null,name:"",description:"",device_count:0,user_count:0,status:"active",created_at:null,updated_at:null}),w=$([]);let A=null;const V=B(()=>w.value.filter(a=>a.status==="online").length),q=B(()=>w.value.filter(a=>a.status==="offline").length),L=B(()=>new Set(w.value.map(e=>e.server_id)).size),F=B(()=>{let a=w.value;if(x.value){const e=x.value.toLowerCase();a=a.filter(n=>n.device_name&&n.device_name.toLowerCase().includes(e))}return E.value&&(a=a.filter(e=>e.status===E.value)),a}),H=()=>{C.push("/device-center?tab=groups")},N=a=>({active:"success",inactive:"info",disabled:"danger"})[a]||"info",J=a=>({active:"活跃",inactive:"非活跃",disabled:"已禁用"})[a]||"未知",K=a=>({online:"success",offline:"info",busy:"warning",error:"danger"})[a]||"info",Q=a=>({online:"在线",offline:"离线",busy:"占用中",error:"错误"})[a]||"未知",M=a=>a?new Date(a).toLocaleString():"N/A",W=a=>({"🔐 CA锁":"CA锁",加密锁:"加密锁","📡 通信设备":"通信设备","📶 蓝牙设备":"蓝牙设备",ca_lock:"CA锁",encryption_key:"加密锁",bank_ukey:"银行U盾",communication:"通信设备",bluetooth:"蓝牙设备",other:"其他设备"})[a]||a||"未知类型",b=async()=>{k.value=!0;try{const a=await fetch(`/api/v1/device-groups/${h}`,{headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"}});if(!a.ok)throw new Error(`HTTP ${a.status}: ${a.statusText}`);const e=await a.json();Object.assign(c,{id:e.id||h,name:e.name||"未命名分组",description:e.description||"",device_count:e.device_count||0,user_count:e.user_count||0,status:e.status||"active",created_at:e.created_at,updated_at:e.updated_at});const n=await fetch(`/api/v1/device-groups/${h}/devices`,{headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"}});if(n.ok){const u=await n.json();console.log("分组设备API响应:",u);let p=[];u.success&&u.data?p=Array.isArray(u.data)?u.data:[]:p=u.devices||u||[],w.value=p.map(d=>({id:d.id,device_id:d.device_id||d.id,device_name:d.device_name,device_type:d.device_type,custom_name:d.custom_name,server_id:d.slave_server_id,server_name:`服务器-${d.slave_server_id||"未知"}`,physical_port:d.physical_port,vendor_id:d.vendor_id,product_id:d.product_id,status:d.status,last_connected:d.added_at||d.last_used_at||d.connected_at}))}else w.value=[];k.value=!1}catch(a){console.error("获取分组详情失败:",a),v.error("获取分组详情失败"),k.value=!1}},X=async a=>{switch(a){case"edit":v.info("编辑分组功能开发中...");break;case"add-device":await Y();break;case"remove-device":v.info("请在设备列表中选择要移除的设备");break;case"delete":await Z();break}},Y=async()=>{try{const a=await fetch(`/api/v1/devices?exclude_group=${h}`,{headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"}});if(!a.ok)throw new Error("获取设备列表失败");const e=await a.json();console.log("可添加设备API响应:",e);let n=[];if(e.success&&e.data?n=e.data.devices||e.data||[]:n=e.devices||e||[],n.length===0){v.warning("没有可添加的设备");return}await j.confirm(`找到 ${n.length} 个可添加的设备，是否继续？`,"添加设备",{confirmButtonText:"继续",cancelButtonText:"取消",type:"info"}),v.info("设备选择对话框功能开发中...")}catch(a){a.message&&a.message!=="cancel"&&v.error(a.message)}},Z=async()=>{try{if(await j.confirm(`确定要删除分组 "${c.name}" 吗？此操作不可恢复！`,"确认删除",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"error"}),!(await fetch(`/api/v1/device-groups/${h}`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"}})).ok)throw new Error("删除分组失败");v.success("分组删除成功"),C.push("/device-center/device-groups")}catch(a){a.message&&a.message!=="cancel"&&v.error(a.message)}},ee=a=>{const e=I.params.id;C.push({path:`/device-center/device/${a.id}`,query:{from_group:e}})},te=a=>{C.push(`/device-center/slave-server/${a.server_id}`)},se=async a=>{try{await j.confirm(`确定要从分组中移除设备 "${a.custom_name||a.device_name}" 吗？`,"确认移除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await fetch(`/api/v1/device-groups/${h}/devices`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"},body:JSON.stringify({device_ids:[a.id]})});if(!e.ok)throw new Error(`移除失败: ${e.statusText}`);v.success(`设备 "${a.custom_name||a.device_name}" 已从分组中移除`),b()}catch(e){e.message&&e.message!=="cancel"&&v.error(e.message)}},U=a=>{console.log("收到设备分配更新事件:",a),a.assignments&&a.assignments.find(n=>n.group_id===parseInt(I.params.id))&&(console.log("当前分组有设备分配更新，刷新数据"),b())},y=a=>{console.log("收到设备更新事件:",a),b()};return _e(()=>{b(),A=setInterval(b,3e4),f.on("devices_batch_assigned",U),f.on("device_updates",y),f.on("device_added",y),f.on("device_removed",y)}),ve(()=>{A&&clearInterval(A),f.off("devices_batch_assigned",U),f.off("device_updates",y),f.off("device_added",y),f.off("device_removed",y)}),(a,e)=>{const n=ke,u=fe,p=me,d=Be,ae=$e,oe=ge,m=we,S=Se,G=be,ne=Ue,T=Oe,le=Re,g=Le,ie=Ve,de=he;return D(),z("div",Pe,[t("div",qe,[t("div",Fe,[s(u,{onClick:H,type:"text",class:"back-button"},{default:o(()=>[s(n,null,{default:o(()=>[s(_(De))]),_:1}),e[2]||(e[2]=r(" 返回分组列表 ",-1))]),_:1,__:[2]}),t("div",He,[t("h2",null,i(c.name||"设备分组详情"),1),s(p,{type:N(c.status),size:"large"},{default:o(()=>[s(n,{class:"status-icon"},{default:o(()=>[c.device_count>0?(D(),O(_(Ce),{key:0})):(D(),O(_(xe),{key:1}))]),_:1}),r(" "+i(c.device_count||0)+" 个设备 ",1)]),_:1},8,["type"])])]),t("div",Je,[s(u,{onClick:b,loading:k.value,type:"primary"},{default:o(()=>[s(n,null,{default:o(()=>[s(_(Ee))]),_:1}),e[3]||(e[3]=r(" 刷新数据 ",-1))]),_:1,__:[3]},8,["loading"]),s(oe,{onCommand:X},{dropdown:o(()=>[s(ae,null,{default:o(()=>[s(d,{command:"edit"},{default:o(()=>e[5]||(e[5]=[r("编辑分组",-1)])),_:1,__:[5]}),s(d,{command:"add-device"},{default:o(()=>e[6]||(e[6]=[r("添加设备",-1)])),_:1,__:[6]}),s(d,{command:"remove-device"},{default:o(()=>e[7]||(e[7]=[r("移除设备",-1)])),_:1,__:[7]}),s(d,{divided:"",command:"delete"},{default:o(()=>e[8]||(e[8]=[r("删除分组",-1)])),_:1,__:[8]})]),_:1})]),default:o(()=>[s(u,{type:"success"},{default:o(()=>[e[4]||(e[4]=r(" 管理操作 ",-1)),s(n,{class:"el-icon--right"},{default:o(()=>[s(_(Te))]),_:1})]),_:1,__:[4]})]),_:1})])]),pe((D(),z("div",Ke,[t("div",Qe,[s(m,{class:"overview-card"},{default:o(()=>[t("div",We,[t("div",Xe,[s(n,null,{default:o(()=>[s(_(P))]),_:1})]),t("div",Ye,[t("div",Ze,i(c.device_count||0),1),e[9]||(e[9]=t("div",{class:"card-label"},"设备总数",-1))])])]),_:1}),s(m,{class:"overview-card"},{default:o(()=>[t("div",et,[t("div",tt,[s(n,null,{default:o(()=>[s(_(Ie))]),_:1})]),t("div",st,[t("div",at,i(V.value),1),e[10]||(e[10]=t("div",{class:"card-label"},"在线设备",-1))])])]),_:1}),s(m,{class:"overview-card"},{default:o(()=>[t("div",ot,[t("div",nt,[s(n,null,{default:o(()=>[s(_(Ae))]),_:1})]),t("div",lt,[t("div",it,i(L.value),1),e[11]||(e[11]=t("div",{class:"card-label"},"涉及服务器",-1))])])]),_:1}),s(m,{class:"overview-card"},{default:o(()=>[t("div",dt,[t("div",ct,[s(n,null,{default:o(()=>[s(_(Me))]),_:1})]),t("div",rt,[t("div",ut,i(c.user_count||0),1),e[12]||(e[12]=t("div",{class:"card-label"},"授权用户",-1))])])]),_:1})]),s(G,{gutter:20,class:"detail-sections"},{default:o(()=>[s(S,{span:12},{default:o(()=>[s(m,{class:"info-card"},{header:o(()=>[t("div",_t,[s(n,null,{default:o(()=>[s(_(ze))]),_:1}),e[13]||(e[13]=t("span",null,"基础信息",-1))])]),default:o(()=>[t("div",vt,[t("div",pt,[e[14]||(e[14]=t("span",{class:"info-label"},"分组名称：",-1)),t("span",ft,i(c.name),1)]),t("div",mt,[e[15]||(e[15]=t("span",{class:"info-label"},"分组描述：",-1)),t("span",gt,i(c.description||"暂无描述"),1)]),t("div",ht,[e[16]||(e[16]=t("span",{class:"info-label"},"创建时间：",-1)),t("span",wt,i(M(c.created_at)),1)]),t("div",bt,[e[17]||(e[17]=t("span",{class:"info-label"},"更新时间：",-1)),t("span",yt,i(M(c.updated_at)),1)]),t("div",kt,[e[18]||(e[18]=t("span",{class:"info-label"},"分组状态：",-1)),s(p,{type:N(c.status)},{default:o(()=>[r(i(J(c.status)),1)]),_:1},8,["type"])])])]),_:1})]),_:1}),s(S,{span:12},{default:o(()=>[s(m,{class:"info-card"},{header:o(()=>[t("div",Dt,[s(n,null,{default:o(()=>[s(_(je))]),_:1}),e[19]||(e[19]=t("span",null,"统计信息",-1))])]),default:o(()=>[t("div",Ct,[t("div",xt,[e[20]||(e[20]=t("span",{class:"info-label"},"设备总数：",-1)),t("span",Et,i(c.device_count||0)+" 个",1)]),t("div",Tt,[e[21]||(e[21]=t("span",{class:"info-label"},"在线设备：",-1)),t("span",$t,i(V.value)+" 个",1)]),t("div",Bt,[e[22]||(e[22]=t("span",{class:"info-label"},"离线设备：",-1)),t("span",It,i(q.value)+" 个",1)]),t("div",At,[e[23]||(e[23]=t("span",{class:"info-label"},"涉及服务器：",-1)),t("span",Mt,i(L.value)+" 台",1)]),t("div",St,[e[24]||(e[24]=t("span",{class:"info-label"},"授权用户：",-1)),t("span",zt,i(c.user_count||0)+" 人",1)])])]),_:1})]),_:1})]),_:1}),s(G,{gutter:20,class:"detail-sections"},{default:o(()=>[s(S,{span:24},{default:o(()=>[s(m,{class:"info-card"},{header:o(()=>[t("div",jt,[s(n,null,{default:o(()=>[s(_(P))]),_:1}),e[25]||(e[25]=t("span",null,"设备列表",-1)),t("div",Vt,[s(ne,{modelValue:x.value,"onUpdate:modelValue":e[0]||(e[0]=l=>x.value=l),placeholder:"搜索设备名称...",style:{width:"200px","margin-right":"10px"},clearable:""},{prefix:o(()=>[s(n,null,{default:o(()=>[s(_(Ge))]),_:1})]),_:1},8,["modelValue"]),s(le,{modelValue:E.value,"onUpdate:modelValue":e[1]||(e[1]=l=>E.value=l),placeholder:"筛选状态",style:{width:"120px"},clearable:""},{default:o(()=>[s(T,{label:"全部",value:""}),s(T,{label:"在线",value:"online"}),s(T,{label:"离线",value:"offline"}),s(T,{label:"占用中",value:"busy"})]),_:1},8,["modelValue"])])])]),default:o(()=>[t("div",Lt,[s(ie,{data:F.value,stripe:""},{default:o(()=>[s(g,{prop:"device_id",label:"设备ID",width:"120"}),s(g,{label:"设备名称",width:"280","show-overflow-tooltip":""},{default:o(({row:l})=>[t("div",Nt,[t("span",Ut,i(l.custom_name||l.device_name),1),l.custom_name&&l.device_name!==l.custom_name?(D(),z("span",Gt," ("+i(l.device_name)+") ",1)):Ne("",!0)])]),_:1}),s(g,{label:"设备类型",width:"120"},{default:o(({row:l})=>[s(p,{size:"small",type:"info"},{default:o(()=>[r(i(W(l.device_type)),1)]),_:2},1024)]),_:1}),s(g,{label:"设备来源",width:"240","show-overflow-tooltip":""},{default:o(({row:l})=>[t("div",Rt,i(l.server_name||"未知服务器"),1)]),_:1}),s(g,{label:"状态",width:"100"},{default:o(({row:l})=>[s(p,{type:K(l.status),size:"small"},{default:o(()=>[r(i(Q(l.status)),1)]),_:2},1032,["type"])]),_:1}),s(g,{label:"最后连接",width:"180","show-overflow-tooltip":""},{default:o(({row:l})=>[r(i(M(l.last_connected)),1)]),_:1}),s(g,{label:"操作",width:"180",fixed:"right"},{default:o(({row:l})=>[t("div",Ot,[s(u,{size:"small",onClick:R=>ee(l)},{default:o(()=>e[26]||(e[26]=[r(" 详情 ",-1)])),_:2,__:[26]},1032,["onClick"]),s(u,{size:"small",onClick:R=>te(l)},{default:o(()=>e[27]||(e[27]=[r(" 服务器 ",-1)])),_:2,__:[27]},1032,["onClick"]),s(u,{size:"small",type:"danger",onClick:R=>se(l)},{default:o(()=>e[28]||(e[28]=[r(" 移除 ",-1)])),_:2,__:[28]},1032,["onClick"])])]),_:1})]),_:1},8,["data"])])]),_:1})]),_:1})]),_:1})])),[[de,k.value]])])}}},ls=ce(Pt,[["__scopeId","data-v-96a0d0d5"]]);export{ls as default};
