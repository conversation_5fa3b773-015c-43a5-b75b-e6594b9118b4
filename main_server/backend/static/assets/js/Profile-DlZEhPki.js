import{_ as q}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                   *//* empty css                     *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                *//* empty css                  *//* empty css                 *//* empty css                             *//* empty css                  */import{u as G,r as k,a as H,o as J,c as y,e as o,w as a,ad as K,x as Q,i as p,d as r,z as w,N as W,n as l,t as i,p as n,af as X,ae as Y,a7 as Z,y as m,a5 as C,a6 as D,an as ee,m as te,f as ae,j as oe,ai as se,aj as le,k as ne,s as U}from"./index-CwbwN4Sv.js";const re={class:"profile-container"},ie={class:"card-header"},ue={class:"profile-content"},de={class:"avatar-section"},pe={class:"user-name"},_e={class:"user-username"},ce={class:"info-section"},me={class:"info-section"},fe={class:"info-section"},ge={class:"roles-container"},ve={class:"permissions-container"},ye={key:0,class:"no-permissions"},be={key:0,class:"action-section"},ke={__name:"Profile",setup(we){const s=G(),f=k(null),v=k(!1),b=k(!1),I=k(),g=H({type:"",description:""}),A={type:[{required:!0,message:"请选择申请类型",trigger:"change"}],description:[{required:!0,message:"请填写申请说明",trigger:"blur"},{min:10,message:"申请说明至少10个字符",trigger:"blur"}]},N=u=>u?new Date(u).toLocaleString("zh-CN"):"未知",F=u=>({0:"集团总部",1:"大区",2:"分公司",3:"部门",4:"小组"})[u]||"未知",M=u=>({超级管理员:"danger",管理员:"success",普通用户:"info",新用户:"warning"})[u]||"info",B=async()=>{try{await I.value.validate(),b.value=!0,await new Promise(u=>setTimeout(u,1e3)),U.success("权限申请已提交，请等待管理员审核"),v.value=!1,I.value.resetFields(),Object.assign(g,{type:"",description:""})}catch(u){console.error("提交申请失败:",u),U.error("提交申请失败，请重试")}finally{b.value=!1}},T=async()=>{var u;try{if(!((u=s.userInfo)!=null&&u.organization_id)){console.warn("用户未分配组织");return}const e=await fetch(`/api/v1/organizations/${s.userInfo.organization_id}`,{headers:{Authorization:`Bearer ${s.token}`,"Content-Type":"application/json"}});if(e.ok){const _=await e.json();f.value=_}else console.error("获取组织信息失败:",e.status),f.value={name:"未知组织",level:0,path:"/"}}catch(e){console.error("获取组织信息失败:",e),f.value={name:"获取失败",level:0,path:"/"}}};return J(()=>{T()}),(u,e)=>{const _=Z,j=W,d=Y,z=X,R=ee,E=te,S=K,V=le,L=se,h=oe,O=ne,$=ae,P=Q;return p(),y("div",re,[o(S,{class:"profile-card"},{header:a(()=>[r("div",ie,[e[9]||(e[9]=r("h2",null,"个人资料",-1)),n(s).isNewUser?(p(),m(_,{key:0,type:"warning",size:"large"},{default:a(()=>e[5]||(e[5]=[l(" 新用户 ",-1)])),_:1,__:[5]})):n(s).isSuperAdmin?(p(),m(_,{key:1,type:"danger",size:"large"},{default:a(()=>e[6]||(e[6]=[l(" 超级管理员 ",-1)])),_:1,__:[6]})):n(s).isManager?(p(),m(_,{key:2,type:"success",size:"large"},{default:a(()=>e[7]||(e[7]=[l(" 管理员 ",-1)])),_:1,__:[7]})):(p(),m(_,{key:3,type:"info",size:"large"},{default:a(()=>e[8]||(e[8]=[l(" 普通用户 ",-1)])),_:1,__:[8]}))])]),default:a(()=>{var c;return[r("div",ue,[r("div",de,[o(j,{size:120,class:"user-avatar"},{default:a(()=>[l(i(n(s).userName.charAt(0)),1)]),_:1}),r("h3",pe,i(n(s).userName),1),r("p",_e,"@"+i((c=n(s).userInfo)==null?void 0:c.username),1)]),r("div",ce,[e[10]||(e[10]=r("h4",null,"基本信息",-1)),o(z,{column:1,border:""},{default:a(()=>[o(d,{label:"用户名"},{default:a(()=>{var t;return[l(i((t=n(s).userInfo)==null?void 0:t.username),1)]}),_:1}),o(d,{label:"姓名"},{default:a(()=>{var t;return[l(i((t=n(s).userInfo)==null?void 0:t.full_name),1)]}),_:1}),o(d,{label:"邮箱"},{default:a(()=>{var t;return[l(i((t=n(s).userInfo)==null?void 0:t.email),1)]}),_:1}),o(d,{label:"手机号"},{default:a(()=>{var t;return[l(i(((t=n(s).userInfo)==null?void 0:t.phone)||"未设置"),1)]}),_:1}),o(d,{label:"账户状态"},{default:a(()=>{var t;return[o(_,{type:(t=n(s).userInfo)!=null&&t.is_active?"success":"danger"},{default:a(()=>{var x;return[l(i((x=n(s).userInfo)!=null&&x.is_active?"正常":"已禁用"),1)]}),_:1},8,["type"])]}),_:1}),o(d,{label:"注册时间"},{default:a(()=>{var t;return[l(i(N((t=n(s).userInfo)==null?void 0:t.created_at)),1)]}),_:1}),o(d,{label:"最后登录"},{default:a(()=>{var t;return[l(i(N((t=n(s).userInfo)==null?void 0:t.last_login)),1)]}),_:1})]),_:1})]),r("div",me,[e[11]||(e[11]=r("h4",null,"组织信息",-1)),o(z,{column:1,border:""},{default:a(()=>[o(d,{label:"所属组织"},{default:a(()=>{var t;return[l(i(((t=f.value)==null?void 0:t.name)||"未分配"),1)]}),_:1}),o(d,{label:"组织层级"},{default:a(()=>{var t;return[l(i(F((t=f.value)==null?void 0:t.level)),1)]}),_:1}),o(d,{label:"组织路径"},{default:a(()=>{var t;return[l(i(((t=f.value)==null?void 0:t.path)||"/"),1)]}),_:1})]),_:1})]),r("div",fe,[e[13]||(e[13]=r("h4",null,"角色权限",-1)),o(z,{column:1,border:""},{default:a(()=>[o(d,{label:"用户角色"},{default:a(()=>[r("div",ge,[(p(!0),y(C,null,D(n(s).userRoles,t=>(p(),m(_,{key:t.id,type:M(t.name),class:"role-tag"},{default:a(()=>[l(i(t.name),1)]),_:2},1032,["type"]))),128))])]),_:1}),n(s).isNewUser?w("",!0):(p(),m(d,{key:0,label:"权限列表"},{default:a(()=>[r("div",ve,[(p(!0),y(C,null,D(n(s).permissions,t=>(p(),m(_,{key:t,size:"small",class:"permission-tag"},{default:a(()=>[l(i(t),1)]),_:2},1024))),128)),n(s).permissions.length===0?(p(),y("span",ye," 暂无特殊权限 ")):w("",!0)])]),_:1})),n(s).isNewUser?(p(),m(d,{key:1,label:"权限说明"},{default:a(()=>[o(R,{title:"新用户权限限制",type:"warning",closable:!1,"show-icon":""},{default:a(()=>e[12]||(e[12]=[r("p",null,"作为新用户，您当前只能访问个人资料页面。",-1),r("p",null,"如需使用其他功能，请联系管理员进行权限申请。",-1)])),_:1,__:[12]})]),_:1})):w("",!0)]),_:1})]),n(s).isNewUser?(p(),y("div",be,[o(E,{type:"primary",size:"large",onClick:e[0]||(e[0]=t=>v.value=!0),disabled:b.value},{default:a(()=>e[14]||(e[14]=[l(" 申请权限 ",-1)])),_:1,__:[14]},8,["disabled"])])):w("",!0)])]}),_:1}),o(P,{modelValue:v.value,"onUpdate:modelValue":e[4]||(e[4]=c=>v.value=c),title:"权限申请",width:"500px","close-on-click-modal":!1},{footer:a(()=>[o(E,{onClick:e[3]||(e[3]=c=>v.value=!1)},{default:a(()=>e[15]||(e[15]=[l("取消",-1)])),_:1,__:[15]}),o(E,{type:"primary",onClick:B,loading:b.value},{default:a(()=>e[16]||(e[16]=[l(" 提交申请 ",-1)])),_:1,__:[16]},8,["loading"])]),default:a(()=>[o($,{ref_key:"applicationFormRef",ref:I,model:g,rules:A,"label-width":"80px"},{default:a(()=>[o(h,{label:"申请类型",prop:"type"},{default:a(()=>[o(L,{modelValue:g.type,"onUpdate:modelValue":e[1]||(e[1]=c=>g.type=c),placeholder:"请选择申请类型"},{default:a(()=>[o(V,{label:"权限升级",value:"permission_upgrade"}),o(V,{label:"功能访问",value:"feature_access"}),o(V,{label:"其他申请",value:"other"})]),_:1},8,["modelValue"])]),_:1}),o(h,{label:"申请说明",prop:"description"},{default:a(()=>[o(O,{modelValue:g.description,"onUpdate:modelValue":e[2]||(e[2]=c=>g.description=c),type:"textarea",rows:4,placeholder:"请详细说明您的申请理由和需要的权限..."},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},Be=q(ke,[["__scopeId","data-v-333ebcec"]]);export{Be as default};
