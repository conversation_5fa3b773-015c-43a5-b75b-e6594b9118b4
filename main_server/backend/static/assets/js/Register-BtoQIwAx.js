import{_ as L}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                   *//* empty css                     *//* empty css                  *//* empty css                 */import{r as u,a as M,c as T,d as c,e as r,w as a,v as j,f as G,x as H,b as O,i as n,j as J,k as K,y as p,z as E,E as Q,p as i,A as z,B as A,C as U,m as W,n as N,t as Z,D as X,F as Y,s as f,G as ee,H as le,I as re}from"./index-BALd70Fs.js";const ae={class:"register-container"},se={class:"register-box"},oe={class:"back-to-login"},te={class:"success-content"},ue={__name:"Register",setup(ne){const $=O(),w=u(),y=u(!1),b=u(!1),h=u(!1),k=u(!1),g=u(""),v=u(""),d=u(""),V=u(!1),B=u(""),l=M({username:"",password:"",confirmPassword:"",fullName:"",phone:"",email:"",notes:""}),F={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:50,message:"用户名长度应在3-50个字符之间",trigger:"blur"},{pattern:/^[a-zA-Z0-9_-]+$/,message:"用户名只能包含英文字母、数字、下划线(_)和连字符(-)",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度至少6位",trigger:"blur"},{validator:(o,e,s)=>{/^[a-zA-Z0-9!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]+$/.test(e)?s():s(new Error("密码包含不允许的字符"))},trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认密码",trigger:"blur"},{validator:(o,e,s)=>{e!==l.password?s(new Error("两次输入的密码不一致")):s()},trigger:"blur"}],fullName:[{required:!0,message:"请输入姓名",trigger:"blur"},{min:2,max:50,message:"姓名长度应在2-50个字符之间",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号格式",trigger:"blur"}],email:[{validator:(o,e,s)=>{e&&!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(e)?s(new Error("请输入正确的邮箱格式")):s()},trigger:"blur"}],notes:[{max:500,message:"备注长度不能超过500个字符",trigger:"blur"}]},R=async()=>{if(!l.username||l.username.length<3){g.value="";return}b.value=!0,g.value="";try{const o=await Y(l.username);o.available?g.value="success":(g.value="error",f.warning(o.message))}catch(o){g.value="error",console.error("检查用户名失败:",o)}finally{b.value=!1}},S=async()=>{if(!l.phone||!/^1[3-9]\d{9}$/.test(l.phone)){v.value="";return}h.value=!0,v.value="";try{const o=await ee(l.phone);o.available?v.value="success":(v.value="error",f.warning(o.message))}catch(o){v.value="error",console.error("检查手机号失败:",o)}finally{h.value=!1}},q=async()=>{if(!l.email){d.value="success";return}if(!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(l.email)){d.value="";return}k.value=!0,d.value="";try{const o=await le(l.email);o.available?d.value="success":(d.value="error",f.warning(o.message))}catch(o){d.value="error",console.error("检查邮箱失败:",o)}finally{k.value=!1}},C=async()=>{var o,e;if(w.value)try{if(await w.value.validate(),g.value==="error"){f.error("用户名已存在，请更换");return}if(v.value==="error"){f.error("手机号已被注册，请更换");return}if(d.value==="error"){f.error("邮箱已被注册，请更换");return}y.value=!0;const s=await re({username:l.username,password:l.password,confirm_password:l.confirmPassword,full_name:l.fullName,phone:l.phone,email:l.email||null,notes:l.notes||null});s.success?(B.value=s.message,V.value=!0):f.error(s.message||"注册失败")}catch(s){console.error("注册失败:",s),(e=(o=s.response)==null?void 0:o.data)!=null&&e.detail?f.error(s.response.data.detail):f.error("注册失败，请稍后重试")}finally{y.value=!1}},P=()=>{$.push("/login")};return(o,e)=>{const s=Q,_=K,m=J,x=W,I=G,D=H;return n(),T("div",ae,[c("div",se,[e[10]||(e[10]=c("div",{class:"logo-section"},[c("h1",{class:"logo-text"},"OmniLink 全联通系统"),c("p",{class:"logo-subtitle"},"用户注册")],-1)),r(I,{ref_key:"registerFormRef",ref:w,model:l,rules:F,"label-width":"80px",size:"large",onSubmit:j(C,["prevent"])},{default:a(()=>[r(m,{label:"用户名",prop:"username"},{default:a(()=>[r(_,{modelValue:l.username,"onUpdate:modelValue":e[0]||(e[0]=t=>l.username=t),placeholder:"请输入用户名（3-50位字母、数字、下划线）",onBlur:R},{suffix:a(()=>[b.value?(n(),p(s,{key:0,class:"is-loading"},{default:a(()=>[r(i(z))]),_:1})):g.value==="success"?(n(),p(s,{key:1,style:{color:"#67c23a"}},{default:a(()=>[r(i(A))]),_:1})):g.value==="error"?(n(),p(s,{key:2,style:{color:"#f56c6c"}},{default:a(()=>[r(i(U))]),_:1})):E("",!0)]),_:1},8,["modelValue"])]),_:1}),r(m,{label:"密码",prop:"password"},{default:a(()=>[r(_,{modelValue:l.password,"onUpdate:modelValue":e[1]||(e[1]=t=>l.password=t),type:"password",placeholder:"请输入密码（至少6位，包含字母和数字）","show-password":""},null,8,["modelValue"])]),_:1}),r(m,{label:"确认密码",prop:"confirmPassword"},{default:a(()=>[r(_,{modelValue:l.confirmPassword,"onUpdate:modelValue":e[2]||(e[2]=t=>l.confirmPassword=t),type:"password",placeholder:"请再次输入密码","show-password":""},null,8,["modelValue"])]),_:1}),r(m,{label:"姓名",prop:"fullName"},{default:a(()=>[r(_,{modelValue:l.fullName,"onUpdate:modelValue":e[3]||(e[3]=t=>l.fullName=t),placeholder:"请输入真实姓名"},null,8,["modelValue"])]),_:1}),r(m,{label:"手机号",prop:"phone"},{default:a(()=>[r(_,{modelValue:l.phone,"onUpdate:modelValue":e[4]||(e[4]=t=>l.phone=t),placeholder:"请输入手机号",onBlur:S},{suffix:a(()=>[h.value?(n(),p(s,{key:0,class:"is-loading"},{default:a(()=>[r(i(z))]),_:1})):v.value==="success"?(n(),p(s,{key:1,style:{color:"#67c23a"}},{default:a(()=>[r(i(A))]),_:1})):v.value==="error"?(n(),p(s,{key:2,style:{color:"#f56c6c"}},{default:a(()=>[r(i(U))]),_:1})):E("",!0)]),_:1},8,["modelValue"])]),_:1}),r(m,{label:"邮箱",prop:"email"},{default:a(()=>[r(_,{modelValue:l.email,"onUpdate:modelValue":e[5]||(e[5]=t=>l.email=t),placeholder:"请输入邮箱（可选）",onBlur:q},{suffix:a(()=>[k.value?(n(),p(s,{key:0,class:"is-loading"},{default:a(()=>[r(i(z))]),_:1})):d.value==="success"?(n(),p(s,{key:1,style:{color:"#67c23a"}},{default:a(()=>[r(i(A))]),_:1})):d.value==="error"?(n(),p(s,{key:2,style:{color:"#f56c6c"}},{default:a(()=>[r(i(U))]),_:1})):E("",!0)]),_:1},8,["modelValue"])]),_:1}),r(m,{label:"备注",prop:"notes"},{default:a(()=>[r(_,{modelValue:l.notes,"onUpdate:modelValue":e[6]||(e[6]=t=>l.notes=t),type:"textarea",rows:3,placeholder:"请输入备注信息（可选）",maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),_:1}),r(m,null,{default:a(()=>[r(x,{type:"primary",size:"large",style:{width:"100%"},loading:y.value,onClick:C},{default:a(()=>[N(Z(y.value?"注册中...":"注册"),1)]),_:1},8,["loading"])]),_:1}),r(m,null,{default:a(()=>[c("div",oe,[e[9]||(e[9]=c("span",null,"已有账户？",-1)),r(x,{type:"text",onClick:P},{default:a(()=>e[8]||(e[8]=[N("返回登录",-1)])),_:1,__:[8]})])]),_:1})]),_:1},8,["model"])]),r(D,{modelValue:V.value,"onUpdate:modelValue":e[7]||(e[7]=t=>V.value=t),title:"注册成功",width:"400px","close-on-click-modal":!1,"close-on-press-escape":!1,"show-close":!1},{footer:a(()=>[r(x,{type:"primary",onClick:P},{default:a(()=>e[12]||(e[12]=[N("返回登录",-1)])),_:1,__:[12]})]),default:a(()=>[c("div",te,[r(s,{size:"60",style:{color:"#67c23a","margin-bottom":"16px"}},{default:a(()=>[r(i(X))]),_:1}),c("p",null,Z(B.value),1),e[11]||(e[11]=c("p",{style:{color:"#909399","font-size":"14px","margin-top":"16px"}}," 您的账户已提交审核，请等待管理员激活后即可正常使用系统。 ",-1))])]),_:1},8,["modelValue"])])}}},ge=L(ue,[["__scopeId","data-v-c6885e9b"]]);export{ge as default};
