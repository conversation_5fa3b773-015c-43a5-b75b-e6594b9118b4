import{_ as ne}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                   *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                     *//* empty css               *//* empty css               *//* empty css                 *//* empty css                *//* empty css                  *//* empty css                         */import{r as ie,g as le,a as re,a9 as de,o as ce,bN as ue,c as y,d as s,ab as _e,e as t,w as o,m as fe,t as r,a7 as ve,M as pe,ac as me,a2 as m,ad as be,cK as he,s as K,b as ye,i as u,n as c,E as ge,p as f,dd as we,y as b,D as ke,dq as Se,c$ as De,O as Be,P as Ce,a5 as Q,Q as Ee,d7 as Ve,L as He,dr as xe,d6 as N,cL as Ne,z as C,an as Te,dn as Ie,ah as Ue,ak as $e,a4 as Me}from"./index-CwbwN4Sv.js";import{a as Ae}from"./slaveServers-zp_qrs1V.js";const ze={class:"slave-server-detail"},Oe={class:"detail-header"},Le={class:"header-left"},Pe={class:"server-title"},Re={class:"header-right"},je={class:"detail-content"},Fe={class:"overview-cards"},qe={class:"card-content"},Ke={class:"card-icon cpu"},Qe={class:"card-info"},Ge={class:"card-value"},Je={class:"card-content"},We={class:"card-icon memory"},Xe={class:"card-info"},Ye={class:"card-value"},Ze={class:"card-content"},es={class:"card-icon disk"},ss={class:"card-info"},ts={class:"card-value"},os={class:"card-content"},as={class:"card-icon devices"},ns={class:"card-info"},is={class:"card-value"},ls={class:"card-header"},rs={class:"info-content"},ds={class:"info-item"},cs={class:"info-value"},us={class:"info-item"},_s={class:"info-value"},fs={class:"info-item"},vs={class:"info-value"},ps={class:"info-item"},ms={class:"info-label"},bs={class:"info-value"},hs={class:"info-item"},ys={class:"info-label"},gs={class:"info-value"},ws={class:"info-item"},ks={class:"info-value"},Ss={key:0,class:"info-item"},Ds={class:"info-value"},Bs={key:1,class:"offline-warning"},Cs={class:"card-header"},Es={class:"info-content"},Vs={class:"info-item"},Hs={class:"info-item"},xs={class:"info-value"},Ns={class:"info-item"},Ts={class:"info-value"},Is={class:"info-item"},Us={class:"info-value"},$s={class:"card-header"},Ms={class:"header-stats"},As={class:"usb-devices"},zs={key:0,class:"offline-device-warning"},Os={__name:"SlaveServerDetail",setup(Ls){const G=le(),T=ye(),w=ie(!1),I=G.params.id,n=re({server_info:{},system_info:{},virtualhere_info:{},usb_info:{},timestamp:null});let V=null;const i=de(()=>{var a,e;return((a=n.server_info)==null?void 0:a.status)==="online"&&((e=n.server_info)==null?void 0:e.is_online)===!0}),J=()=>{T.push("/device-center")},W=a=>i.value?"success":"danger",X=a=>a?i.value?"在线":a.offline_duration?`离线 (${a.offline_duration})`:"离线":"未知",H=a=>a?`${a.toFixed(1)}%`:"0%",Y=a=>{if(!a)return"N/A";const e=Math.floor(a/86400),_=Math.floor(a%86400/3600),k=Math.floor(a%3600/60);return`${e}天 ${_}小时 ${k}分钟`},U=a=>a?new Date(a).toLocaleString():"N/A",$=a=>a?`0x${a.toString(16).toUpperCase().padStart(4,"0")}`:"N/A",M=a=>{if(!a)return"未知";const e=a.toLowerCase();return e.includes("hub")?"HUB":e.includes("mouse")?"鼠标":e.includes("keyboard")?"键盘":e.includes("storage")?"存储":"设备"},Z=a=>{switch(M(a)){case"HUB":return"info";case"鼠标":return"success";case"键盘":return"warning";case"存储":return"danger";default:return""}},E=async()=>{w.value=!0;try{const e=(await Ae(I)).data;Object.assign(n,{server_info:{id:e.id,server_id:e.server_id,name:e.name,ip_address:e.ip_address,port:e.port,vh_port:e.vh_port,status:e.status,is_online:e.is_online,offline_duration:e.offline_duration,last_seen:e.last_seen,description:e.description,created_at:e.created_at,device_count:e.device_count,hardware_uuid:e.hardware_uuid},system_info:{cpu_usage:"N/A",memory_usage:"N/A",disk_usage:"N/A"},virtualhere_info:{status:e.is_online?"running":"stopped",port:e.vh_port,version:"N/A"},usb_info:{device_count:e.device_count||0,devices:[]},timestamp:new Date().toISOString()}),console.log("获取到的基础信息:",e),console.log("合并后的服务器详情:",n),w.value=!1}catch(a){console.error("获取服务器详情失败:",a),K.error("获取服务器详情失败"),Object.assign(n,{server_info:{name:"OmniLink-Slave-Server",ip_address:"**********",port:8891,vh_port:7575,status:"online",uptime:86400,boot_time:new Date(Date.now()-864e5).toISOString()},system_info:{cpu:{percent:15.2},memory:{percent:45.8},disk:{percent:23.1}},virtualhere_info:{status:"running",port:7575,process_id:1234,version:"VirtualHere USB Server 4.3.3"},usb_info:{count:2,hub_count:2,device_count:0,devices:[{bus:1,address:1,vendor_id:7531,product_id:2,description:"USB Device 1d6b:0002"},{bus:2,address:1,vendor_id:7531,product_id:3,description:"USB Device 1d6b:0003"}]},timestamp:new Date().toISOString()}),w.value=!1}},ee=async a=>{try{if(a==="update"){T.push(`/device-center/slave-server/${I}/virtualhere`);return}await Me.confirm(`确定要执行 "${A(a)}" 操作吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),K.success(`${A(a)} 操作已执行`),setTimeout(()=>{E()},1e3)}catch{}},A=a=>({restart:"重启服务器","restart-vh":"重启VirtualHere","stop-vh":"停止VirtualHere","start-vh":"启动VirtualHere",update:"更新VirtualHere","wake-up":"唤醒服务器","force-restart":"强制重启","check-connection":"检查连接","remove-offline":"移除离线记录"})[a]||a;return ce(()=>{E(),V=setInterval(E,3e4)}),ue(()=>{V&&clearInterval(V)}),(a,e)=>{var L,P;const _=ge,k=fe,p=ve,v=Ee,se=Ce,te=pe,h=be,z=Te,x=Ne,O=he,g=$e,oe=Ue,ae=me;return u(),y("div",ze,[s("div",Oe,[s("div",Le,[t(k,{onClick:J,type:"text",class:"back-button"},{default:o(()=>[t(_,null,{default:o(()=>[t(f(we))]),_:1}),e[0]||(e[0]=c(" 返回列表 ",-1))]),_:1,__:[0]}),s("div",Pe,[s("h2",null,r(((L=n.server_info)==null?void 0:L.name)||"从服务器详情"),1),t(p,{type:W((P=n.server_info)==null?void 0:P.status),size:"large"},{default:o(()=>[t(_,{class:"status-icon"},{default:o(()=>[i.value?(u(),b(f(ke),{key:0})):(u(),b(f(Se),{key:1}))]),_:1}),c(" "+r(X(n.server_info)),1)]),_:1},8,["type"])])]),s("div",Re,[t(k,{onClick:E,loading:w.value,type:"primary",disabled:!i.value},{default:o(()=>[t(_,null,{default:o(()=>[t(f(De))]),_:1}),e[1]||(e[1]=c(" 刷新数据 ",-1))]),_:1,__:[1]},8,["loading","disabled"]),t(te,{onCommand:ee},{dropdown:o(()=>[t(se,null,{default:o(()=>[i.value?(u(),y(Q,{key:0},[t(v,{command:"restart"},{default:o(()=>e[3]||(e[3]=[c("重启服务器",-1)])),_:1,__:[3]}),t(v,{command:"restart-vh"},{default:o(()=>e[4]||(e[4]=[c("重启VirtualHere",-1)])),_:1,__:[4]}),t(v,{command:"stop-vh"},{default:o(()=>e[5]||(e[5]=[c("停止VirtualHere",-1)])),_:1,__:[5]}),t(v,{command:"start-vh"},{default:o(()=>e[6]||(e[6]=[c("启动VirtualHere",-1)])),_:1,__:[6]}),t(v,{divided:"",command:"update"},{default:o(()=>e[7]||(e[7]=[c("更新VirtualHere",-1)])),_:1,__:[7]})],64)):(u(),y(Q,{key:1},[t(v,{command:"wake-up"},{default:o(()=>e[8]||(e[8]=[c("唤醒服务器",-1)])),_:1,__:[8]}),t(v,{command:"force-restart"},{default:o(()=>e[9]||(e[9]=[c("强制重启",-1)])),_:1,__:[9]}),t(v,{divided:"",command:"check-connection"},{default:o(()=>e[10]||(e[10]=[c("检查连接",-1)])),_:1,__:[10]}),t(v,{command:"remove-offline"},{default:o(()=>e[11]||(e[11]=[c("移除离线记录",-1)])),_:1,__:[11]})],64))]),_:1})]),default:o(()=>[t(k,{type:"success"},{default:o(()=>[e[2]||(e[2]=c(" 管理操作 ",-1)),t(_,{class:"el-icon--right"},{default:o(()=>[t(f(Be))]),_:1})]),_:1,__:[2]})]),_:1})])]),_e((u(),y("div",je,[s("div",Fe,[t(h,{class:m(["overview-card",{"offline-card":!i.value}])},{default:o(()=>{var l,d;return[s("div",qe,[s("div",Ke,[t(_,null,{default:o(()=>[t(f(Ve))]),_:1})]),s("div",Qe,[s("div",Ge,r(i.value?H((d=(l=n.system_info)==null?void 0:l.cpu)==null?void 0:d.percent):"数据缺失"),1),e[12]||(e[12]=s("div",{class:"card-label"},"CPU使用率",-1))])])]}),_:1},8,["class"]),t(h,{class:m(["overview-card",{"offline-card":!i.value}])},{default:o(()=>{var l,d;return[s("div",Je,[s("div",We,[t(_,null,{default:o(()=>[t(f(He))]),_:1})]),s("div",Xe,[s("div",Ye,r(i.value?H((d=(l=n.system_info)==null?void 0:l.memory)==null?void 0:d.percent):"数据缺失"),1),e[13]||(e[13]=s("div",{class:"card-label"},"内存使用率",-1))])])]}),_:1},8,["class"]),t(h,{class:m(["overview-card",{"offline-card":!i.value}])},{default:o(()=>{var l,d;return[s("div",Ze,[s("div",es,[t(_,null,{default:o(()=>[t(f(xe))]),_:1})]),s("div",ss,[s("div",ts,r(i.value?H((d=(l=n.system_info)==null?void 0:l.disk)==null?void 0:d.percent):"数据缺失"),1),e[14]||(e[14]=s("div",{class:"card-label"},"磁盘使用率",-1))])])]}),_:1},8,["class"]),t(h,{class:m(["overview-card",{"offline-card":!i.value}])},{default:o(()=>{var l;return[s("div",os,[s("div",as,[t(_,null,{default:o(()=>[t(f(N))]),_:1})]),s("div",ns,[s("div",is,r(i.value?((l=n.usb_info)==null?void 0:l.count)||0:"离线状态"),1),e[15]||(e[15]=s("div",{class:"card-label"},"USB设备",-1))])])]}),_:1},8,["class"])]),t(O,{gutter:20,class:"detail-sections"},{default:o(()=>[t(x,{span:12},{default:o(()=>[t(h,{class:m(["info-card",{"offline-card":!i.value}])},{header:o(()=>[s("div",ls,[t(_,null,{default:o(()=>[t(f(Ie))]),_:1}),e[16]||(e[16]=s("span",null,"基础信息",-1))])]),default:o(()=>{var l,d,S,D,B,R,j,F,q;return[s("div",rs,[s("div",ds,[e[17]||(e[17]=s("span",{class:"info-label"},"服务器名称：",-1)),s("span",cs,r((l=n.server_info)==null?void 0:l.name),1)]),s("div",us,[e[18]||(e[18]=s("span",{class:"info-label"},"IP地址：",-1)),s("span",_s,r((d=n.server_info)==null?void 0:d.ip_address)+":"+r((S=n.server_info)==null?void 0:S.port),1)]),s("div",fs,[e[19]||(e[19]=s("span",{class:"info-label"},"VH端口：",-1)),s("span",vs,r((D=n.server_info)==null?void 0:D.vh_port),1)]),s("div",ps,[s("span",ms,r(i.value?"运行时间：":"离线前运行时间："),1),s("span",bs,r(Y((B=n.server_info)==null?void 0:B.uptime)||"未知"),1)]),s("div",hs,[s("span",ys,r(i.value?"启动时间：":"离线前启动时间："),1),s("span",gs,r(U((R=n.server_info)==null?void 0:R.boot_time)||"未知"),1)]),s("div",ws,[e[20]||(e[20]=s("span",{class:"info-label"},"最后心跳：",-1)),s("span",ks,r(U((j=n.server_info)==null?void 0:j.last_seen)),1)]),i.value?C("",!0):(u(),y("div",Ss,[e[21]||(e[21]=s("span",{class:"info-label"},"离线持续时间：",-1)),s("span",Ds,r(((F=n.server_info)==null?void 0:F.offline_duration)||"计算中..."),1)])),i.value?C("",!0):(u(),y("div",Bs,[t(z,{title:"服务器离线",description:`服务器当前处于离线状态${(q=n.server_info)!=null&&q.offline_duration?"，已离线 "+n.server_info.offline_duration:""}。部分功能可能无法使用。`,type:"warning",closable:!1,"show-icon":""},null,8,["description"])]))])]}),_:1},8,["class"])]),_:1}),t(x,{span:12},{default:o(()=>[t(h,{class:m(["info-card",{"offline-card":!i.value}])},{header:o(()=>[s("div",Cs,[t(_,null,{default:o(()=>[t(f(N))]),_:1}),e[22]||(e[22]=s("span",null,"VirtualHere状态",-1))])]),default:o(()=>{var l,d,S,D;return[s("div",Es,[s("div",Vs,[e[24]||(e[24]=s("span",{class:"info-label"},"运行状态：",-1)),i.value?(u(),b(p,{key:0,type:((l=n.virtualhere_info)==null?void 0:l.status)==="running"?"success":"danger"},{default:o(()=>{var B;return[c(r(((B=n.virtualhere_info)==null?void 0:B.status)==="running"?"运行中":"已停止"),1)]}),_:1},8,["type"])):(u(),b(p,{key:1,type:"danger"},{default:o(()=>e[23]||(e[23]=[c(" 无法连接 ",-1)])),_:1,__:[23]}))]),s("div",Hs,[e[25]||(e[25]=s("span",{class:"info-label"},"监听端口：",-1)),s("span",xs,r(i.value?(d=n.virtualhere_info)==null?void 0:d.port:"数据缺失"),1)]),s("div",Ns,[e[26]||(e[26]=s("span",{class:"info-label"},"进程ID：",-1)),s("span",Ts,r(i.value?((S=n.virtualhere_info)==null?void 0:S.process_id)||"N/A":"数据缺失"),1)]),s("div",Is,[e[27]||(e[27]=s("span",{class:"info-label"},"软件版本：",-1)),s("span",Us,r(i.value?(D=n.virtualhere_info)==null?void 0:D.version:"数据缺失"),1)])])]}),_:1},8,["class"])]),_:1})]),_:1}),t(O,{gutter:20,class:"detail-sections"},{default:o(()=>[t(x,{span:24},{default:o(()=>[t(h,{class:m(["info-card",{"offline-card":!i.value}])},{header:o(()=>[s("div",$s,[t(_,null,{default:o(()=>[t(f(N))]),_:1}),e[29]||(e[29]=s("span",null,"USB设备信息",-1)),s("div",Ms,[i.value?(u(),b(p,{key:0,size:"small"},{default:o(()=>{var l;return[c("总计: "+r(((l=n.usb_info)==null?void 0:l.count)||0),1)]}),_:1})):C("",!0),i.value?(u(),b(p,{key:1,size:"small",type:"warning"},{default:o(()=>{var l;return[c("HUB: "+r(((l=n.usb_info)==null?void 0:l.hub_count)||0),1)]}),_:1})):C("",!0),i.value?(u(),b(p,{key:2,size:"small",type:"success"},{default:o(()=>{var l;return[c("设备: "+r(((l=n.usb_info)==null?void 0:l.device_count)||0),1)]}),_:1})):(u(),b(p,{key:3,size:"small",type:"info"},{default:o(()=>e[28]||(e[28]=[c("历史记录",-1)])),_:1,__:[28]}))])])]),default:o(()=>{var l;return[s("div",As,[i.value?C("",!0):(u(),y("div",zs,[t(z,{title:"设备信息不可用",description:"从服务器离线，根据历史信息该服务器拥有以下设备。实时设备状态请等待服务器上线后查看。",type:"info",closable:!1,"show-icon":""})])),t(oe,{data:((l=n.usb_info)==null?void 0:l.devices)||[],stripe:"",class:m({"offline-table":!i.value})},{default:o(()=>[t(g,{prop:"bus",label:"总线",width:"80"}),t(g,{prop:"address",label:"地址",width:"80"}),t(g,{prop:"vendor_id",label:"VID",width:"100"},{default:o(({row:d})=>[s("code",null,r($(d.vendor_id)),1)]),_:1}),t(g,{prop:"product_id",label:"PID",width:"100"},{default:o(({row:d})=>[s("code",null,r($(d.product_id)),1)]),_:1}),t(g,{prop:"description",label:"设备描述","min-width":"200"}),t(g,{label:"设备类型",width:"120"},{default:o(({row:d})=>[t(p,{size:"small",type:Z(d.description)},{default:o(()=>[c(r(M(d.description)),1)]),_:2},1032,["type"])]),_:1})]),_:1},8,["data","class"])])]}),_:1},8,["class"])]),_:1})]),_:1})])),[[ae,w.value]])])}}},st=ne(Os,[["__scopeId","data-v-7fcf8969"]]);export{st as default};
