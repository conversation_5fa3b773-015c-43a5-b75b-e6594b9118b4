import{_ as Gs}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                   *//* empty css                    *//* empty css                    *//* empty css               *//* empty css                *//* empty css                       *//* empty css                 *//* empty css                   *//* empty css                    *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                 *//* empty css                *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css                     *//* empty css                  *//* empty css                  */import{an as I,u as Hs,aN as ft,r as y,a as R,o as Ks,s as d,c as x,z,e as s,w as n,dc as Ys,ai as Qs,x as Xs,a5 as pe,i as g,m as ea,n as m,cZ as ta,c_ as sa,d as a,f as aa,j as la,k as na,t as u,ac as oa,af as ra,a7 as ia,cM as da,y as U,p as W,cX as _t,E as ua,cR as vt,a6 as Ve,cY as ca,c$ as ma,dy as pa,cQ as fa,ad as _a,ae as va,bB as ya,q as ga,a9 as yt,B as ba,aa as wa,aj as ha,bZ as Ca,dz as Sa,ag as Ta,ah as ka,cP as Va,cK as xa,cL as $a,cO as Da,du as za,dv as Ma,a4 as q,Y as Ua,d6 as Ra,dA as Ea,a0 as Oa,L as Aa,$ as Ba,dr as Ia}from"./index-msvS5Uas.js";/* empty css                             */import{a as La,b as Pa,u as xe,s as Fa,c as Na,d as ja,e as Wa,f as Ja}from"./systemSettings-CjckMlpd.js";function qa(){return I({url:"/api/v1/system/cache-info",method:"get"})}function gt(L){return I({url:"/api/v1/system/clear-cache",method:"post",params:{cache_type:L}})}function Za(){return gt("all")}function Ga(){try{return localStorage.clear(),sessionStorage.clear(),"caches"in window&&caches.keys().then(L=>{L.forEach($e=>{caches.delete($e)})}),Promise.resolve({success:!0,message:"Web页面缓存清除成功"})}catch(L){return Promise.reject({success:!1,message:`Web页面缓存清除失败: ${L.message}`})}}function Ha(){const L=new URL(window.location);L.searchParams.set("_refresh",Date.now()),window.location.href=L.toString()}const Ka={key:0,class:"system-settings-container"},Ya={key:1,class:"access-denied-container"},Qa={key:2,class:"login-management-container"},Xa={class:"settings-section"},el={class:"standard-container"},tl={class:"card-header"},sl={class:"tunnel-action-buttons"},al={class:"standard-container"},ll={class:"card-header"},nl={class:"connection-status-grid"},ol={class:"connection-header"},rl={class:"connection-details"},il={class:"detail-item"},dl={class:"value"},ul={class:"detail-item"},cl={class:"value"},ml={class:"detail-item"},pl={class:"value"},fl={class:"connection-actions"},_l={class:"card-header"},vl={class:"test-controls"},yl={class:"test-suite"},gl={class:"test-section"},bl={class:"test-header"},wl={class:"test-results"},hl={class:"metric-grid"},Cl={class:"metric-item"},Sl={class:"metric-value"},Tl={class:"metric-item"},kl={class:"metric-value"},Vl={class:"metric-item"},xl={class:"metric-value"},$l={class:"metric-item"},Dl={class:"metric-value"},zl={key:0,class:"test-progress"},Ml={class:"progress-text"},Ul={class:"test-section"},Rl={class:"test-header"},El={class:"test-results"},Ol={class:"metric-grid"},Al={class:"metric-item"},Bl={class:"metric-value"},Il={class:"metric-item"},Ll={class:"metric-value"},Pl={class:"metric-item"},Fl={class:"metric-value"},Nl={class:"metric-item"},jl={class:"metric-value"},Wl={key:0,class:"test-progress"},Jl={class:"progress-text"},ql={class:"test-section"},Zl={class:"test-header"},Gl={class:"concurrent-controls"},Hl={class:"test-results"},Kl={class:"metric-grid"},Yl={class:"metric-item"},Ql={class:"metric-value"},Xl={class:"metric-item"},en={class:"metric-value"},tn={class:"metric-item"},sn={class:"metric-value"},an={class:"metric-item"},ln={class:"metric-value"},nn={key:0,class:"test-progress"},on={class:"progress-text"},rn={class:"test-section"},dn={class:"test-header"},un={class:"stability-controls"},cn={class:"test-results"},mn={class:"metric-grid"},pn={class:"metric-item"},fn={class:"metric-value"},_n={class:"metric-item"},vn={class:"metric-value"},yn={class:"metric-item"},gn={class:"metric-value"},bn={class:"metric-item"},wn={class:"metric-value"},hn={key:0,class:"test-progress"},Cn={class:"progress-text"},Sn={class:"test-section"},Tn={class:"test-header"},kn={class:"compression-controls"},Vn={class:"compression-config"},xn={class:"config-row"},$n={class:"config-item"},Dn={class:"config-item"},zn={class:"config-item"},Mn={class:"config-item"},Un={class:"config-row"},Rn={class:"config-item"},En={key:0,class:"config-item"},On={key:1,class:"config-item"},An={class:"test-results"},Bn={class:"metric-grid"},In={class:"metric-item"},Ln={class:"metric-value"},Pn={class:"metric-item"},Fn={class:"metric-value"},Nn={class:"metric-item"},jn={class:"metric-value"},Wn={class:"metric-item"},Jn={class:"metric-value"},qn={class:"metric-item"},Zn={class:"metric-value"},Gn={class:"metric-item"},Hn={class:"metric-value"},Kn={class:"metric-item"},Yn={class:"metric-value"},Qn={class:"metric-item"},Xn={class:"metric-value"},eo={key:0,class:"test-progress"},to={class:"progress-text"},so={key:1,class:"compression-charts"},ao={class:"chart-container"},lo={class:"chart-placeholder"},no={class:"chart-label"},oo={class:"chart-value"},ro={class:"card-header"},io={class:"report-actions"},uo={class:"test-report"},co={class:"report-summary"},mo={class:"summary-grid"},po={class:"summary-item"},fo={class:"value"},_o={class:"summary-item"},vo={class:"value"},yo={class:"summary-item"},go={class:"value"},bo={class:"report-details"},wo={class:"standard-container"},ho={class:"card-header"},Co={class:"standard-container"},So={class:"cache-info-section"},To={class:"cache-name"},ko={class:"cache-description"},Vo={class:"cache-size"},xo={class:"cache-actions",style:{"margin-top":"20px"}},$o={class:"algorithm-option"},Do={class:"algorithm-desc"},zo={key:0,class:"form-help"},Mo={class:"form-tip"},Uo={class:"preview-content"},Ro={class:"preview-item"},Eo={class:"preview-value"},Oo={class:"preview-item"},Ao={class:"preview-value"},Bo={class:"preview-item"},Io={class:"preview-value"},Lo={class:"dialog-footer"},Po={class:"form-tip"},Fo={class:"dialog-footer"},No={class:"preview-section"},jo={class:"network-impact"},Wo={class:"impact-item"},Jo={class:"impact-value"},qo={class:"impact-item"},Zo={class:"impact-value"},Go={class:"impact-item"},Ho={class:"impact-value"},Ko={class:"dialog-footer"},Yo={__name:"SystemSettings",setup(L){const $e=()=>I.get("/api/v1/compression/configs"),Fe=t=>I.post("/api/v1/compression/configs",t),bt=(t,e)=>I.put(`/api/v1/compression/configs/${t}`,e),wt=t=>I.delete(`/api/v1/compression/configs/${t}`),ht=t=>I.post(`/api/v1/compression/configs/${t}/activate`),Ct=t=>I.post(`/api/v1/compression/configs/${t}/deactivate`),St=()=>I.get("/api/v1/compression/status"),Tt=()=>I.get("/api/v1/compression/algorithms"),kt=()=>I.get("/api/v1/compression/recommendations"),Ne=Hs(),je=ft(()=>Ne.canAccessSystemSettings&&Ne.getPermissionLevel<=1),We=y("client"),te=y(!1),se=y(!1),Je=R({system_name:"",system_version:""}),ae=R({client_download_url:""}),le=R({clientSettings:!1}),De=y([]),fe=y({}),Vt=y({}),_e=y([]),$=y(null),ze=y(!1),ve=y(!1),qe=y(!1),i=R({latency:{running:!1,progress:0,currentStep:""},bandwidth:{running:!1,progress:0,currentStep:""},concurrent:{running:!1,progress:0,currentStep:""},stability:{running:!1,progress:0,currentStep:""},compression:{running:!1,progress:0,currentStep:""}}),r=R({latency:{average:null,min:null,max:null,packetLoss:null},bandwidth:{download:null,upload:null,totalData:null,duration:null},concurrent:{successful:null,failed:null,avgResponseTime:null,successRate:null},stability:{elapsed:null,disconnections:null,avgLatency:null,score:null},compression:{originalSize:null,compressedSize:null,compressionRatio:null,transferSpeed:null,compressionTime:null,decompressionTime:null,totalTransferred:null,bandwidthSaved:null,chartData:null}}),Me=R({connections:10}),Ue=R({duration:"15"}),w=R({dataSize:"10",concurrency:"10",duration:"30",dataType:"json",enableCompression:!0,algorithm:"snappy",compressionLevel:"medium"}),D=R({startTime:null,endTime:null,overallScore:0,details:[]}),xt=ft(()=>r.latency.average!==null||r.bandwidth.download!==null||r.concurrent.successful!==null||r.stability.score!==null),P=R({name:"",tunnel_type:"",is_active:!1}),ne=y(""),M=R({id:null,name:"",tunnel_type:"",is_active:!1,force_disabled:!1}),oe=y(""),re=y(!1),Z=y(!1),Ze=y(!1),ie=y(!1),Re=y(!1),ye=y(!1),ge=y(!1),be=y(!1),j=y(null),O=y([]),J=y(null),G=y([]),we=y(null),he=y([]),C=R({name:"",algorithm:"",level:"medium",stream_type:"",max_concurrent_users:500,bandwidth_limit_mbps:3,auto_fallback:!0,is_default:!1}),S=R({id:null,name:"",algorithm:"",level:"medium",stream_type:"",max_concurrent_users:500,bandwidth_limit_mbps:3,auto_fallback:!0,is_default:!1}),H=y(null),de=y(null),$t=y([]),Ee=y({bandwidth_saved:0,latency_increase:0,cpu_usage:0}),Ge={name:[{required:!0,message:"请输入配置名称",trigger:"blur"}],algorithm:[{required:!0,message:"请选择压缩算法",trigger:"change"}],level:[{required:!0,message:"请选择压缩级别",trigger:"change"}]},Oe=y(),He=y(),Ke=y([]),Ae=y(!1),F=R({all:!1,web:!1,server_cache:!1,redis_cache:!1,query_cache:!1,compression_cache:!1,tunnel_cache:!1,api_cache:!1}),Ye=async()=>{try{const t=await La();t.success&&t.data.forEach(e=>{e.key==="system_name"?Je.system_name=e.value:e.key==="system_version"?Je.system_version=e.value:e.key==="client_download_url"&&(ae.client_download_url=e.value)})}catch{d.error("加载设置失败")}},Dt=async(t,e)=>{try{await Ja(t,{value:e}),d.success("设置更新成功")}catch{d.error("设置更新失败")}},zt=async()=>{try{le.clientSettings=!0,await Dt("client_download_url",ae.client_download_url),d.success("客户端设置保存成功")}catch{d.error("客户端设置保存失败")}finally{le.clientSettings=!1}},Mt=async()=>{try{await Ye(),d.success("客户端设置已重置")}catch{d.error("重置设置失败")}},ue=async()=>{try{const t=await Pa();t.success&&(De.value=t.data)}catch{d.error("加载内网穿透配置失败")}},Ut=async()=>{try{let t={};ne.value&&(t=JSON.parse(ne.value)),await Wa({...P,config_data:t}),d.success("内网穿透配置创建成功"),te.value=!1,Object.assign(P,{name:"",tunnel_type:"",is_active:!1}),ne.value="",await ue()}catch{d.error("创建内网穿透配置失败")}},Rt=async t=>{try{await xe(t.id,{is_active:t.is_active}),d.success("配置状态更新成功")}catch{d.error("配置状态更新失败"),t.is_active=!t.is_active}},Et=async t=>{try{await Fa(t.id),d.success(`${t.tunnel_type} 启动成功`),t.is_running=!0,t.status="running"}catch{d.error("启动内网穿透失败")}},Ot=async t=>{try{await Na(t.id),d.success(`${t.tunnel_type} 停止成功`),t.is_running=!1,t.status="stopped"}catch{d.error("停止内网穿透失败")}},At=t=>{Object.assign(M,{id:t.id,name:t.name,tunnel_type:t.tunnel_type,is_active:t.is_active,force_disabled:t.force_disabled||!1}),oe.value=JSON.stringify(t.config_data,null,2),se.value=!0},Bt=async()=>{try{let t={};oe.value&&(t=JSON.parse(oe.value)),await xe(M.id,{name:M.name,config_data:t,is_active:M.is_active,force_disabled:M.force_disabled}),d.success("内网穿透配置更新成功"),se.value=!1,await ue()}catch{d.error("更新内网穿透配置失败")}},It=async t=>{try{fe.value[t.id]=!0;const e=await Qe(t);Vt.value[t.id]=e,e.success?d.success(`${t.name} 连接测试成功！延迟: ${e.latency}ms`):d.error(`${t.name} 连接测试失败: ${e.error}`)}catch(e){d.error(`连接测试失败: ${e.message}`)}finally{fe.value[t.id]=!1}},Qe=async t=>{const e=Be(t);try{const o=await Lt(e.host,e.port);let c=null;it(t.tunnel_type)&&(c=await Pt(e.webUrl));const p=await Ft(e);return{success:o.success&&p.success,latency:o.latency,portConnectivity:o,httpResponse:c,dataTransfer:p,timestamp:new Date().toISOString()}}catch(o){return{success:!1,error:o.message,timestamp:new Date().toISOString()}}},Be=t=>{const e={host:"skfirefly.cn",name:t.name,type:t.tunnel_type};switch(t.tunnel_type.toLowerCase()){case"frp":return{...e,port:28e3,webUrl:"http://skfirefly.cn:28000"};case"linker":return{...e,port:28005,webUrl:null};default:return{...e,port:8080,webUrl:null}}},Lt=async(t,e)=>{const o=Date.now();try{const c=await fetch("/api/v1/system/test-connection",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({host:t,port:e}),timeout:5e3}),p=Date.now()-o;return c.ok?{success:!0,latency:p,status:"connected"}:{success:!1,latency:p,status:"failed",error:"Connection refused"}}catch(c){return{success:!1,latency:Date.now()-o,status:"timeout",error:c.message}}},Pt=async t=>{if(!t)return null;try{const e=await fetch(t,{method:"HEAD",timeout:3e3,mode:"no-cors"});return{success:!0,status:e.status,statusText:e.statusText}}catch(e){return{success:!1,error:e.message}}},Ft=async t=>{try{const e="test-data-"+Date.now(),o=Date.now();await new Promise(p=>setTimeout(p,100));const c=Date.now()-o;return{success:!0,transferTime:c,dataSize:e.length,throughput:(e.length/c*1e3).toFixed(2)+" bytes/s"}}catch(e){return{success:!1,error:e.message}}},Xe=async()=>{ze.value=!0;try{await ue(),_e.value=De.value.filter(t=>t.is_active);for(const t of _e.value)await Nt(t);d.success("连接状态刷新完成")}catch{d.error("刷新连接状态失败")}finally{ze.value=!1}},Nt=async t=>{try{const e=await Qe(t);t.service_status=e.success?"运行中":"连接失败",t.last_check=new Date().toISOString(),t.uptime=e.success?"正常运行":"0分钟"}catch{t.service_status="检查失败",t.last_check=new Date().toISOString(),t.uptime="0分钟"}},jt=t=>{switch(t){case"运行中":return"success";case"连接失败":return"danger";case"检查失败":return"warning";default:return"info"}},Wt=t=>t||"未知",et=t=>t.service_status==="运行中",Jt=t=>{$.value=t,d.info(`已选择 ${t.name} 进行性能测试`)},qt=async()=>{if(!$.value){d.warning("请先选择要测试的配置");return}ve.value=!0,D.startTime=new Date().toISOString();try{await tt(),await at(),await nt(),Yt(),d.success("全面测试完成")}catch{d.error("测试过程中出现错误")}finally{ve.value=!1,D.endTime=new Date().toISOString()}},Zt=()=>{i.latency.running=!1,i.bandwidth.running=!1,i.concurrent.running=!1,i.stability.running=!1,ve.value=!1,qe.value=!1,d.info("已停止所有测试")},tt=async()=>{if($.value){i.latency.running=!0,i.latency.progress=0,i.latency.currentStep="开始延迟测试...";try{const t=[];for(let o=0;o<10;o++){i.latency.currentStep=`第 ${o+1}/10 次 ping 测试`,i.latency.progress=Math.round(o/10*100);const c=await st($.value);c.success&&t.push(c.latency),await new Promise(p=>setTimeout(p,500))}t.length>0&&(r.latency.average=Math.round(t.reduce((o,c)=>o+c,0)/t.length),r.latency.min=Math.min(...t),r.latency.max=Math.max(...t),r.latency.packetLoss=Math.round((10-t.length)/10*100)),i.latency.progress=100,i.latency.currentStep="延迟测试完成"}catch{d.error("延迟测试失败")}finally{i.latency.running=!1}}},st=async t=>{const e=Date.now();try{const o=Be(t),c=await fetch("/api/v1/system/test-connection",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({host:o.host,port:o.port}),timeout:3e3}),p=Date.now()-e;return{success:c.ok,latency:p}}catch{return{success:!1,latency:-1}}},at=async()=>{if($.value){i.bandwidth.running=!0,i.bandwidth.progress=0,i.bandwidth.currentStep="开始带宽测试...";try{const t=Date.now();i.bandwidth.currentStep="测试下载速度...",i.bandwidth.progress=25;const e=await lt("download");i.bandwidth.currentStep="测试上传速度...",i.bandwidth.progress=75;const o=await lt("upload"),c=Math.round((Date.now()-t)/1e3);r.bandwidth.download=e.toFixed(2),r.bandwidth.upload=o.toFixed(2),r.bandwidth.totalData=((e+o)*c/8).toFixed(2),r.bandwidth.duration=c,i.bandwidth.progress=100,i.bandwidth.currentStep="带宽测试完成"}catch{d.error("带宽测试失败")}finally{i.bandwidth.running=!1}}},lt=async t=>(await new Promise(e=>setTimeout(e,2e3)),Math.random()*50+10),nt=async()=>{if($.value){i.concurrent.running=!0,i.concurrent.progress=0,i.concurrent.currentStep="开始并发连接测试...";try{const t=Me.connections,e=[],o=[];i.concurrent.currentStep=`创建 ${t} 个并发连接...`;for(let h=0;h<t;h++)e.push(Gt($.value)),i.concurrent.progress=Math.round(h/t*50),await new Promise(k=>setTimeout(k,50));i.concurrent.currentStep="等待连接结果...";const c=await Promise.allSettled(e);let p=0,f=0,T=0;c.forEach(h=>{h.status==="fulfilled"&&h.value.success?(p++,T+=h.value.responseTime):f++}),r.concurrent.successful=p,r.concurrent.failed=f,r.concurrent.avgResponseTime=p>0?Math.round(T/p):0,r.concurrent.successRate=Math.round(p/t*100),i.concurrent.progress=100,i.concurrent.currentStep="并发测试完成"}catch{d.error("并发测试失败")}finally{i.concurrent.running=!1}}},Gt=async t=>{const e=Date.now();try{const o=Be(t),c=await fetch("/api/v1/system/test-connection",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({host:o.host,port:o.port}),timeout:5e3}),p=Date.now()-e;return{success:c.ok,responseTime:p}}catch{return{success:!1,responseTime:Date.now()-e}}},Ht=async()=>{if(!$.value)return;i.stability.running=!0,i.stability.progress=0,i.stability.currentStep="开始稳定性测试...";const t=parseInt(Ue.duration)*60*1e3,e=Date.now();let o=0;const c=[];try{const p=setInterval(async()=>{if(!i.stability.running){clearInterval(p);return}const f=Date.now()-e,T=Math.min(Math.round(f/t*100),100);i.stability.progress=T,i.stability.currentStep=`稳定性测试进行中... ${Math.round(f/1e3)}s / ${Math.round(t/1e3)}s`;const h=await st($.value);if(h.success?c.push(h.latency):o++,r.stability.elapsed=Kt(f),r.stability.disconnections=o,r.stability.avgLatency=c.length>0?Math.round(c.reduce((k,v)=>k+v,0)/c.length):0,f>=t){clearInterval(p);const k=c.length/(c.length+o),v=r.stability.avgLatency;let V=Math.round(k*70);v<50?V+=30:v<100?V+=20:v<200&&(V+=10),r.stability.score=Math.min(V,100),i.stability.progress=100,i.stability.currentStep="稳定性测试完成",i.stability.running=!1}},2e3)}catch{d.error("稳定性测试失败"),i.stability.running=!1}},Kt=t=>{const e=Math.floor(t/1e3),o=Math.floor(e/60),c=Math.floor(o/60);return c>0?`${c}小时${o%60}分钟`:o>0?`${o}分钟${e%60}秒`:`${e}秒`},Yt=()=>{D.details=[];let t=0,e=0;if(r.latency.average!==null){const o=r.latency.average<50?90:r.latency.average<100?75:r.latency.average<200?60:40;D.details.push({testType:"延迟测试",result:`平均 ${r.latency.average}ms，丢包率 ${r.latency.packetLoss}%`,score:o,status:o>=70?"passed":"failed",notes:r.latency.average<100?"延迟表现良好":"延迟较高，可能影响实时性"}),t+=o,e++}if(r.bandwidth.download!==null){const o=parseFloat(r.bandwidth.download)>20?90:parseFloat(r.bandwidth.download)>10?75:parseFloat(r.bandwidth.download)>5?60:40;D.details.push({testType:"带宽测试",result:`下载 ${r.bandwidth.download}Mbps，上传 ${r.bandwidth.upload}Mbps`,score:o,status:o>=70?"passed":"failed",notes:parseFloat(r.bandwidth.download)>10?"带宽充足":"带宽较低"}),t+=o,e++}if(r.concurrent.successful!==null){const o=r.concurrent.successRate>90?90:r.concurrent.successRate>80?75:r.concurrent.successRate>70?60:40;D.details.push({testType:"并发测试",result:`成功率 ${r.concurrent.successRate}%，平均响应 ${r.concurrent.avgResponseTime}ms`,score:o,status:o>=70?"passed":"failed",notes:r.concurrent.successRate>85?"并发性能优秀":"并发性能需要优化"}),t+=o,e++}r.stability.score!==null&&(D.details.push({testType:"稳定性测试",result:`评分 ${r.stability.score}/100，中断 ${r.stability.disconnections} 次`,score:r.stability.score,status:r.stability.score>=70?"passed":"failed",notes:r.stability.score>85?"连接非常稳定":"连接稳定性一般"}),t+=r.stability.score,e++),D.overallScore=e>0?Math.round(t/e):0},Ie=t=>t?new Date(t).toLocaleString():"--",Qt=()=>{var p;const t={config:$.value,testResults:r,report:D,timestamp:new Date().toISOString()},e=new Blob([JSON.stringify(t,null,2)],{type:"application/json"}),o=URL.createObjectURL(e),c=document.createElement("a");c.href=o,c.download=`datalink-test-report-${(p=$.value)==null?void 0:p.name}-${new Date().toISOString().split("T")[0]}.json`,c.click(),URL.revokeObjectURL(o),d.success("测试报告已导出")},Xt=()=>{Object.keys(r.latency).forEach(t=>r.latency[t]=null),Object.keys(r.bandwidth).forEach(t=>r.bandwidth[t]=null),Object.keys(r.concurrent).forEach(t=>r.concurrent[t]=null),Object.keys(r.stability).forEach(t=>r.stability[t]=null),Object.keys(r.compression).forEach(t=>r.compression[t]=null),D.details=[],D.overallScore=0,D.startTime=null,D.endTime=null,d.success("测试结果已清除")},es=async()=>{if(!$.value){d.warning("请先选择要测试的配置");return}i.compression.running=!0,i.compression.progress=0,i.compression.currentStep="初始化数据压缩测试...";try{i.compression.currentStep="生成测试数据...",i.compression.progress=10;const t=ss(w.dataType,parseInt(w.dataSize));i.compression.currentStep="执行压缩性能测试...",i.compression.progress=30;const e=await ls(t);i.compression.currentStep="执行数据传输测试...",i.compression.progress=60;const o=await ns(e.compressedData);i.compression.currentStep="计算测试结果...",i.compression.progress=90,is(t,e,o),i.compression.progress=100,i.compression.currentStep="数据压缩测试完成",d.success("数据压缩测试完成")}catch(t){d.error(`数据压缩测试失败: ${t.message}`)}finally{i.compression.running=!1}},ts=()=>{i.compression.running=!1,d.info("数据压缩测试已停止")},ss=(t,e)=>{const o=e*1024*1024;let c="";switch(t){case"json":c=Le(o);break;case"binary":c=ot(o);break;case"text":c=rt(o);break;case"mixed":c=as(o);break;default:c=Le(o)}return{type:t,size:o,data:c,timestamp:new Date().toISOString()}},Le=t=>{const e={system:{version:"1.0.0",environment:"production",features:["compression","encryption","monitoring"],settings:{maxConnections:1e3,timeout:3e4,retryAttempts:3,enableLogging:!0}},users:[],devices:[],tunnels:[],logs:[]};let o=JSON.stringify(e);for(;o.length<t;)e.users.push({id:Math.random().toString(36).substr(2,9),name:`User_${Math.random().toString(36).substr(2,8)}`,email:`user${Math.random().toString(36).substr(2,5)}@example.com`,role:["admin","user","guest"][Math.floor(Math.random()*3)],permissions:["read","write","execute"],lastLogin:new Date().toISOString(),settings:{theme:"dark",language:"zh-CN",notifications:!0}}),e.devices.push({id:Math.random().toString(36).substr(2,9),name:`Device_${Math.random().toString(36).substr(2,8)}`,type:["usb","network","serial"][Math.floor(Math.random()*3)],status:["online","offline","error"][Math.floor(Math.random()*3)],lastSeen:new Date().toISOString(),properties:{vendor:"Generic",model:"Model_"+Math.random().toString(36).substr(2,6),version:"1.0.0"}}),o=JSON.stringify(e);return o.substring(0,t)},ot=t=>{const e=new ArrayBuffer(t),o=new Uint8Array(e);for(let c=0;c<t;c++)o[c]=Math.floor(Math.random()*256);return btoa(String.fromCharCode.apply(null,o))},rt=t=>{const e=["[INFO] System startup completed successfully","[WARN] High memory usage detected: 85%","[ERROR] Connection timeout to remote server","[DEBUG] Processing user request: GET /api/v1/users","[INFO] Database connection established","[WARN] SSL certificate expires in 30 days","[ERROR] Failed to authenticate user session","[INFO] Backup process completed successfully","[DEBUG] Cache hit ratio: 92%","[WARN] Disk space usage: 78%"];let o="";for(;o.length<t;){const c=new Date().toISOString(),p=e[Math.floor(Math.random()*e.length)],f=Math.random().toString(36).substr(2,16),T=`${c} [${f}] ${p}
`;o+=T}return o.substring(0,t)},as=t=>{const e=Le(Math.floor(t*.4)),o=ot(Math.floor(t*.3)),c=rt(Math.floor(t*.3));return JSON.stringify({json:e,binary:o,text:c,metadata:{type:"mixed",timestamp:new Date().toISOString(),components:["json","binary","text"]}})},ls=async t=>{const e=Date.now();try{const o=await fetch("/api/v1/system/test-compression",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({data:t.data,algorithm:w.algorithm,level:w.compressionLevel,enableCompression:w.enableCompression})});if(!o.ok)throw new Error("压缩测试API调用失败");const c=await o.json(),p=Date.now()-e;return{originalSize:t.size,compressedSize:c.compressedSize,compressedData:c.compressedData,compressionTime:p,algorithm:w.algorithm,compressionRatio:((t.size-c.compressedSize)/t.size*100).toFixed(2)}}catch{throw new Error("压缩测试服务不可用")}},ns=async t=>{const e=Date.now(),o=parseInt(w.concurrency),c=w.duration==="continuous"?60:parseInt(w.duration);let p=0,f=0;const T=[];for(let h=0;h<o;h++)T.push(os(t,c));try{(await Promise.allSettled(T)).forEach(V=>{V.status==="fulfilled"&&(p+=V.value.bytesTransferred,f+=V.value.transferCount)});const k=Date.now()-e,v=p/1024/1024/(k/1e3);return{totalTransferred:p,transferCount:f,transferSpeed:v.toFixed(2),totalTime:k,concurrency:o}}catch(h){throw new Error(`传输测试失败: ${h.message}`)}},os=async(t,e)=>{const o=Date.now(),c=o+e*1e3;let p=0,f=0;for(;Date.now()<c&&i.compression.running;)try{const T=await rs(t);p+=T.bytes,f++;const h=Date.now()-o,k=Math.min(h/(e*1e3)*100,100);i.compression.progress=Math.max(i.compression.progress,60+k*.3),await new Promise(v=>setTimeout(v,10))}catch(T){console.error("单次传输失败:",T);break}return{bytesTransferred:p,transferCount:f}},rs=async t=>{const e=Math.random()*50+10;return await new Promise(o=>setTimeout(o,e)),{bytes:t.length,latency:e,success:!0}},is=(t,e,o)=>{r.compression.originalSize=Ce(t.size),r.compression.compressedSize=Ce(e.compressedSize),r.compression.compressionRatio=e.compressionRatio,r.compression.transferSpeed=o.transferSpeed,r.compression.compressionTime=e.compressionTime,r.compression.decompressionTime=Math.floor(e.compressionTime*.3),r.compression.totalTransferred=Ce(o.totalTransferred);const c=t.size*o.transferCount,p=o.totalTransferred,f=Ce(c-p);r.compression.bandwidthSaved=f,r.compression.chartData=[{algorithm:e.algorithm.toUpperCase(),ratio:parseFloat(e.compressionRatio),size:e.compressedSize}]},Ce=t=>{if(t===0)return"0 B";const e=1024,o=["B","KB","MB","GB","TB"],c=Math.floor(Math.log(t)/Math.log(e));return parseFloat((t/Math.pow(e,c)).toFixed(2))+" "+o[c]},ds=async t=>{try{await q.confirm(`确定要升级 ${t.name} 的内网穿透工具吗？这将下载并安装最新版本。`,"升级确认",{confirmButtonText:"确定升级",cancelButtonText:"取消",type:"warning"}),d.info("升级功能正在开发中，敬请期待")}catch{}},us=async t=>{try{const e=t.force_disabled?"解锁":"锁定";await q.confirm(`确定要${e} ${t.name} 吗？${t.force_disabled?"解锁后可以正常启用配置。":"锁定后将完全禁止启动，用于安全防护。"}`,`${e}确认`,{confirmButtonText:`确定${e}`,cancelButtonText:"取消",type:t.force_disabled?"success":"warning"}),t.force_disabled=!t.force_disabled,await xe(t.id,{force_disabled:t.force_disabled}),d.success(`${t.name} 已${e}`),t.force_disabled&&t.is_active&&(t.is_active=!1,await xe(t.id,{is_active:!1}))}catch(e){e!=="cancel"&&(d.error("操作失败"),t.force_disabled=!t.force_disabled)}},cs=async t=>{try{await q.confirm("确定要删除此配置吗？","确认删除",{type:"warning"}),await ja(t.id),d.success("配置删除成功"),await ue()}catch(e){e!=="cancel"&&d.error("删除配置失败")}},ms=t=>({headscale:"primary",netbird:"success",zerotier:"warning",nebula:"info",frp:"primary",nps:"success",rathole:"info",linker:"danger"})[t]||"default",ps=t=>({running:"success",stopped:"info",error:"danger"})[t]||"default",fs=t=>({running:"运行中",stopped:"已停止",error:"错误"})[t]||"未知",it=t=>["frp","nps","headscale","netbird","zerotier"].includes(t.toLowerCase()),_s=t=>{if(t.tunnel_type.toLowerCase()==="frp"){const e=t.config_data||{};return e.dashboard_port||e.web_port||e.bind_port}return!0},vs=async t=>{try{const o=await(await fetch(`/api/v1/system/tunnels/${t.id}/management-url`)).json();o.success&&o.url?window.open(o.url,"_blank","width=1200,height=800,scrollbars=yes,resizable=yes"):d.warning(o.message||"无法获取管理界面地址")}catch(e){console.error("获取管理界面地址失败:",e),d.error("获取管理界面地址失败")}},A=async()=>{try{Re.value=!0;const t=await $e();t&&typeof t=="object"?t.success===!0?(O.value=Array.isArray(t.data)?t.data:[],J.value=O.value.find(e=>e==null?void 0:e.is_active)||null):t.data&&t.data.success===!0?(O.value=Array.isArray(t.data.data)?t.data.data:[],J.value=O.value.find(e=>e==null?void 0:e.is_active)||null):Array.isArray(t)?(O.value=t,J.value=O.value.find(e=>e==null?void 0:e.is_active)||null):Array.isArray(t.data)?(O.value=t.data,J.value=O.value.find(e=>e==null?void 0:e.is_active)||null):(console.warn("未知的响应格式:",t),O.value=[],J.value=null):(console.warn("无效的响应:",t),O.value=[],J.value=null)}catch(t){console.error("加载压缩配置失败:",t),d.error(`加载压缩配置失败: ${t.message||"未知错误"}`),O.value=[],J.value=null}finally{Re.value=!1}},ys=async()=>{try{const t=await Tt();let e=t;t&&t.success&&t.data&&(console.log("🔧 loadAvailableAlgorithms - 检测到API中间件包装格式，提取data字段"),e=t.data),console.log("loadAvailableAlgorithms - 处理后的算法数据:",e);let o=null;if(e&&e.algorithms?o=e.algorithms:Array.isArray(e)?o=e:e&&e.data&&e.data.algorithms&&(o=e.data.algorithms),o)G.value=o.map(c=>({...c,enabled:c.available}));else throw new Error("无法解析算法数据")}catch(t){console.error("加载可用算法失败:",t),G.value=[{name:"snappy",display_name:"Snappy",description:"Google开发的快速压缩算法，速度优先",available:!0,enabled:!0},{name:"lz4",display_name:"LZ4",description:"超高速压缩算法，极低延迟",available:!0,enabled:!0},{name:"zstd",display_name:"Zstandard",description:"Facebook开发的高效压缩算法，压缩率优先",available:!0,enabled:!1}]}},K=async()=>{try{const t=await St();t.success&&t.data&&t.data.global_stats?we.value=t.data.global_stats:t.data&&t.data.success&&t.data.data&&t.data.data.global_stats?we.value=t.data.data.global_stats:t.global_stats?we.value=t.global_stats:t.data&&t.data.global_stats?we.value=t.data.global_stats:console.warn("无法解析压缩统计数据格式:",t)}catch(t){console.error("加载压缩统计失败:",t)}},dt=async()=>{try{const t=await kt();t.success&&t.data?he.value=t.data:t.data&&t.data.success&&t.data.data?he.value=t.data.data:t.recommendations?he.value=t.recommendations:t.data&&t.data.recommendations?he.value=t.data.recommendations:console.warn("无法解析智能推荐数据格式:",t)}catch(t){console.error("加载智能推荐失败:",t)}},gs=()=>{const t=G.value.find(e=>e.name===C.algorithm);de.value=t,t&&bs()},bs=()=>{const t=de.value;if(!t)return;const o={snappy:{rate:85,speed:520,memory:440},lz4:{rate:78,speed:610,memory:410},zstd:{rate:92,speed:380,memory:520}}[t.name]||{rate:80,speed:400,memory:500};H.value={estimated_rate:o.rate,estimated_speed:o.speed,estimated_memory:o.memory}},ws=async()=>{var t;try{await Oe.value.validate(),ye.value=!0;const e=await Fe(C);if(e&&typeof e=="object")if(e.success===!0||e.data&&e.data.success===!0)d.success("压缩配置创建成功"),re.value=!1,Pe(),await A();else{const o=e.message||((t=e.data)==null?void 0:t.message)||"创建压缩配置失败";d.error(o)}else d.error("服务器响应格式错误")}catch(e){console.error("创建压缩配置失败:",e),d.error("创建压缩配置失败")}finally{ye.value=!1}},hs=t=>{Object.assign(S,t),Z.value=!0},Cs=async()=>{try{await He.value.validate(),ge.value=!0;const t=await bt(S.id,S);t.success?(d.success("压缩配置更新成功"),Z.value=!1,await A()):t.data&&t.data.success?(d.success("压缩配置更新成功"),Z.value=!1,await A()):d.error("更新压缩配置失败")}catch(t){console.error("更新压缩配置失败:",t),d.error("更新压缩配置失败")}finally{ge.value=!1}},Ss=async t=>{try{j.value=t;const e=d({message:"正在切换压缩配置，请稍候...",type:"info",duration:0}),o=await ht(t);e.close(),o.success?(d.success("压缩配置已激活，新连接将使用新配置"),await A(),await K()):o.data&&o.data.success?(d.success("压缩配置已激活，新连接将使用新配置"),await A(),await K()):d.error("激活压缩配置失败")}catch(e){console.error("激活压缩配置失败:",e),d.error("激活压缩配置失败")}finally{j.value=null}},Ts=async t=>{try{j.value=t;const e=await Ct(t);e.success?(d.success("压缩配置已停用"),await A(),await K()):e.data&&e.data.success?(d.success("压缩配置已停用"),await A(),await K()):d.error("停用压缩配置失败")}catch(e){console.error("停用压缩配置失败:",e),d.error("停用压缩配置失败")}finally{j.value=null}},ks=async t=>{try{await q.confirm("确定要删除这个压缩配置吗？","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await wt(t);e.success?(d.success("压缩配置已删除"),await A()):e.data&&e.data.success?(d.success("压缩配置已删除"),await A()):d.error("删除压缩配置失败")}catch(e){e!=="cancel"&&(console.error("删除压缩配置失败:",e),d.error("删除压缩配置失败"))}},Vs=t=>{const e=new FileReader;e.onload=o=>{try{const c=JSON.parse(o.target.result);Object.assign(C,c),d.success("配置文件读取成功")}catch{d.error("配置文件格式错误")}},e.readAsText(t.raw)},xs=async()=>{try{be.value=!0;const t=await Fe(C);t.success?(d.success("配置导入成功"),ie.value=!1,Pe(),await A()):t.data&&t.data.success&&(d.success("配置导入成功"),ie.value=!1,Pe(),await A())}catch(t){console.error("导入配置失败:",t),d.error("导入配置失败")}finally{be.value=!1}},Pe=()=>{var t;Object.assign(C,{name:"",algorithm:"",level:"medium",stream_type:"",max_concurrent_users:500,bandwidth_limit_mbps:3,auto_fallback:!0,is_default:!1}),H.value=null,de.value=null,(t=Oe.value)==null||t.resetFields()},$s=t=>({snappy:"success",lz4:"warning",zstd:"primary"})[t]||"info",Ds=t=>({snappy:"Snappy",lz4:"LZ4",zstd:"Zstandard"})[t]||t.toUpperCase(),zs=t=>({low:"success",medium:"warning",high:"danger"})[t]||"info",Ms=t=>({low:"低",medium:"中",high:"高"})[t]||t,Us=t=>({client_data:"专属客户端数据",general_data:"通用数据",web_data:"Web数据 (已弃用)",web_http:"Web HTTP (已弃用)",web_https:"Web HTTPS (已弃用)",vh_usb:"VirtualHere USB (已弃用)",vh_ctrl:"VirtualHere 控制 (旧)"})[t]||"通用数据",Rs=async t=>{try{d.success(`${t.display_name} ${t.enabled?"已启用":"已禁用"}`)}catch{d.error("算法状态切换失败"),t.enabled=!t.enabled}},Es=t=>{d.info(`配置 ${t.display_name} 算法`)},Os=async t=>{try{d.info(`正在测试 ${t.display_name} 算法...`),setTimeout(()=>{d.success(`${t.display_name} 算法测试通过`)},2e3)}catch{d.error("算法测试失败")}};Ks(()=>{Ye(),ue(),A(),ys(),K(),dt(),Xe(),setInterval(K,5e3),setInterval(dt,3e4),Se()});const Se=async()=>{Ae.value=!0;try{const t=await qa();t.success?Ke.value=t.data:d.error(t.message||"获取缓存信息失败")}catch(t){console.error("获取缓存信息失败:",t),d.error("获取缓存信息失败")}finally{Ae.value=!1}},As=async t=>{try{await q.confirm(`确定要清除 ${Ps(t)} 吗？`,"确认清除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),F[t]=!0;const e=await gt(t);e.success?(d.success(e.message||"缓存清除成功"),await Se()):d.error(e.message||"缓存清除失败")}catch(e){e!=="cancel"&&(console.error("清除缓存失败:",e),d.error("清除缓存失败"))}finally{F[t]=!1}},Bs=async()=>{try{await q.confirm("确定要清除所有服务器缓存吗？这可能会导致短暂的性能下降。","确认清除所有缓存",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),F.all=!0;const t=await Za();t.success?(d.success(t.message||"所有缓存清除成功"),await Se()):d.error(t.message||"清除缓存失败")}catch(t){t!=="cancel"&&(console.error("清除所有缓存失败:",t),d.error("清除所有缓存失败"))}finally{F.all=!1}},Is=async()=>{try{await q.confirm("确定要清除Web页面缓存吗？页面将会自动刷新。","确认清除Web缓存",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),F.web=!0;try{await Ga(),d.success("Web页面缓存清除成功，页面即将刷新"),setTimeout(()=>{Ha()},1e3)}catch{d.error("Web页面缓存清除失败")}}catch(t){t!=="cancel"&&(console.error("清除Web缓存失败:",t),d.error("清除Web缓存失败"))}finally{F.web=!1}},Ls=()=>{Se()},Ps=t=>({server_cache:"服务器程序缓存",redis_cache:"Redis数据缓存",query_cache:"数据库查询缓存",compression_cache:"压缩统计缓存",tunnel_cache:"隧道状态缓存",api_cache:"API响应缓存"})[t]||t,Fs=t=>({server_cache:Ba,redis_cache:Aa,query_cache:Oa,compression_cache:Ea,tunnel_cache:Ra,api_cache:Ua})[t]||Ia,Ns=t=>({active:"活跃",inactive:"未激活",error:"错误"})[t]||t;return(t,e)=>{const o=ea,c=Ys,p=na,f=la,T=aa,h=sa,k=ua,v=ra,V=ia,Y=da,Q=oa,B=Qs,js=ca,ce=pa,me=fa,_=va,E=_a,Ws=ha,Js=Sa,qs=ta,X=Xs,ee=ka,ut=Ta,ct=Va,N=$a,Te=xa,ke=Da,Zs=za,mt=wa;return g(),x(pe,null,[je.value?(g(),x("div",Ka)):(g(),x("div",Ya,[s(c,{icon:"warning",title:"访问被拒绝","sub-title":"您没有权限访问系统设置，仅全域管理员和超级管理员可以访问。"},{extra:n(()=>[s(o,{type:"primary",onClick:e[0]||(e[0]=l=>t.$router.push("/dashboard"))},{default:n(()=>e[51]||(e[51]=[m(" 返回工作台 ",-1)])),_:1,__:[51]})]),_:1})])),je.value?(g(),x("div",Qa,[s(B,{class:"main-card"},{header:n(()=>e[52]||(e[52]=[a("div",{class:"card-header"},[a("span",{class:"header-title"},"系统设置"),a("span",{class:"header-subtitle"},"管理系统配置、客户端设置和安全参数")],-1)])),default:n(()=>[s(qs,{modelValue:We.value,"onUpdate:modelValue":e[14]||(e[14]=l=>We.value=l),type:"border-card"},{default:n(()=>[s(h,{label:"客户端设置",name:"client"},{default:n(()=>[a("div",Xa,[e[55]||(e[55]=a("h3",null,"专属客户端",-1)),s(T,{model:ae,"label-width":"150px"},{default:n(()=>[s(f,{label:"下载链接"},{default:n(()=>[s(p,{modelValue:ae.client_download_url,"onUpdate:modelValue":e[1]||(e[1]=l=>ae.client_download_url=l),placeholder:"请输入客户端下载链接"},null,8,["modelValue"]),e[53]||(e[53]=a("div",{class:"setting-help"}," 此链接将在登录页面显示，用户可点击下载专属客户端 ",-1))]),_:1,__:[53]}),s(f,null,{default:n(()=>[s(o,{type:"primary",onClick:zt,loading:le.clientSettings,icon:le.clientSettings?"":"Check"},{default:n(()=>[m(u(le.clientSettings?"保存中...":"保存设置"),1)]),_:1},8,["loading","icon"]),s(o,{onClick:Mt},{default:n(()=>e[54]||(e[54]=[m(" 重置 ",-1)])),_:1,__:[54]})]),_:1})]),_:1},8,["model"])])]),_:1}),s(h,{label:"公网链接",name:"tunnel"},{default:n(()=>[a("div",el,[s(B,{class:"standard-card"},{header:n(()=>[a("div",tl,[e[57]||(e[57]=a("span",null,"公网链接管理",-1)),s(o,{type:"primary",onClick:e[2]||(e[2]=l=>te.value=!0)},{default:n(()=>[s(k,null,{default:n(()=>[s(W(vt))]),_:1}),e[56]||(e[56]=m(" 新增配置 ",-1))]),_:1,__:[56]})])]),default:n(()=>[s(Q,{data:De.value,style:{width:"100%"},size:"small"},{default:n(()=>[s(v,{prop:"name",label:"配置名称",width:"120","show-overflow-tooltip":""}),s(v,{prop:"tunnel_type",label:"类型",width:"240",align:"center"},{default:n(l=>[s(V,{type:ms(l.row.tunnel_type),size:"small"},{default:n(()=>[m(u(l.row.tunnel_type.toUpperCase()),1)]),_:2},1032,["type"])]),_:1}),s(v,{prop:"is_active",label:"启用",width:"80",align:"center"},{default:n(l=>[s(Y,{modelValue:l.row.is_active,"onUpdate:modelValue":b=>l.row.is_active=b,onChange:b=>Rt(l.row),disabled:l.row.force_disabled,size:"small"},null,8,["modelValue","onUpdate:modelValue","onChange","disabled"])]),_:1}),s(v,{prop:"status",label:"状态",width:"100",align:"center"},{default:n(l=>[s(V,{type:ps(l.row.status),size:"small"},{default:n(()=>[m(u(fs(l.row.status)),1)]),_:2},1032,["type"])]),_:1}),s(v,{label:"操作","min-width":"400",align:"left"},{default:n(l=>[a("div",sl,[l.row.is_running?(g(),U(o,{key:1,type:"warning",size:"small",onClick:b=>Ot(l.row)},{default:n(()=>e[59]||(e[59]=[m(" 停止 ",-1)])),_:2,__:[59]},1032,["onClick"])):(g(),U(o,{key:0,type:"success",size:"small",onClick:b=>Et(l.row),disabled:!l.row.is_active||l.row.force_disabled},{default:n(()=>e[58]||(e[58]=[m(" 启动 ",-1)])),_:2,__:[58]},1032,["onClick","disabled"])),s(o,{type:"primary",size:"small",onClick:b=>At(l.row)},{default:n(()=>e[60]||(e[60]=[m(" 编辑 ",-1)])),_:2,__:[60]},1032,["onClick"]),s(o,{type:"info",size:"small",onClick:b=>ds(l.row)},{default:n(()=>e[61]||(e[61]=[m(" 升级 ",-1)])),_:2,__:[61]},1032,["onClick"]),s(o,{type:l.row.force_disabled?"success":"danger",size:"small",onClick:b=>us(l.row)},{default:n(()=>[m(u(l.row.force_disabled?"解锁":"锁定"),1)]),_:2},1032,["type","onClick"]),s(o,{type:"success",size:"small",onClick:b=>It(l.row),loading:fe.value[l.row.id],icon:"Connection"},{default:n(()=>[m(u(fe.value[l.row.id]?"测试中":"测试连接"),1)]),_:2},1032,["onClick","loading"]),it(l.row.tunnel_type)&&_s(l.row)?(g(),U(o,{key:2,type:"info",size:"small",onClick:b=>vs(l.row)},{default:n(()=>e[62]||(e[62]=[m(" 管理 ",-1)])),_:2,__:[62]},1032,["onClick"])):z("",!0)]),s(o,{type:"danger",size:"small",onClick:b=>cs(l.row),icon:W(_t),disabled:l.row.is_running},{default:n(()=>e[63]||(e[63]=[m(" 删除 ",-1)])),_:2,__:[63]},1032,["onClick","icon","disabled"])]),_:1})]),_:1},8,["data"])]),_:1})])]),_:1}),s(h,{label:"数据链路验证",name:"datalink"},{default:n(()=>[a("div",al,[s(B,{class:"standard-card"},{header:n(()=>[a("div",ll,[e[65]||(e[65]=a("span",null,"连接状态检查",-1)),s(o,{type:"primary",onClick:Xe,loading:ze.value},{default:n(()=>[s(k,null,{default:n(()=>[s(W(ma))]),_:1}),e[64]||(e[64]=m(" 刷新状态 ",-1))]),_:1,__:[64]},8,["loading"])])]),default:n(()=>[a("div",nl,[(g(!0),x(pe,null,Ve(_e.value,l=>(g(),x("div",{key:l.id,class:"connection-card"},[a("div",ol,[a("h4",null,u(l.name)+" ("+u(l.tunnel_type.toUpperCase())+")",1),s(V,{type:jt(l.status),size:"large"},{default:n(()=>[m(u(Wt(l.status)),1)]),_:2},1032,["type"])]),a("div",rl,[a("div",il,[e[66]||(e[66]=a("span",{class:"label"},"服务状态:",-1)),a("span",dl,u(l.service_status||"未知"),1)]),a("div",ul,[e[67]||(e[67]=a("span",{class:"label"},"最后检查:",-1)),a("span",cl,u(Ie(l.last_check)),1)]),a("div",ml,[e[68]||(e[68]=a("span",{class:"label"},"运行时长:",-1)),a("span",pl,u(l.uptime||"0分钟"),1)])]),a("div",fl,[s(o,{type:"success",size:"small",onClick:b=>Jt(l),disabled:!et(l)},{default:n(()=>e[69]||(e[69]=[m(" 开始性能测试 ",-1)])),_:2,__:[69]},1032,["onClick","disabled"])])]))),128))]),_e.value.length===0?(g(),U(js,{key:0,description:"没有活跃的内网穿透连接"},{default:n(()=>[s(o,{type:"primary",onClick:e[3]||(e[3]=l=>t.$emit("switch-tab","tunnel"))},{default:n(()=>e[70]||(e[70]=[m(" 前往配置管理 ",-1)])),_:1,__:[70]})]),_:1})):z("",!0)]),_:1}),$.value?(g(),U(B,{key:0,class:"standard-card"},{header:n(()=>[a("div",_l,[a("span",null,u($.value.name)+" - 数据性能测试",1),a("div",vl,[s(o,{type:"primary",onClick:qt,loading:ve.value,disabled:!et($.value)},{default:n(()=>e[71]||(e[71]=[m(" 开始全面测试 ",-1)])),_:1,__:[71]},8,["loading","disabled"]),s(o,{type:"danger",onClick:Zt,disabled:!qe.value},{default:n(()=>e[72]||(e[72]=[m(" 停止测试 ",-1)])),_:1,__:[72]},8,["disabled"])])])]),default:n(()=>[a("div",yl,[a("div",gl,[a("div",bl,[e[73]||(e[73]=a("h4",null,"连接延迟测试",-1)),s(o,{size:"small",onClick:tt,loading:i.latency.running},{default:n(()=>[m(u(i.latency.running?"测试中...":"开始测试"),1)]),_:1},8,["loading"])]),a("div",wl,[a("div",hl,[a("div",Cl,[e[74]||(e[74]=a("span",{class:"metric-label"},"平均延迟",-1)),a("span",Sl,u(r.latency.average||"--")+"ms",1)]),a("div",Tl,[e[75]||(e[75]=a("span",{class:"metric-label"},"最小延迟",-1)),a("span",kl,u(r.latency.min||"--")+"ms",1)]),a("div",Vl,[e[76]||(e[76]=a("span",{class:"metric-label"},"最大延迟",-1)),a("span",xl,u(r.latency.max||"--")+"ms",1)]),a("div",$l,[e[77]||(e[77]=a("span",{class:"metric-label"},"丢包率",-1)),a("span",Dl,u(r.latency.packetLoss||"--")+"%",1)])]),i.latency.running?(g(),x("div",zl,[s(ce,{percentage:i.latency.progress,status:i.latency.progress===100?"success":""},null,8,["percentage","status"]),a("span",Ml,u(i.latency.currentStep),1)])):z("",!0)])]),a("div",Ul,[a("div",Rl,[e[78]||(e[78]=a("h4",null,"带宽速率测试",-1)),s(o,{size:"small",onClick:at,loading:i.bandwidth.running},{default:n(()=>[m(u(i.bandwidth.running?"测试中...":"开始测试"),1)]),_:1},8,["loading"])]),a("div",El,[a("div",Ol,[a("div",Al,[e[79]||(e[79]=a("span",{class:"metric-label"},"下载速度",-1)),a("span",Bl,u(r.bandwidth.download||"--")+" Mbps",1)]),a("div",Il,[e[80]||(e[80]=a("span",{class:"metric-label"},"上传速度",-1)),a("span",Ll,u(r.bandwidth.upload||"--")+" Mbps",1)]),a("div",Pl,[e[81]||(e[81]=a("span",{class:"metric-label"},"数据传输量",-1)),a("span",Fl,u(r.bandwidth.totalData||"--")+" MB",1)]),a("div",Nl,[e[82]||(e[82]=a("span",{class:"metric-label"},"测试时长",-1)),a("span",jl,u(r.bandwidth.duration||"--")+"s",1)])]),i.bandwidth.running?(g(),x("div",Wl,[s(ce,{percentage:i.bandwidth.progress,status:i.bandwidth.progress===100?"success":""},null,8,["percentage","status"]),a("span",Jl,u(i.bandwidth.currentStep),1)])):z("",!0)])]),a("div",ql,[a("div",Zl,[e[84]||(e[84]=a("h4",null,"多并发连接测试",-1)),a("div",Gl,[s(me,{modelValue:Me.connections,"onUpdate:modelValue":e[4]||(e[4]=l=>Me.connections=l),min:1,max:100,size:"small",style:{width:"120px","margin-right":"10px"}},null,8,["modelValue"]),e[83]||(e[83]=a("span",{style:{"margin-right":"10px"}},"个并发连接",-1)),s(o,{size:"small",onClick:nt,loading:i.concurrent.running},{default:n(()=>[m(u(i.concurrent.running?"测试中...":"开始测试"),1)]),_:1},8,["loading"])])]),a("div",Hl,[a("div",Kl,[a("div",Yl,[e[85]||(e[85]=a("span",{class:"metric-label"},"成功连接",-1)),a("span",Ql,u(r.concurrent.successful||"--"),1)]),a("div",Xl,[e[86]||(e[86]=a("span",{class:"metric-label"},"失败连接",-1)),a("span",en,u(r.concurrent.failed||"--"),1)]),a("div",tn,[e[87]||(e[87]=a("span",{class:"metric-label"},"平均响应时间",-1)),a("span",sn,u(r.concurrent.avgResponseTime||"--")+"ms",1)]),a("div",an,[e[88]||(e[88]=a("span",{class:"metric-label"},"连接成功率",-1)),a("span",ln,u(r.concurrent.successRate||"--")+"%",1)])]),i.concurrent.running?(g(),x("div",nn,[s(ce,{percentage:i.concurrent.progress,status:i.concurrent.progress===100?"success":""},null,8,["percentage","status"]),a("span",on,u(i.concurrent.currentStep),1)])):z("",!0)])]),a("div",rn,[a("div",dn,[e[89]||(e[89]=a("h4",null,"长期稳定性测试",-1)),a("div",un,[s(E,{modelValue:Ue.duration,"onUpdate:modelValue":e[5]||(e[5]=l=>Ue.duration=l),size:"small",style:{width:"120px","margin-right":"10px"}},{default:n(()=>[s(_,{label:"5分钟",value:"5"}),s(_,{label:"15分钟",value:"15"}),s(_,{label:"30分钟",value:"30"}),s(_,{label:"1小时",value:"60"})]),_:1},8,["modelValue"]),s(o,{size:"small",onClick:Ht,loading:i.stability.running},{default:n(()=>[m(u(i.stability.running?"测试中...":"开始测试"),1)]),_:1},8,["loading"])])]),a("div",cn,[a("div",mn,[a("div",pn,[e[90]||(e[90]=a("span",{class:"metric-label"},"运行时长",-1)),a("span",fn,u(r.stability.elapsed||"--"),1)]),a("div",_n,[e[91]||(e[91]=a("span",{class:"metric-label"},"连接中断次数",-1)),a("span",vn,u(r.stability.disconnections||"--"),1)]),a("div",yn,[e[92]||(e[92]=a("span",{class:"metric-label"},"平均延迟",-1)),a("span",gn,u(r.stability.avgLatency||"--")+"ms",1)]),a("div",bn,[e[93]||(e[93]=a("span",{class:"metric-label"},"稳定性评分",-1)),a("span",wn,u(r.stability.score||"--")+"/100",1)])]),i.stability.running?(g(),x("div",hn,[s(ce,{percentage:i.stability.progress,status:i.stability.progress===100?"success":""},null,8,["percentage","status"]),a("span",Cn,u(i.stability.currentStep),1)])):z("",!0)])]),a("div",Sn,[a("div",Tn,[e[95]||(e[95]=a("h4",null,"数据压缩性能测试",-1)),a("div",kn,[s(o,{size:"small",onClick:es,loading:i.compression.running,type:"primary"},{default:n(()=>[m(u(i.compression.running?"测试中...":"开始压缩测试"),1)]),_:1},8,["loading"]),s(o,{size:"small",onClick:ts,disabled:!i.compression.running,type:"danger"},{default:n(()=>e[94]||(e[94]=[m(" 停止测试 ",-1)])),_:1,__:[94]},8,["disabled"])])]),a("div",Vn,[a("div",xn,[a("div",$n,[e[96]||(e[96]=a("span",{class:"config-label"},"数据大小:",-1)),s(E,{modelValue:w.dataSize,"onUpdate:modelValue":e[6]||(e[6]=l=>w.dataSize=l),size:"small",style:{width:"100px"}},{default:n(()=>[s(_,{label:"1MB",value:"1"}),s(_,{label:"10MB",value:"10"}),s(_,{label:"50MB",value:"50"}),s(_,{label:"100MB",value:"100"})]),_:1},8,["modelValue"])]),a("div",Dn,[e[97]||(e[97]=a("span",{class:"config-label"},"并发数:",-1)),s(E,{modelValue:w.concurrency,"onUpdate:modelValue":e[7]||(e[7]=l=>w.concurrency=l),size:"small",style:{width:"100px"}},{default:n(()=>[s(_,{label:"1",value:"1"}),s(_,{label:"5",value:"5"}),s(_,{label:"10",value:"10"}),s(_,{label:"20",value:"20"}),s(_,{label:"50",value:"50"}),s(_,{label:"100",value:"100"}),s(_,{label:"400",value:"400"})]),_:1},8,["modelValue"])]),a("div",zn,[e[98]||(e[98]=a("span",{class:"config-label"},"持续时间:",-1)),s(E,{modelValue:w.duration,"onUpdate:modelValue":e[8]||(e[8]=l=>w.duration=l),size:"small",style:{width:"120px"}},{default:n(()=>[s(_,{label:"1秒",value:"1"}),s(_,{label:"5秒",value:"5"}),s(_,{label:"10秒",value:"10"}),s(_,{label:"30秒",value:"30"}),s(_,{label:"60秒",value:"60"}),s(_,{label:"5分钟",value:"300"}),s(_,{label:"10分钟",value:"600"}),s(_,{label:"长期持续",value:"continuous"})]),_:1},8,["modelValue"])]),a("div",Mn,[e[99]||(e[99]=a("span",{class:"config-label"},"数据类型:",-1)),s(E,{modelValue:w.dataType,"onUpdate:modelValue":e[9]||(e[9]=l=>w.dataType=l),size:"small",style:{width:"140px"}},{default:n(()=>[s(_,{label:"JSON配置数据",value:"json"}),s(_,{label:"二进制设备数据",value:"binary"}),s(_,{label:"文本日志数据",value:"text"}),s(_,{label:"混合数据包",value:"mixed"})]),_:1},8,["modelValue"])])]),a("div",Un,[a("div",Rn,[e[100]||(e[100]=a("span",{class:"config-label"},"启用压缩:",-1)),s(Y,{modelValue:w.enableCompression,"onUpdate:modelValue":e[10]||(e[10]=l=>w.enableCompression=l)},null,8,["modelValue"])]),w.enableCompression?(g(),x("div",En,[e[101]||(e[101]=a("span",{class:"config-label"},"压缩算法:",-1)),s(E,{modelValue:w.algorithm,"onUpdate:modelValue":e[11]||(e[11]=l=>w.algorithm=l),size:"small",style:{width:"120px"}},{default:n(()=>[s(_,{label:"Snappy",value:"snappy"}),s(_,{label:"LZ4",value:"lz4"}),s(_,{label:"Zstandard",value:"zstd"})]),_:1},8,["modelValue"])])):z("",!0),w.enableCompression?(g(),x("div",On,[e[102]||(e[102]=a("span",{class:"config-label"},"压缩级别:",-1)),s(E,{modelValue:w.compressionLevel,"onUpdate:modelValue":e[12]||(e[12]=l=>w.compressionLevel=l),size:"small",style:{width:"80px"}},{default:n(()=>[s(_,{label:"低",value:"low"}),s(_,{label:"中",value:"medium"}),s(_,{label:"高",value:"high"})]),_:1},8,["modelValue"])])):z("",!0)])]),a("div",An,[a("div",Bn,[a("div",In,[e[103]||(e[103]=a("span",{class:"metric-label"},"原始数据大小",-1)),a("span",Ln,u(r.compression.originalSize||"--"),1)]),a("div",Pn,[e[104]||(e[104]=a("span",{class:"metric-label"},"压缩后大小",-1)),a("span",Fn,u(r.compression.compressedSize||"--"),1)]),a("div",Nn,[e[105]||(e[105]=a("span",{class:"metric-label"},"压缩率",-1)),a("span",jn,u(r.compression.compressionRatio||"--")+"%",1)]),a("div",Wn,[e[106]||(e[106]=a("span",{class:"metric-label"},"传输速度",-1)),a("span",Jn,u(r.compression.transferSpeed||"--")+" MB/s",1)]),a("div",qn,[e[107]||(e[107]=a("span",{class:"metric-label"},"压缩时间",-1)),a("span",Zn,u(r.compression.compressionTime||"--")+"ms",1)]),a("div",Gn,[e[108]||(e[108]=a("span",{class:"metric-label"},"解压时间",-1)),a("span",Hn,u(r.compression.decompressionTime||"--")+"ms",1)]),a("div",Kn,[e[109]||(e[109]=a("span",{class:"metric-label"},"总传输量",-1)),a("span",Yn,u(r.compression.totalTransferred||"--"),1)]),a("div",Qn,[e[110]||(e[110]=a("span",{class:"metric-label"},"节省带宽",-1)),a("span",Xn,u(r.compression.bandwidthSaved||"--"),1)])]),i.compression.running?(g(),x("div",eo,[s(ce,{percentage:i.compression.progress,status:i.compression.progress===100?"success":""},null,8,["percentage","status"]),a("span",to,u(i.compression.currentStep),1)])):z("",!0),r.compression.chartData?(g(),x("div",so,[a("div",ao,[e[111]||(e[111]=a("h5",null,"压缩率对比",-1)),a("div",lo,[(g(!0),x(pe,null,Ve(r.compression.chartData,(l,b)=>(g(),x("div",{class:"chart-bar",key:b,style:ya({height:l.ratio+"%"})},[a("span",no,u(l.algorithm),1),a("span",oo,u(l.ratio)+"%",1)],4))),128))])])])):z("",!0)])])])]),_:1})):z("",!0),xt.value?(g(),U(B,{key:1,class:"standard-card"},{header:n(()=>[a("div",ro,[e[114]||(e[114]=a("span",null,"测试报告",-1)),a("div",io,[s(o,{size:"small",onClick:Qt},{default:n(()=>[s(k,null,{default:n(()=>[s(W(ga))]),_:1}),e[112]||(e[112]=m(" 导出报告 ",-1))]),_:1,__:[112]}),s(o,{size:"small",onClick:Xt},{default:n(()=>[s(k,null,{default:n(()=>[s(W(_t))]),_:1}),e[113]||(e[113]=m(" 清除结果 ",-1))]),_:1,__:[113]})])])]),default:n(()=>{var l,b;return[a("div",uo,[a("div",co,[e[118]||(e[118]=a("h4",null,"测试摘要",-1)),a("div",mo,[a("div",po,[e[115]||(e[115]=a("span",{class:"label"},"测试配置:",-1)),a("span",fo,u((l=$.value)==null?void 0:l.name)+" ("+u((b=$.value)==null?void 0:b.tunnel_type.toUpperCase())+")",1)]),a("div",_o,[e[116]||(e[116]=a("span",{class:"label"},"测试时间:",-1)),a("span",vo,u(Ie(D.startTime))+" - "+u(Ie(D.endTime)),1)]),a("div",yo,[e[117]||(e[117]=a("span",{class:"label"},"总体评分:",-1)),a("span",go,u(D.overallScore)+"/100",1)])])]),a("div",bo,[e[119]||(e[119]=a("h4",null,"详细结果",-1)),s(Q,{data:D.details,style:{width:"100%"}},{default:n(()=>[s(v,{prop:"testType",label:"测试类型",width:"150"}),s(v,{prop:"result",label:"测试结果",width:"200"}),s(v,{prop:"score",label:"评分",width:"100"}),s(v,{prop:"status",label:"状态",width:"100"},{default:n(pt=>[s(V,{type:pt.row.status==="passed"?"success":"danger"},{default:n(()=>[m(u(pt.row.status==="passed"?"通过":"失败"),1)]),_:2},1032,["type"])]),_:1}),s(v,{prop:"notes",label:"备注"})]),_:1},8,["data"])])])]}),_:1})):z("",!0)])]),_:1}),s(h,{label:"数据压缩",name:"compression"},{default:n(()=>[a("div",wo,[s(B,{class:"standard-card"},{header:n(()=>e[120]||(e[120]=[a("div",{class:"card-header"},[a("span",null,"压缩算法管理")],-1)])),default:n(()=>[s(Q,{data:G.value,style:{width:"100%"}},{default:n(()=>[s(v,{prop:"display_name",label:"算法名称",width:"150"}),s(v,{prop:"description",label:"描述","min-width":"200"}),s(v,{prop:"available",label:"可用状态",width:"100",align:"center"},{default:n(l=>[s(V,{type:l.row.available?"success":"danger"},{default:n(()=>[m(u(l.row.available?"可用":"不可用"),1)]),_:2},1032,["type"])]),_:1}),s(v,{prop:"enabled",label:"启用状态",width:"100",align:"center"},{default:n(l=>[s(Y,{modelValue:l.row.enabled,"onUpdate:modelValue":b=>l.row.enabled=b,onChange:b=>Rs(l.row),disabled:!l.row.available},null,8,["modelValue","onUpdate:modelValue","onChange","disabled"])]),_:1}),s(v,{label:"操作",width:"200"},{default:n(l=>[s(o,{size:"small",onClick:b=>Es(l.row),disabled:!l.row.available},{default:n(()=>e[121]||(e[121]=[m(" 配置 ",-1)])),_:2,__:[121]},1032,["onClick","disabled"]),s(o,{size:"small",type:"info",onClick:b=>Os(l.row),disabled:!l.row.available||!l.row.enabled},{default:n(()=>e[122]||(e[122]=[m(" 测试 ",-1)])),_:2,__:[122]},1032,["onClick","disabled"])]),_:1})]),_:1},8,["data"])]),_:1}),s(B,{class:"standard-card",style:{"margin-top":"20px"}},{header:n(()=>[a("div",ho,[e[124]||(e[124]=a("span",null,"压缩配置管理",-1)),s(o,{type:"primary",onClick:e[13]||(e[13]=l=>re.value=!0)},{default:n(()=>[s(k,null,{default:n(()=>[s(W(vt))]),_:1}),e[123]||(e[123]=m(" 新增配置 ",-1))]),_:1,__:[123]})])]),default:n(()=>[yt((g(),U(Q,{data:O.value,style:{width:"100%"}},{default:n(()=>[s(v,{prop:"name",label:"配置名称",width:"150"}),s(v,{prop:"algorithm",label:"压缩算法",width:"120"},{default:n(l=>[s(V,{type:$s(l.row.algorithm)},{default:n(()=>[m(u(Ds(l.row.algorithm)),1)]),_:2},1032,["type"])]),_:1}),s(v,{prop:"level",label:"压缩级别",width:"120"},{default:n(l=>[s(V,{type:zs(l.row.level)},{default:n(()=>[m(u(Ms(l.row.level)),1)]),_:2},1032,["type"])]),_:1}),s(v,{prop:"stream_type",label:"数据流类型",width:"150"},{default:n(l=>[m(u(Us(l.row.stream_type)),1)]),_:1}),s(v,{prop:"is_active",label:"状态",width:"100"},{default:n(l=>[s(V,{type:l.row.is_active?"success":"info"},{default:n(()=>[m(u(l.row.is_active?"已启用":"未启用"),1)]),_:2},1032,["type"])]),_:1}),s(v,{prop:"is_default",label:"默认",width:"80",align:"center"},{default:n(l=>[l.row.is_default?(g(),U(k,{key:0,color:"#67C23A"},{default:n(()=>[s(W(ba))]),_:1})):z("",!0)]),_:1}),s(v,{label:"操作",width:"280"},{default:n(l=>[l.row.is_active?(g(),U(o,{key:1,type:"warning",size:"small",onClick:b=>Ts(l.row.id),loading:j.value===l.row.id},{default:n(()=>[m(u(j.value===l.row.id?"停用中":"停用"),1)]),_:2},1032,["onClick","loading"])):(g(),U(o,{key:0,type:"success",size:"small",onClick:b=>Ss(l.row.id),loading:j.value===l.row.id},{default:n(()=>[m(u(j.value===l.row.id?"切换中":"启用"),1)]),_:2},1032,["onClick","loading"])),s(o,{type:"primary",size:"small",onClick:b=>hs(l.row)},{default:n(()=>e[125]||(e[125]=[m(" 编辑 ",-1)])),_:2,__:[125]},1032,["onClick"]),s(o,{type:"danger",size:"small",onClick:b=>ks(l.row.id),disabled:l.row.is_default||l.row.is_active},{default:n(()=>e[126]||(e[126]=[m(" 删除 ",-1)])),_:2,__:[126]},1032,["onClick","disabled"])]),_:1})]),_:1},8,["data"])),[[mt,Re.value]])]),_:1})])]),_:1}),s(h,{label:"缓存清除",name:"cache"},{default:n(()=>[a("div",Co,[s(B,{class:"standard-card"},{header:n(()=>e[127]||(e[127]=[a("div",{class:"card-header"},[a("span",{class:"header-title"},"系统缓存管理"),a("span",{class:"header-subtitle"},"查看和清除各类系统缓存，解决页面显示异常问题")],-1)])),default:n(()=>[a("div",So,[s(Ws,{title:"缓存清除说明",type:"info",closable:!1,"show-icon":"",style:{"margin-bottom":"20px"}},{default:n(()=>e[128]||(e[128]=[a("p",null,"如果遇到页面显示异常、数据不更新等问题，可以尝试清除相应的缓存。",-1),a("p",null,[a("strong",null,"注意："),m("清除缓存可能会导致短暂的性能下降，建议在系统空闲时操作。")],-1)])),_:1}),yt((g(),U(Q,{data:Ke.value,style:{width:"100%"},class:"standard-table"},{default:n(()=>[s(v,{prop:"name",label:"缓存类型",width:"200"},{default:n(({row:l})=>[a("div",To,[s(k,{class:"cache-icon"},{default:n(()=>[(g(),U(Ca(Fs(l.type))))]),_:2},1024),a("span",null,u(l.name),1)])]),_:1}),s(v,{prop:"description",label:"描述","min-width":"300"},{default:n(({row:l})=>[a("span",ko,u(l.description),1)]),_:1}),s(v,{prop:"status",label:"状态",width:"100"},{default:n(({row:l})=>[s(V,{type:l.status==="active"?"success":l.status==="error"?"danger":"info",size:"small"},{default:n(()=>[m(u(Ns(l.status)),1)]),_:2},1032,["type"])]),_:1}),s(v,{prop:"size",label:"大小",width:"100"},{default:n(({row:l})=>[a("span",Vo,u(l.size),1)]),_:1}),s(v,{label:"操作",width:"150"},{default:n(({row:l})=>[s(o,{type:"primary",size:"small",loading:F[l.type],onClick:b=>As(l.type),disabled:l.status==="inactive"},{default:n(()=>e[129]||(e[129]=[m(" 清除 ",-1)])),_:2,__:[129]},1032,["loading","onClick","disabled"])]),_:1})]),_:1},8,["data"])),[[mt,Ae.value]]),a("div",xo,[s(Js,null,{default:n(()=>[s(o,{type:"warning",loading:F.all,onClick:Bs,icon:"Delete"},{default:n(()=>e[130]||(e[130]=[m(" 清除所有服务器缓存 ",-1)])),_:1,__:[130]},8,["loading"]),s(o,{type:"info",loading:F.web,onClick:Is,icon:"Refresh"},{default:n(()=>e[131]||(e[131]=[m(" 清除Web页面缓存 ",-1)])),_:1,__:[131]},8,["loading"]),s(o,{type:"success",onClick:Ls,icon:"Refresh"},{default:n(()=>e[132]||(e[132]=[m(" 刷新缓存信息 ",-1)])),_:1,__:[132]})]),_:1})])])]),_:1})])]),_:1})]),_:1},8,["modelValue"])]),_:1}),s(X,{modelValue:te.value,"onUpdate:modelValue":e[20]||(e[20]=l=>te.value=l),title:"创建内网穿透配置",width:"600px"},{footer:n(()=>[s(o,{onClick:e[19]||(e[19]=l=>te.value=!1)},{default:n(()=>e[133]||(e[133]=[m("取消",-1)])),_:1,__:[133]}),s(o,{type:"primary",onClick:Ut},{default:n(()=>e[134]||(e[134]=[m("确定",-1)])),_:1,__:[134]})]),default:n(()=>[s(T,{model:P,"label-width":"120px"},{default:n(()=>[s(f,{label:"配置名称",required:""},{default:n(()=>[s(p,{modelValue:P.name,"onUpdate:modelValue":e[15]||(e[15]=l=>P.name=l),placeholder:"请输入配置名称"},null,8,["modelValue"])]),_:1}),s(f,{label:"穿透类型",required:""},{default:n(()=>[s(E,{modelValue:P.tunnel_type,"onUpdate:modelValue":e[16]||(e[16]=l=>P.tunnel_type=l),placeholder:"请选择穿透类型"},{default:n(()=>[s(_,{label:"FRP",value:"frp"}),s(_,{label:"Linker",value:"linker"})]),_:1},8,["modelValue"])]),_:1}),s(f,{label:"配置数据"},{default:n(()=>[s(p,{modelValue:ne.value,"onUpdate:modelValue":e[17]||(e[17]=l=>ne.value=l),type:"textarea",rows:8,placeholder:"请输入JSON格式的配置数据"},null,8,["modelValue"])]),_:1}),s(f,{label:"启用配置"},{default:n(()=>[s(Y,{modelValue:P.is_active,"onUpdate:modelValue":e[18]||(e[18]=l=>P.is_active=l)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),s(X,{modelValue:se.value,"onUpdate:modelValue":e[27]||(e[27]=l=>se.value=l),title:"编辑内网穿透配置",width:"600px"},{footer:n(()=>[s(o,{onClick:e[26]||(e[26]=l=>se.value=!1)},{default:n(()=>e[136]||(e[136]=[m("取消",-1)])),_:1,__:[136]}),s(o,{type:"primary",onClick:Bt},{default:n(()=>e[137]||(e[137]=[m("保存",-1)])),_:1,__:[137]})]),default:n(()=>[s(T,{model:M,"label-width":"120px"},{default:n(()=>[s(f,{label:"配置名称",required:""},{default:n(()=>[s(p,{modelValue:M.name,"onUpdate:modelValue":e[21]||(e[21]=l=>M.name=l),placeholder:"请输入配置名称"},null,8,["modelValue"])]),_:1}),s(f,{label:"穿透类型",required:""},{default:n(()=>[s(E,{modelValue:M.tunnel_type,"onUpdate:modelValue":e[22]||(e[22]=l=>M.tunnel_type=l),placeholder:"请选择穿透类型",disabled:""},{default:n(()=>[s(_,{label:"FRP",value:"frp"}),s(_,{label:"Linker",value:"linker"})]),_:1},8,["modelValue"])]),_:1}),s(f,{label:"配置数据"},{default:n(()=>[s(p,{modelValue:oe.value,"onUpdate:modelValue":e[23]||(e[23]=l=>oe.value=l),type:"textarea",rows:8,placeholder:"请输入JSON格式的配置数据"},null,8,["modelValue"])]),_:1}),s(f,{label:"启用配置"},{default:n(()=>[s(Y,{modelValue:M.is_active,"onUpdate:modelValue":e[24]||(e[24]=l=>M.is_active=l),disabled:M.force_disabled},null,8,["modelValue","disabled"])]),_:1}),s(f,{label:"强制禁用"},{default:n(()=>[s(Y,{modelValue:M.force_disabled,"onUpdate:modelValue":e[25]||(e[25]=l=>M.force_disabled=l)},null,8,["modelValue"]),e[135]||(e[135]=a("div",{class:"form-tip"},"启用后将完全阻止此配置被启动，用于安全防护",-1))]),_:1,__:[135]})]),_:1},8,["model"])]),_:1},8,["modelValue"]),s(X,{modelValue:re.value,"onUpdate:modelValue":e[37]||(e[37]=l=>re.value=l),title:"创建数据压缩配置",width:"700px"},{footer:n(()=>[a("span",Lo,[s(o,{onClick:e[36]||(e[36]=l=>re.value=!1)},{default:n(()=>e[147]||(e[147]=[m("取消",-1)])),_:1,__:[147]}),s(o,{type:"primary",onClick:ws,loading:ye.value},{default:n(()=>[m(u(ye.value?"创建中...":"创建配置"),1)]),_:1},8,["loading"])])]),default:n(()=>[s(T,{model:C,rules:Ge,ref_key:"compressionFormRef",ref:Oe,"label-width":"140px"},{default:n(()=>[s(f,{label:"配置名称",prop:"name"},{default:n(()=>[s(p,{modelValue:C.name,"onUpdate:modelValue":e[28]||(e[28]=l=>C.name=l),placeholder:"请输入配置名称"},null,8,["modelValue"])]),_:1}),s(f,{label:"压缩算法",prop:"algorithm"},{default:n(()=>[s(E,{modelValue:C.algorithm,"onUpdate:modelValue":e[29]||(e[29]=l=>C.algorithm=l),placeholder:"请选择压缩算法",onChange:gs},{default:n(()=>[(g(!0),x(pe,null,Ve(G.value.filter(l=>l.available),l=>(g(),U(_,{key:l.name,label:l.display_name,value:l.name},{default:n(()=>[a("div",$o,[a("span",null,u(l.display_name),1),a("span",Do,u(l.description),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"]),de.value?(g(),x("div",zo,[s(V,{size:"small",type:"info"},{default:n(()=>[m(u(de.value.description),1)]),_:1})])):z("",!0)]),_:1}),s(f,{label:"压缩级别",prop:"level"},{default:n(()=>[s(ut,{modelValue:C.level,"onUpdate:modelValue":e[30]||(e[30]=l=>C.level=l)},{default:n(()=>[s(ee,{label:"low"},{default:n(()=>e[138]||(e[138]=[a("span",{class:"level-option"},[a("strong",null,"低压缩率"),m(" - 高速度，适合实时传输 ")],-1)])),_:1,__:[138]}),s(ee,{label:"medium"},{default:n(()=>e[139]||(e[139]=[a("span",{class:"level-option"},[a("strong",null,"中等压缩率"),m(" - 平衡速度与压缩效果 ")],-1)])),_:1,__:[139]}),s(ee,{label:"high"},{default:n(()=>e[140]||(e[140]=[a("span",{class:"level-option"},[a("strong",null,"高压缩率"),m(" - 低速度，适合大文件传输 ")],-1)])),_:1,__:[140]})]),_:1},8,["modelValue"])]),_:1}),s(f,{label:"数据流类型",prop:"stream_type"},{default:n(()=>[s(E,{modelValue:C.stream_type,"onUpdate:modelValue":e[31]||(e[31]=l=>C.stream_type=l),placeholder:"请选择数据流类型"},{default:n(()=>[s(_,{label:"专属客户端数据 (仅针对专属客户端连接)",value:"client_data"}),s(_,{label:"通用数据 (其他专属客户端数据流)",value:"general_data"})]),_:1},8,["modelValue"]),a("div",Mo,[s(ct,{type:"info",size:"small"},{default:n(()=>e[141]||(e[141]=[m(" 注意：数据压缩仅针对专属客户端连接，不影响分布式节点间通信和Web界面通信 ",-1)])),_:1,__:[141]})])]),_:1}),s(Te,{gutter:20},{default:n(()=>[s(N,{span:12},{default:n(()=>[s(f,{label:"最大并发用户"},{default:n(()=>[s(me,{modelValue:C.max_concurrent_users,"onUpdate:modelValue":e[32]||(e[32]=l=>C.max_concurrent_users=l),min:1,max:1e3,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),s(N,{span:12},{default:n(()=>[s(f,{label:"带宽限制(Mbps)"},{default:n(()=>[s(me,{modelValue:C.bandwidth_limit_mbps,"onUpdate:modelValue":e[33]||(e[33]=l=>C.bandwidth_limit_mbps=l),min:.1,max:100,step:.1,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),s(f,{label:"高级选项"},{default:n(()=>[s(ke,{modelValue:C.auto_fallback,"onUpdate:modelValue":e[34]||(e[34]=l=>C.auto_fallback=l)},{default:n(()=>e[142]||(e[142]=[m("自动降级",-1)])),_:1,__:[142]},8,["modelValue"]),s(ke,{modelValue:C.is_default,"onUpdate:modelValue":e[35]||(e[35]=l=>C.is_default=l)},{default:n(()=>e[143]||(e[143]=[m("设为默认配置",-1)])),_:1,__:[143]},8,["modelValue"])]),_:1}),H.value?(g(),U(f,{key:0,label:"压缩效果预览"},{default:n(()=>[s(B,{class:"preview-card"},{default:n(()=>[a("div",Uo,[s(Te,{gutter:20},{default:n(()=>[s(N,{span:8},{default:n(()=>[a("div",Ro,[e[144]||(e[144]=a("span",{class:"preview-label"},"预估压缩率:",-1)),a("span",Eo,u(H.value.estimated_rate)+"%",1)])]),_:1}),s(N,{span:8},{default:n(()=>[a("div",Oo,[e[145]||(e[145]=a("span",{class:"preview-label"},"预估速度:",-1)),a("span",Ao,u(H.value.estimated_speed)+" MB/s",1)])]),_:1}),s(N,{span:8},{default:n(()=>[a("div",Bo,[e[146]||(e[146]=a("span",{class:"preview-label"},"内存使用:",-1)),a("span",Io,u(H.value.estimated_memory)+" KB",1)])]),_:1})]),_:1})])]),_:1})]),_:1})):z("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue"]),s(X,{modelValue:Z.value,"onUpdate:modelValue":e[47]||(e[47]=l=>Z.value=l),title:"编辑数据压缩配置",width:"700px"},{footer:n(()=>[a("span",Fo,[s(o,{onClick:e[46]||(e[46]=l=>Z.value=!1)},{default:n(()=>e[154]||(e[154]=[m("取消",-1)])),_:1,__:[154]}),s(o,{type:"primary",onClick:Cs,loading:ge.value},{default:n(()=>[m(u(ge.value?"更新中...":"更新配置"),1)]),_:1},8,["loading"])])]),default:n(()=>[s(T,{model:S,rules:Ge,ref_key:"editCompressionFormRef",ref:He,"label-width":"140px"},{default:n(()=>[s(f,{label:"配置名称",prop:"name"},{default:n(()=>[s(p,{modelValue:S.name,"onUpdate:modelValue":e[38]||(e[38]=l=>S.name=l),placeholder:"请输入配置名称"},null,8,["modelValue"])]),_:1}),s(f,{label:"压缩算法",prop:"algorithm"},{default:n(()=>[s(E,{modelValue:S.algorithm,"onUpdate:modelValue":e[39]||(e[39]=l=>S.algorithm=l),placeholder:"请选择压缩算法"},{default:n(()=>[(g(!0),x(pe,null,Ve(G.value.filter(l=>l.available),l=>(g(),U(_,{key:l.name,label:l.display_name,value:l.name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),s(f,{label:"压缩级别",prop:"level"},{default:n(()=>[s(ut,{modelValue:S.level,"onUpdate:modelValue":e[40]||(e[40]=l=>S.level=l)},{default:n(()=>[s(ee,{label:"low"},{default:n(()=>e[148]||(e[148]=[m("低压缩率 (高速度)",-1)])),_:1,__:[148]}),s(ee,{label:"medium"},{default:n(()=>e[149]||(e[149]=[m("中等压缩率 (平衡)",-1)])),_:1,__:[149]}),s(ee,{label:"high"},{default:n(()=>e[150]||(e[150]=[m("高压缩率 (低速度)",-1)])),_:1,__:[150]})]),_:1},8,["modelValue"])]),_:1}),s(f,{label:"数据流类型",prop:"stream_type"},{default:n(()=>[s(E,{modelValue:S.stream_type,"onUpdate:modelValue":e[41]||(e[41]=l=>S.stream_type=l),placeholder:"请选择数据流类型"},{default:n(()=>[s(_,{label:"专属客户端数据 (仅针对专属客户端连接)",value:"client_data"}),s(_,{label:"通用数据 (其他专属客户端数据流)",value:"general_data"})]),_:1},8,["modelValue"]),a("div",Po,[s(ct,{type:"info",size:"small"},{default:n(()=>e[151]||(e[151]=[m(" 注意：数据压缩仅针对专属客户端连接，不影响分布式节点间通信和Web界面通信 ",-1)])),_:1,__:[151]})])]),_:1}),s(Te,{gutter:20},{default:n(()=>[s(N,{span:12},{default:n(()=>[s(f,{label:"最大并发用户"},{default:n(()=>[s(me,{modelValue:S.max_concurrent_users,"onUpdate:modelValue":e[42]||(e[42]=l=>S.max_concurrent_users=l),min:1,max:1e3,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),s(N,{span:12},{default:n(()=>[s(f,{label:"带宽限制(Mbps)"},{default:n(()=>[s(me,{modelValue:S.bandwidth_limit_mbps,"onUpdate:modelValue":e[43]||(e[43]=l=>S.bandwidth_limit_mbps=l),min:.1,max:100,step:.1,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),s(f,{label:"高级选项"},{default:n(()=>[s(ke,{modelValue:S.auto_fallback,"onUpdate:modelValue":e[44]||(e[44]=l=>S.auto_fallback=l)},{default:n(()=>e[152]||(e[152]=[m("自动降级",-1)])),_:1,__:[152]},8,["modelValue"]),s(ke,{modelValue:S.is_default,"onUpdate:modelValue":e[45]||(e[45]=l=>S.is_default=l)},{default:n(()=>e[153]||(e[153]=[m("设为默认配置",-1)])),_:1,__:[153]},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),s(X,{modelValue:Ze.value,"onUpdate:modelValue":e[48]||(e[48]=l=>Ze.value=l),title:"压缩效果预览",width:"900px"},{default:n(()=>[a("div",No,[s(Te,{gutter:20},{default:n(()=>[s(N,{span:12},{default:n(()=>[s(B,null,{header:n(()=>e[155]||(e[155]=[a("span",null,"算法性能对比",-1)])),default:n(()=>[s(Q,{data:$t.value,style:{width:"100%"}},{default:n(()=>[s(v,{prop:"algorithm",label:"算法",width:"100"}),s(v,{prop:"compression_rate",label:"压缩率",width:"80"},{default:n(l=>[m(u(l.row.compression_rate)+"% ",1)]),_:1}),s(v,{prop:"speed",label:"速度",width:"100"},{default:n(l=>[m(u(l.row.speed)+" MB/s ",1)]),_:1})]),_:1},8,["data"])]),_:1})]),_:1}),s(N,{span:12},{default:n(()=>[s(B,null,{header:n(()=>e[156]||(e[156]=[a("span",null,"网络影响分析",-1)])),default:n(()=>[a("div",jo,[a("div",Wo,[e[157]||(e[157]=a("span",{class:"impact-label"},"带宽节省:",-1)),a("span",Jo,u(Ee.value.bandwidth_saved)+"%",1)]),a("div",qo,[e[158]||(e[158]=a("span",{class:"impact-label"},"延迟增加:",-1)),a("span",Zo,u(Ee.value.latency_increase)+"ms",1)]),a("div",Go,[e[159]||(e[159]=a("span",{class:"impact-label"},"CPU使用:",-1)),a("span",Ho,u(Ee.value.cpu_usage)+"%",1)])])]),_:1})]),_:1})]),_:1})])]),_:1},8,["modelValue"]),s(X,{modelValue:ie.value,"onUpdate:modelValue":e[50]||(e[50]=l=>ie.value=l),title:"导入压缩配置",width:"600px"},{footer:n(()=>[a("span",Ko,[s(o,{onClick:e[49]||(e[49]=l=>ie.value=!1)},{default:n(()=>e[162]||(e[162]=[m("取消",-1)])),_:1,__:[162]}),s(o,{type:"primary",onClick:xs,loading:be.value},{default:n(()=>[m(u(be.value?"导入中...":"导入配置"),1)]),_:1},8,["loading"])])]),default:n(()=>[s(Zs,{class:"upload-demo",drag:"","auto-upload":!1,"on-change":Vs,accept:".json"},{tip:n(()=>e[160]||(e[160]=[a("div",{class:"el-upload__tip"}," 只能上传 JSON 格式的配置文件 ",-1)])),default:n(()=>[s(k,{class:"el-icon--upload"},{default:n(()=>[s(W(Ma))]),_:1}),e[161]||(e[161]=a("div",{class:"el-upload__text"},[m(" 将配置文件拖到此处，或"),a("em",null,"点击上传")],-1))]),_:1,__:[161]})]),_:1},8,["modelValue"])])):z("",!0)],64)}}},kr=Gs(Yo,[["__scopeId","data-v-13cc1329"]]);export{kr as default};
