import{_ as le}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                   *//* empty css                         *//* empty css                    *//* empty css                 *//* empty css               *//* empty css                *//* empty css                 *//* empty css               *//* empty css                  */import{r as b,g as oe,a as ne,aN as ie,o as re,c as u,d as a,a9 as de,e as t,w as l,m as ce,a7 as ue,aa as _e,cK as pe,s as m,b as fe,i as r,n as c,E as me,p as d,dd as ve,L as he,t as _,c$ as ge,cL as ye,ai as we,cY as ke,a5 as D,a6 as C,Y as M,q as Se,dt as Ne,dr as $e,d6 as be,y as T,z as I,cX as F,k as Ee,du as Be,dv as De,dw as R,cR as Ce,d1 as Te,d0 as ze,d3 as xe,a4 as x}from"./index-msvS5Uas.js";const Ve={class:"virtualhere-manager"},Oe={class:"manager-header"},Le={class:"header-left"},Me={class:"manager-title"},Ie={class:"header-right"},Fe={class:"manager-content"},Re={class:"card-header"},Ue={class:"file-list"},He={key:0,class:"empty-state"},je={key:1},Pe={class:"file-info"},Je={class:"file-details"},Ae={class:"file-name"},Ke={class:"file-meta"},Ye={class:"file-size"},qe={class:"file-time"},Ge={class:"file-actions"},Xe={class:"card-header"},Qe={class:"status-info"},We={class:"status-item"},Ze={class:"status-item"},et={class:"status-value"},tt={class:"status-item"},st={class:"status-value"},at={class:"card-header"},lt={class:"header-actions"},ot={class:"upload-slots"},nt={class:"slot-header"},it={class:"slot-title"},rt={class:"slot-actions"},dt={class:"slot-content"},ct={class:"target-name"},ut={class:"upload-area"},_t={class:"upload-actions"},pt={class:"card-header"},ft={class:"history-list"},mt={key:0,class:"empty-state"},vt={class:"history-item"},ht={class:"history-title"},gt={class:"history-description"},yt={key:0,class:"history-files"},wt={__name:"VirtualHereManager",setup(kt){const U=oe(),H=fe(),k=b(!1),S=b(!1),j=U.params.id,p=ne({name:"",vh_status:"unknown",vh_port:7575,vh_version:""}),E=b([]),v=b([{id:Date.now(),targetName:"",fileList:[],file:null}]),y=b([]),P=ie(()=>v.value.some(s=>s.file&&s.targetName.trim())),J=()=>{H.push(`/device-center/slave-server/${j}`)},V=s=>{if(!s)return"0 B";const e=1024,o=["B","KB","MB","GB"],n=Math.floor(Math.log(s)/Math.log(e));return parseFloat((s/Math.pow(e,n)).toFixed(2))+" "+o[n]},O=s=>s?new Date(s).toLocaleString():"N/A",A=s=>({upload:"success",backup:"info",error:"danger",rollback:"warning"})[s]||"info",K=()=>{v.value.push({id:Date.now(),targetName:"",fileList:[],file:null})},Y=s=>{v.value.splice(s,1)},q=(s,e)=>{e.file=s.raw,e.fileList=[s]},G=s=>{s.file=null,s.fileList=[]},L=()=>{v.value.forEach(s=>{s.file=null,s.fileList=[],s.targetName=""})},N=async()=>{k.value=!0;try{const e=await(await fetch("http://localhost:8890/api/files/virtualhere")).json();if(e.status==="success")E.value=e.files||[];else throw new Error(e.message||"获取文件列表失败");k.value=!1}catch(s){console.error("获取文件列表失败:",s),m.error("获取文件列表失败"),E.value=[{name:"vhusbdx86_64",size:2048576,modified:new Date().toISOString()},{name:"vhclientx86_64",size:1024768,modified:new Date(Date.now()-864e5).toISOString()}],k.value=!1}},X=async s=>{try{await x.confirm(`确定要备份文件 "${s.name}" 吗？`,"确认备份",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"});const o=await(await fetch("http://localhost:8890/api/files/backup",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({filename:s.name})})).json();if(o.status==="success")m.success(`文件 "${s.name}" 备份成功`),y.value.unshift({id:Date.now(),type:"backup",title:"文件备份",description:`备份文件: ${s.name} → ${o.backup_file}`,timestamp:new Date().toISOString(),files:[s.name,o.backup_file]}),N();else throw new Error(o.message||"备份失败")}catch(e){e.message!=="cancel"&&(console.error("备份失败:",e),m.error(`备份失败: ${e.message}`))}},Q=async()=>{try{const s=v.value.filter(o=>o.file&&o.targetName.trim());if(s.length===0){m.warning("请选择文件并设置目标名称");return}await x.confirm(`确定要上传并替换 ${s.length} 个文件吗？此操作将备份原文件并替换为新文件。`,"确认上传替换",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),S.value=!0;const e=[];try{for(let o=0;o<s.length;o++){const n=s[o],h=new FormData;h.append("file",n.file),h.append("target_name",n.targetName);const f=await(await fetch("http://localhost:8890/api/files/upload",{method:"POST",body:h})).json();if(f.status!=="success")throw new Error(`上传文件 ${n.file.name} 失败: ${f.message}`);const w=await(await fetch("http://localhost:8890/api/files/replace",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({temp_filename:n.targetName,target_filename:n.targetName})})).json();if(w.status!=="success")throw new Error(`替换文件 ${n.targetName} 失败: ${w.message}`);e.push({originalName:n.file.name,targetName:n.targetName,size:f.size}),y.value.unshift({id:Date.now()+o,type:"upload",title:"文件上传替换",description:`成功替换: ${n.file.name} → ${n.targetName} (${V(f.size)})`,timestamp:new Date().toISOString(),files:[n.targetName]})}S.value=!1,m.success(`成功上传并替换 ${e.length} 个文件`),L(),N()}catch(o){S.value=!1,console.error("上传替换过程中出错:",o),m.error(`上传替换失败: ${o.message}`),y.value.unshift({id:Date.now(),type:"error",title:"文件上传替换失败",description:o.message,timestamp:new Date().toISOString(),files:s.map(n=>n.targetName)})}}catch(s){S.value=!1,s!=="cancel"&&(console.error("上传失败:",s),m.error("文件上传失败"))}},W=async s=>{try{await x.confirm(`确定要回滚文件 "${s.name}" 到备份版本吗？这将覆盖当前文件。`,"确认回滚",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const o=await(await fetch("http://localhost:8890/api/files/rollback",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({filename:s.name})})).json();if(o.status==="success")m.success(`文件 "${s.name}" 回滚成功`),y.value.unshift({id:Date.now(),type:"rollback",title:"文件回滚",description:`回滚文件: ${s.name} 到备份版本`,timestamp:new Date().toISOString(),files:[s.name]}),N();else throw new Error(o.message||"回滚失败")}catch(e){e.message!=="cancel"&&(console.error("回滚失败:",e),m.error(`回滚失败: ${e.message}`))}};return re(()=>{p.name="OmniLink-Slave-Server",p.vh_status="running",p.vh_port=7575,p.vh_version="VirtualHere USB Server 4.3.3",N()}),(s,e)=>{const o=me,n=ce,h=ue,z=ke,f=we,$=ye,w=pe,Z=Ee,ee=Be,te=ze,se=Te,ae=_e;return r(),u("div",Ve,[a("div",Oe,[a("div",Le,[t(n,{onClick:J,type:"text",class:"back-button"},{default:l(()=>[t(o,null,{default:l(()=>[t(d(ve))]),_:1}),e[0]||(e[0]=c(" 返回详情 ",-1))]),_:1,__:[0]}),a("div",Me,[e[1]||(e[1]=a("h2",null,"VirtualHere 程序管理",-1)),t(h,{type:"info",size:"large"},{default:l(()=>[t(o,{class:"status-icon"},{default:l(()=>[t(d(he))]),_:1}),c(" "+_(p.name||"从服务器"),1)]),_:1})])]),a("div",Ie,[t(n,{onClick:N,loading:k.value,type:"primary"},{default:l(()=>[t(o,null,{default:l(()=>[t(d(ge))]),_:1}),e[2]||(e[2]=c(" 刷新文件列表 ",-1))]),_:1,__:[2]},8,["loading"])])]),de((r(),u("div",Fe,[t(w,{gutter:20,class:"content-sections"},{default:l(()=>[t($,{span:12},{default:l(()=>[t(f,{class:"info-card"},{header:l(()=>[a("div",Re,[t(o,null,{default:l(()=>[t(d($e))]),_:1}),e[3]||(e[3]=a("span",null,"当前VirtualHere文件",-1))])]),default:l(()=>[a("div",Ue,[E.value.length===0?(r(),u("div",He,[t(z,{description:"暂无文件信息"})])):(r(),u("div",je,[(r(!0),u(D,null,C(E.value,i=>(r(),u("div",{key:i.name,class:"file-item"},[a("div",Pe,[t(o,{class:"file-icon"},{default:l(()=>[t(d(M))]),_:1}),a("div",Je,[a("div",Ae,_(i.name),1),a("div",Ke,[a("span",Ye,_(V(i.size)),1),a("span",qe,_(O(i.modified)),1)])])]),a("div",Ge,[t(n,{size:"small",onClick:g=>X(i)},{default:l(()=>[t(o,null,{default:l(()=>[t(d(Se))]),_:1}),e[4]||(e[4]=c(" 备份 ",-1))]),_:2,__:[4]},1032,["onClick"]),t(n,{size:"small",type:"warning",onClick:g=>W(i)},{default:l(()=>[t(o,null,{default:l(()=>[t(d(Ne))]),_:1}),e[5]||(e[5]=c(" 回滚 ",-1))]),_:2,__:[5]},1032,["onClick"])])]))),128))]))])]),_:1})]),_:1}),t($,{span:12},{default:l(()=>[t(f,{class:"info-card"},{header:l(()=>[a("div",Xe,[t(o,null,{default:l(()=>[t(d(be))]),_:1}),e[6]||(e[6]=a("span",null,"VirtualHere状态",-1))])]),default:l(()=>[a("div",Qe,[a("div",We,[e[7]||(e[7]=a("span",{class:"status-label"},"运行状态：",-1)),t(h,{type:p.vh_status==="running"?"success":"danger"},{default:l(()=>[c(_(p.vh_status==="running"?"运行中":"已停止"),1)]),_:1},8,["type"])]),a("div",Ze,[e[8]||(e[8]=a("span",{class:"status-label"},"监听端口：",-1)),a("span",et,_(p.vh_port||7575),1)]),a("div",tt,[e[9]||(e[9]=a("span",{class:"status-label"},"当前版本：",-1)),a("span",st,_(p.vh_version||"VirtualHere USB Server 4.3.3"),1)])])]),_:1})]),_:1})]),_:1}),t(w,{gutter:20,class:"content-sections"},{default:l(()=>[t($,{span:24},{default:l(()=>[t(f,{class:"upload-card"},{header:l(()=>[a("div",at,[t(o,null,{default:l(()=>[t(d(R))]),_:1}),e[11]||(e[11]=a("span",null,"程序文件上传",-1)),a("div",lt,[t(n,{size:"small",onClick:K},{default:l(()=>[t(o,null,{default:l(()=>[t(d(Ce))]),_:1}),e[10]||(e[10]=c(" 添加上传槽位 ",-1))]),_:1,__:[10]})])])]),default:l(()=>[a("div",ot,[(r(!0),u(D,null,C(v.value,(i,g)=>(r(),u("div",{key:i.id,class:"upload-slot"},[a("div",nt,[a("div",it,[t(o,null,{default:l(()=>[t(d(M))]),_:1}),a("span",null,"上传槽位 "+_(g+1),1)]),a("div",rt,[v.value.length>1?(r(),T(n,{key:0,size:"small",type:"danger",onClick:B=>Y(g)},{default:l(()=>[t(o,null,{default:l(()=>[t(d(F))]),_:1}),e[12]||(e[12]=c(" 删除 ",-1))]),_:2,__:[12]},1032,["onClick"])):I("",!0)])]),a("div",dt,[a("div",ct,[t(Z,{modelValue:i.targetName,"onUpdate:modelValue":B=>i.targetName=B,placeholder:"目标文件名（如：vhusbdx86_64）",class:"target-input"},{prepend:l(()=>e[13]||(e[13]=[c("目标名称",-1)])),_:2},1032,["modelValue","onUpdate:modelValue"])]),a("div",ut,[t(ee,{ref_for:!0,ref:`upload-${i.id}`,class:"upload-dragger",drag:"","auto-upload":!1,limit:1,"on-change":B=>q(B,i),"on-remove":()=>G(i),"file-list":i.fileList},{tip:l(()=>e[14]||(e[14]=[a("div",{class:"el-upload__tip"}," 支持可执行文件、库文件等，单个文件大小不超过100MB ",-1)])),default:l(()=>[t(o,{class:"el-icon--upload"},{default:l(()=>[t(d(De))]),_:1}),e[15]||(e[15]=a("div",{class:"el-upload__text"},[c(" 将文件拖到此处，或"),a("em",null,"点击上传")],-1))]),_:2,__:[15]},1032,["on-change","on-remove","file-list"])])])]))),128))]),a("div",_t,[t(n,{type:"primary",size:"large",loading:S.value,disabled:!P.value,onClick:Q},{default:l(()=>[t(o,null,{default:l(()=>[t(d(R))]),_:1}),e[16]||(e[16]=c(" 开始上传并替换 ",-1))]),_:1,__:[16]},8,["loading","disabled"]),t(n,{size:"large",onClick:L},{default:l(()=>[t(o,null,{default:l(()=>[t(d(F))]),_:1}),e[17]||(e[17]=c(" 清空所有 ",-1))]),_:1,__:[17]})])]),_:1})]),_:1})]),_:1}),t(w,{gutter:20,class:"content-sections"},{default:l(()=>[t($,{span:24},{default:l(()=>[t(f,{class:"history-card"},{header:l(()=>[a("div",pt,[t(o,null,{default:l(()=>[t(d(xe))]),_:1}),e[18]||(e[18]=a("span",null,"操作历史",-1))])]),default:l(()=>[a("div",ft,[y.value.length===0?(r(),u("div",mt,[t(z,{description:"暂无操作记录"})])):(r(),T(se,{key:1},{default:l(()=>[(r(!0),u(D,null,C(y.value,i=>(r(),T(te,{key:i.id,timestamp:O(i.timestamp),type:A(i.type)},{default:l(()=>[a("div",vt,[a("div",ht,_(i.title),1),a("div",gt,_(i.description),1),i.files?(r(),u("div",yt,[(r(!0),u(D,null,C(i.files,g=>(r(),T(h,{key:g,size:"small"},{default:l(()=>[c(_(g),1)]),_:2},1024))),128))])):I("",!0)])]),_:2},1032,["timestamp","type"]))),128))]),_:1}))])]),_:1})]),_:1})]),_:1})])),[[ae,k.value]])])}}},Vt=le(wt,[["__scopeId","data-v-4a80542c"]]);export{Vt as default};
