import{ap as e}from"./index-BALd70Fs.js";const a={getApplicationTypes(){return e({url:"/api/v1/application-requests/types",method:"get"})},getApplicationRequests(t={}){return e({url:"/api/v1/application-requests/",method:"get",params:t})},getApplicationRequest(t){return e({url:`/api/v1/application-requests/${t}`,method:"get"})},createApplicationRequest(t){return e({url:"/api/v1/application-requests/",method:"post",data:t})},processApplicationRequest(t,p){return e({url:`/api/v1/application-requests/${t}/process`,method:"post",data:p})},getApplicationStats(){return e({url:"/api/v1/application-requests/stats/summary",method:"get"})},forwardApplicationRequest(t,p){return e({url:`/api/v1/application-requests/${t}/forward`,method:"post",data:p})},getAvailableManagers(t={}){return e({url:"/api/v1/application-requests/available-managers",method:"get",params:t})}},s={getApplications:a.getApplicationRequests,getApplication:a.getApplicationRequest,createApplication:a.createApplicationRequest,processApplication:a.processApplicationRequest,forwardApplication:a.forwardApplicationRequest,getAvailableManagers:a.getAvailableManagers,getTypes:a.getApplicationTypes,getStats:a.getApplicationStats};export{a,s as b};
