import{an as e}from"./index-BGppeauV.js";const r={getApplicationTypes(){return e({url:"/api/v1/application-requests/types",method:"get"})},getApplicationRequests(t={}){return e({url:"/api/v1/application-requests/",method:"get",params:t})},getApplicationRequest(t){return e({url:`/api/v1/application-requests/${t}`,method:"get"})},createApplicationRequest(t){return e({url:"/api/v1/application-requests/",method:"post",data:t})},processApplicationRequest(t,a){return e({url:`/api/v1/application-requests/${t}/process`,method:"post",data:a})},getApplicationStats(){return e({url:"/api/v1/application-requests/stats/summary",method:"get"})},forwardApplicationRequest(t,a){return e({url:`/api/v1/application-requests/${t}/forward`,method:"post",data:a})},getAvailableManagers(t={}){return e({url:"/api/v1/application-requests/available-managers",method:"get",params:t})}};export{r as a};
