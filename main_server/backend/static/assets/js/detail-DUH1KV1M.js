import{_ as X}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                   *//* empty css                     *//* empty css                 *//* empty css                    *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                     *//* empty css               *//* empty css                *//* empty css                  */import{r as u,a as Y,a9 as ee,o as te,c as ae,d as o,e as t,w as a,m as le,t as _,ad as se,x as oe,g as ne,s as c,b as de,i as ie,n,E as re,p,dd as ue,cV as pe,cR as N,dg as T,L as _e,R as ce,d6 as ve,cZ as me,c_ as fe,ah as ge,ak as we,d7 as L,a7 as be,f as ye,j as ke,k as Ce,a4 as Ve}from"./index-BALd70Fs.js";import{a as g}from"./index-AiBR6hvT.js";const $e={class:"device-group-detail"},Ee={class:"page-header"},De={class:"header-left"},he={class:"header-info"},xe={class:"header-actions"},Ie={class:"stats-cards"},ze={class:"stat-content"},Te={class:"stat-info"},Ue={class:"stat-value"},Be={class:"stat-content"},Pe={class:"stat-info"},Se={class:"stat-value"},Ge={class:"stat-content"},Ne={class:"stat-info"},Le={class:"stat-value"},Re={class:"main-content"},Ae={class:"tab-header"},Fe={class:"device-name"},Me={class:"tab-header"},je={__name:"detail",setup(qe){const m=ne(),R=de(),w=u({}),y=u([]),E=u([]),A=u([]),U=u([]);u([]);const k=u([]),D=u(!1),h=u(!1),C=u(!1),b=u(!1),f=u(!1),v=Y({name:"",description:""}),F=ee(()=>y.value.filter(l=>l.status==="available").length),M=()=>{R.go(-1)},B=l=>l?new Date(l).toLocaleString("zh-CN"):"N/A",j=async()=>{try{const l=m.params.id,e=await g.get(`/api/v1/device-groups/${l}`);e.data.success&&(w.value=e.data.data,v.name=w.value.name,v.description=w.value.description)}catch(l){console.error("加载分组详情失败:",l),c.error("加载分组详情失败")}},q=async()=>{try{D.value=!0;const l=m.params.id,e=await g.get(`/api/v1/device-groups/${l}/devices`);e.data.success&&(y.value=e.data.data)}catch(l){console.error("加载分组设备失败:",l),c.error("加载分组设备失败")}finally{D.value=!1}},x=async()=>{try{h.value=!0;const l=m.params.id,e=await g.get(`/api/v1/device-groups/${l}/permissions`);E.value=e.data||[]}catch(l){console.error("加载分组权限失败:",l),c.error("加载分组权限失败")}finally{h.value=!1}},Z=async()=>{try{const l=m.params.id,e=await g.get(`/api/v1/device-groups/${l}/available-users`);U.value=e.data||[]}catch(l){console.error("加载可分配用户失败:",l),c.error("加载可分配用户失败")}},H=l=>{k.value=l},J=async()=>{if(k.value.length===0){c.warning("请选择要分配权限的用户");return}try{const l=m.params.id,e=k.value.map(i=>i.id);await g.post(`/api/v1/device-groups/${l}/permissions`,{user_ids:e,permission_type:"use",expires_at:null}),c.success("权限分配成功"),f.value=!1,k.value=[],await x()}catch(l){console.error("分配权限失败:",l),c.error("分配权限失败")}},K=async l=>{try{await Ve.confirm(`确定要撤销用户"${l.username}"的权限吗？`,"撤销确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=m.params.id;await g.delete(`/api/v1/device-groups/${e}/permissions/${l.id}`),c.success("权限撤销成功"),await x()}catch(e){e!=="cancel"&&(console.error("撤销权限失败:",e),c.error("撤销权限失败"))}};return te(()=>{j(),q(),x(),Z()}),(l,e)=>{const i=re,r=le,V=se,d=we,I=be,$=ge,P=fe,O=me,S=Ce,G=ke,Q=ye,z=oe;return ie(),ae("div",$e,[o("div",Ee,[o("div",De,[t(r,{type:"text",onClick:M,class:"back-button"},{default:a(()=>[t(i,null,{default:a(()=>[t(p(ue))]),_:1}),e[13]||(e[13]=n(" 返回 ",-1))]),_:1,__:[13]}),o("div",he,[o("h2",null,_(w.value.name||"设备分组详情"),1),o("p",null,_(w.value.description||"暂无描述"),1)])]),o("div",xe,[t(r,{type:"primary",onClick:e[0]||(e[0]=s=>C.value=!0)},{default:a(()=>[t(i,null,{default:a(()=>[t(p(pe))]),_:1}),e[14]||(e[14]=n(" 编辑分组 ",-1))]),_:1,__:[14]}),t(r,{type:"success",onClick:e[1]||(e[1]=s=>b.value=!0)},{default:a(()=>[t(i,null,{default:a(()=>[t(p(N))]),_:1}),e[15]||(e[15]=n(" 添加设备 ",-1))]),_:1,__:[15]}),t(r,{type:"warning",onClick:e[2]||(e[2]=s=>f.value=!0)},{default:a(()=>[t(i,null,{default:a(()=>[t(p(T))]),_:1}),e[16]||(e[16]=n(" 分配权限 ",-1))]),_:1,__:[16]})])]),o("div",Ie,[t(V,{class:"stat-card"},{default:a(()=>[o("div",ze,[t(i,{class:"stat-icon"},{default:a(()=>[t(p(_e))]),_:1}),o("div",Te,[o("div",Ue,_(y.value.length),1),e[17]||(e[17]=o("div",{class:"stat-label"},"设备总数",-1))])])]),_:1}),t(V,{class:"stat-card"},{default:a(()=>[o("div",Be,[t(i,{class:"stat-icon"},{default:a(()=>[t(p(ce))]),_:1}),o("div",Pe,[o("div",Se,_(E.value.length),1),e[18]||(e[18]=o("div",{class:"stat-label"},"授权用户",-1))])])]),_:1}),t(V,{class:"stat-card"},{default:a(()=>[o("div",Ge,[t(i,{class:"stat-icon"},{default:a(()=>[t(p(ve))]),_:1}),o("div",Ne,[o("div",Le,_(F.value),1),e[19]||(e[19]=o("div",{class:"stat-label"},"在线设备",-1))])])]),_:1})]),o("div",Re,[t(V,{class:"content-card"},{default:a(()=>[t(O,{type:"border-card"},{default:a(()=>[t(P,{label:"分组设备"},{label:a(()=>[o("span",null,[t(i,null,{default:a(()=>[t(p(L))]),_:1}),e[20]||(e[20]=n(" 分组设备",-1))])]),default:a(()=>[o("div",Ae,[t(r,{type:"primary",size:"small",onClick:e[3]||(e[3]=s=>b.value=!0)},{default:a(()=>[t(i,null,{default:a(()=>[t(p(N))]),_:1}),e[21]||(e[21]=n(" 添加设备 ",-1))]),_:1,__:[21]})]),t($,{data:y.value,style:{width:"100%"},loading:D.value},{default:a(()=>[t(d,{prop:"device_name",label:"设备名称","min-width":"150"},{default:a(s=>[o("div",Fe,[t(i,{class:"device-icon"},{default:a(()=>[t(p(L))]),_:1}),n(" "+_(s.row.device_name||"未知设备"),1)])]),_:1}),t(d,{prop:"device_type",label:"设备类型",width:"120"},{default:a(s=>[t(I,{size:"small"},{default:a(()=>[n(_(s.row.device_type||"未知"),1)]),_:2},1024)]),_:1}),t(d,{prop:"status",label:"状态",width:"100"},{default:a(s=>[t(I,{type:s.row.status==="available"?"success":"warning",size:"small"},{default:a(()=>[n(_(s.row.status==="available"?"可用":"占用"),1)]),_:2},1032,["type"])]),_:1}),t(d,{prop:"slave_server_name",label:"所属服务器",width:"150"}),t(d,{prop:"added_at",label:"添加时间",width:"180"},{default:a(s=>[n(_(B(s.row.added_at)),1)]),_:1}),t(d,{label:"操作",width:"120"},{default:a(s=>[t(r,{size:"small",type:"danger",onClick:W=>l.removeDevice(s.row)},{default:a(()=>e[22]||(e[22]=[n(" 移除 ",-1)])),_:2,__:[22]},1032,["onClick"])]),_:1})]),_:1},8,["data","loading"])]),_:1}),t(P,{label:"用户权限"},{label:a(()=>[o("span",null,[t(i,null,{default:a(()=>[t(p(T))]),_:1}),e[23]||(e[23]=n(" 用户权限",-1))])]),default:a(()=>[o("div",Me,[t(r,{type:"primary",size:"small",onClick:e[4]||(e[4]=s=>f.value=!0)},{default:a(()=>[t(i,null,{default:a(()=>[t(p(T))]),_:1}),e[24]||(e[24]=n(" 分配权限 ",-1))]),_:1,__:[24]})]),t($,{data:E.value,style:{width:"100%"},loading:h.value},{default:a(()=>[t(d,{prop:"username",label:"用户名",width:"150"}),t(d,{prop:"role_name",label:"角色",width:"120"},{default:a(s=>[t(I,{size:"small"},{default:a(()=>[n(_(s.row.role_name),1)]),_:2},1024)]),_:1}),t(d,{prop:"organization_name",label:"组织",width:"150"}),t(d,{prop:"granted_at",label:"授权时间",width:"180"},{default:a(s=>[n(_(B(s.row.granted_at)),1)]),_:1}),t(d,{label:"操作",width:"120"},{default:a(s=>[t(r,{size:"small",type:"danger",onClick:W=>K(s.row)},{default:a(()=>e[25]||(e[25]=[n(" 撤销 ",-1)])),_:2,__:[25]},1032,["onClick"])]),_:1})]),_:1},8,["data","loading"])]),_:1})]),_:1})]),_:1})]),t(z,{modelValue:C.value,"onUpdate:modelValue":e[8]||(e[8]=s=>C.value=s),title:"编辑设备分组",width:"500px"},{footer:a(()=>[t(r,{onClick:e[7]||(e[7]=s=>C.value=!1)},{default:a(()=>e[26]||(e[26]=[n("取消",-1)])),_:1,__:[26]}),t(r,{type:"primary",onClick:l.updateGroup},{default:a(()=>e[27]||(e[27]=[n("确定",-1)])),_:1,__:[27]},8,["onClick"])]),default:a(()=>[t(Q,{model:v,"label-width":"100px"},{default:a(()=>[t(G,{label:"分组名称",required:""},{default:a(()=>[t(S,{modelValue:v.name,"onUpdate:modelValue":e[5]||(e[5]=s=>v.name=s),placeholder:"请输入分组名称"},null,8,["modelValue"])]),_:1}),t(G,{label:"分组描述"},{default:a(()=>[t(S,{modelValue:v.description,"onUpdate:modelValue":e[6]||(e[6]=s=>v.description=s),type:"textarea",rows:3,placeholder:"请输入分组描述"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),t(z,{modelValue:b.value,"onUpdate:modelValue":e[10]||(e[10]=s=>b.value=s),title:"添加设备到分组",width:"600px"},{footer:a(()=>[t(r,{onClick:e[9]||(e[9]=s=>b.value=!1)},{default:a(()=>e[28]||(e[28]=[n("取消",-1)])),_:1,__:[28]}),t(r,{type:"primary",onClick:l.addDevicesToGroup},{default:a(()=>e[29]||(e[29]=[n("确定",-1)])),_:1,__:[29]},8,["onClick"])]),default:a(()=>[t($,{data:A.value,style:{width:"100%"},onSelectionChange:l.handleDeviceSelection},{default:a(()=>[t(d,{type:"selection",width:"55"}),t(d,{prop:"device_name",label:"设备名称","min-width":"150"}),t(d,{prop:"device_type",label:"设备类型",width:"120"}),t(d,{prop:"slave_server_name",label:"所属服务器",width:"150"})]),_:1},8,["data","onSelectionChange"])]),_:1},8,["modelValue"]),t(z,{modelValue:f.value,"onUpdate:modelValue":e[12]||(e[12]=s=>f.value=s),title:"分配用户权限",width:"600px"},{footer:a(()=>[t(r,{onClick:e[11]||(e[11]=s=>f.value=!1)},{default:a(()=>e[30]||(e[30]=[n("取消",-1)])),_:1,__:[30]}),t(r,{type:"primary",onClick:J},{default:a(()=>e[31]||(e[31]=[n("确定",-1)])),_:1,__:[31]})]),default:a(()=>[t($,{data:U.value,style:{width:"100%"},onSelectionChange:H},{default:a(()=>[t(d,{type:"selection",width:"55"}),t(d,{prop:"username",label:"用户名",width:"150"}),t(d,{prop:"role_name",label:"角色",width:"120"}),t(d,{prop:"organization_name",label:"组织",width:"150"})]),_:1},8,["data"])]),_:1},8,["modelValue"])])}}},ot=X(je,[["__scopeId","data-v-c2565929"]]);export{ot as default};
