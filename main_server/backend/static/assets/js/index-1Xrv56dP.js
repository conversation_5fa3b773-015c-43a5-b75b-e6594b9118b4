import{_ as ie}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                   *//* empty css                  *//* empty css               *//* empty css                 *//* empty css                *//* empty css                *//* empty css                    *//* empty css                *//* empty css               *//* empty css                  *//* empty css                 */import{ap as te,u as ce,r as j,a as ne,a9 as Q,aa as he,o as re,c as p,i as l,d as o,ab as le,t as k,e as c,w as r,n as I,p as u,c$ as W,m as de,cX as q,ac as ue,a5 as ae,a6 as oe,a2 as ee,N as ve,a7 as _e,db as ye,z as X,dg as Ue,E as me,an as ke,cY as ge,s as h,a4 as Y,cK as De,dc as $e,cL as Ie,ad as Ce,cS as we,y as F,Z as be,_ as xe,R as Me,cT as ze,k as Te,bs as Ee,d2 as Le,bA as Be}from"./index-Bcq_EQlf.js";/* empty css                 *//* empty css                         *//* empty css                  */import{u as Ae}from"./useOrganizationTree-B4e9krnm.js";import"./permission-assignment-CIHkuTWb.js";import"./index-BZj090F-.js";function Fe(C){return te({url:`/api/v1/client-management/users/${C}/devices`,method:"get"})}function Pe(C,w){return te({url:`/api/v1/client-management/users/${C}/devices/${w}/clear-uuid`,method:"post"})}function se(C,w){return te({url:`/api/v1/client-management/users/${C}/devices/batch-clear-uuid`,method:"post",data:{device_ids:w}})}const Ne={class:"uuid-manager-multiple"},Se={class:"users-section"},Re={class:"section-header"},Ve={class:"header-actions"},Oe={class:"users-list"},Ke={class:"user-header"},je={class:"user-info"},qe={class:"user-details"},Xe={class:"user-name"},Ye={class:"user-meta"},Ze={class:"user-org"},Ge={class:"user-actions"},He={class:"devices-section"},Je={class:"devices-header"},Qe={class:"devices-title"},We={key:0,class:"uuid-count"},es={key:1,class:"no-uuid"},ss={key:0,class:"loading-devices"},ts={key:1,class:"uuid-list"},ns={class:"uuid-info"},as={class:"uuid-details"},os={class:"uuid-value"},is={class:"uuid-meta"},cs={class:"device-name"},rs={class:"login-time"},ls={class:"uuid-actions"},ds={key:0,class:"hidden-uuid-notice"},us={key:2,class:"empty-uuid"},_s={__name:"UUIDManagerMultiple",props:{users:{type:Array,required:!0,default:()=>[]},canManageFunction:{type:Function,required:!0}},emits:["uuid-cleared"],setup(C,{emit:w}){const D=C,P=w,Z=ce(),{getPermissionLevel:N}=Z,x=j(!1),v=ne({}),M=ne({}),L=Q(()=>D.users.some(e=>D.canManageFunction(e))),S=e=>{switch(e){case"全域管理员":return"danger";case"超级管理员":return"warning";case"管理员":return"primary";case"普通用户":return"success";case"新用户":return"info";default:return"info"}},R=(e,n)=>{if(!e)return"无";const _=N.value;if(!D.canManageFunction(n))return"无权限查看";if(_<=1)return e;if(_===2)if(e.length<=12){const i=Math.floor(e.length/2),a=e.substring(0,i),t=e.substring(e.length-i),d=Math.max(6,e.length-i*2);return`${a}${"*".repeat(d)}${t}`}else{const i=e.substring(0,6),a=e.substring(e.length-6),t=Math.max(6,e.length-12);return`${i}${"*".repeat(t)}${a}`}else return"无权限查看"},G=e=>{if(!e)return"未知";try{return new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}catch{return"时间格式错误"}},y=(e,n)=>{if(!e||e.length===0)return{devices:[],hiddenCount:0};const _=N.value;return D.canManageFunction(n)?_<=1?{devices:e,hiddenCount:0}:_===2?e.length<=4?{devices:e,hiddenCount:0}:{devices:e.slice(0,4),hiddenCount:e.length-4}:{devices:[],hiddenCount:0}:{devices:[],hiddenCount:0}},B=async e=>{M[e.id]=!0;try{const n=await Fe(e.id);n.success?v[e.id]=n.data:v[e.id]=[]}catch(n){console.error(`加载用户 ${e.username} 的设备失败:`,n),v[e.id]=[]}finally{M[e.id]=!1}},A=async e=>{await B(e)},T=async()=>{x.value=!0;try{const e=D.users.map(n=>B(n));await Promise.all(e)}finally{x.value=!1}},E=async(e,n)=>{var _,m;if(!D.canManageFunction(e)){h.warning("您没有权限清空此用户的UUID");return}try{await Y.confirm(`确定要清空用户 "${e.full_name||e.username}" 设备 "${n.device_name||"未知设备"}" 的UUID吗？此操作不可撤销。`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const i=await Pe(e.id,n.id);i&&i.success?h.success(`设备 "${i.device_name||n.device_name}" UUID清空成功`):h.success("清空UUID成功"),P("uuid-cleared"),await A(e)}catch(i){if(i!=="cancel"){console.error("清空UUID失败:",i);const a=((m=(_=i.response)==null?void 0:_.data)==null?void 0:m.detail)||i.message||"清空UUID失败";h.error(a)}}},H=async e=>{var _,m;if(!D.canManageFunction(e)){h.warning("您没有权限清空此用户的UUID");return}const n=v[e.id]||[];if(n.length===0){h.warning("该用户暂无设备");return}try{await Y.confirm(`确定要清空用户 "${e.full_name||e.username}" 的所有 ${n.length} 个设备UUID吗？此操作不可撤销。`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const i=n.map(t=>t.id),a=await se(e.id,i);a&&a.success?h.success(`成功清空用户 "${a.user_name||e.username}" 的 ${a.cleared_count} 个设备UUID`):h.success("清空用户所有UUID成功"),P("uuid-cleared"),await A(e)}catch(i){if(i!=="cancel"){console.error("批量清空UUID失败:",i);const a=((m=(_=i.response)==null?void 0:_.data)==null?void 0:m.detail)||i.message||"清空UUID失败";h.error(a)}}},J=async()=>{var n,_;const e=D.users.filter(m=>D.canManageFunction(m));if(e.length===0){h.warning("您没有权限清空所选用户的UUID");return}try{await Y.confirm(`确定要清空所有选中用户的UUID吗？此操作将影响 ${e.length} 个用户，且不可撤销。`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),x.value=!0;let m=0,i=0,a=0;const t=e.map(async d=>{try{const f=(v[d.id]||[]).map(g=>g.id),s=await se(d.id,f);s&&s.success&&(m+=s.cleared_count||0,i++)}catch($){console.error(`清空用户 ${d.username} UUID失败:`,$),a++}});await Promise.all(t),a===0?h.success(`成功清空 ${i} 个用户的 ${m} 个设备UUID`):h.warning(`操作完成：成功 ${i} 个用户，失败 ${a} 个用户`),P("uuid-cleared"),await T()}catch(m){if(m!=="cancel"){console.error("批量清空UUID失败:",m);const i=((_=(n=m.response)==null?void 0:n.data)==null?void 0:_.detail)||m.message||"批量清空UUID失败";h.error(i)}}finally{x.value=!1}};return he(()=>D.users,e=>{e&&e.length>0?T():(Object.keys(v).forEach(n=>{delete v[n]}),Object.keys(M).forEach(n=>{delete M[n]}))},{immediate:!0}),re(()=>{D.users&&D.users.length>0&&T()}),(e,n)=>{const _=de,m=ve,i=_e,a=ye,t=me,d=ke,$=ge,f=ue;return l(),p("div",Ne,[o("div",Se,[o("div",Re,[o("h4",null,"选中用户 ("+k(C.users.length)+")",1),o("div",Ve,[c(_,{onClick:T,loading:x.value,size:"small",icon:u(W)},{default:r(()=>n[0]||(n[0]=[I(" 刷新全部 ",-1)])),_:1,__:[0]},8,["loading","icon"]),c(_,{type:"danger",size:"small",onClick:J,disabled:!L.value,icon:u(q)},{default:r(()=>n[1]||(n[1]=[I(" 全部清空 ",-1)])),_:1,__:[1]},8,["disabled","icon"])])]),le((l(),p("div",Oe,[(l(!0),p(ae,null,oe(C.users,s=>(l(),p("div",{key:s.id,class:ee(["user-card",{"no-permission":!C.canManageFunction(s)}])},[o("div",Ke,[o("div",je,[c(m,{size:32},{default:r(()=>{var g,b;return[I(k(((g=s.full_name)==null?void 0:g.charAt(0))||((b=s.username)==null?void 0:b.charAt(0))),1)]}),_:2},1024),o("div",qe,[o("div",Xe,k(s.full_name||s.username),1),o("div",Ye,[c(i,{type:S(s.role_name),size:"small"},{default:r(()=>[I(k(s.role_name),1)]),_:2},1032,["type"]),o("span",Ze,k(s.organization_name),1)])])]),o("div",Ge,[c(_,{type:"danger",size:"small",onClick:g=>H(s),disabled:!C.canManageFunction(s),icon:u(q)},{default:r(()=>n[2]||(n[2]=[I(" 清空UUID ",-1)])),_:2,__:[2]},1032,["onClick","disabled","icon"])])]),o("div",He,[o("div",Je,[o("span",Qe,[n[3]||(n[3]=I(" 被绑定的UUID号： ",-1)),v[s.id]&&v[s.id].length>0?(l(),p("span",We," ("+k(v[s.id].length)+"个) ",1)):(l(),p("span",es,"无"))]),c(_,{onClick:g=>A(s),loading:M[s.id],size:"small",text:"",icon:u(W)},{default:r(()=>n[4]||(n[4]=[I(" 刷新 ",-1)])),_:2,__:[4]},1032,["onClick","loading","icon"])]),M[s.id]?(l(),p("div",ss,[c(a,{rows:2,animated:""})])):v[s.id]&&v[s.id].length>0?(l(),p("div",ts,[(l(!0),p(ae,null,oe(y(v[s.id],s).devices,g=>(l(),p("div",{key:g.id,class:"uuid-item"},[o("div",ns,[c(t,{class:"uuid-icon"},{default:r(()=>[c(u(Ue))]),_:1}),o("div",as,[o("div",os,k(R(g.device_fingerprint,s)),1),o("div",is,[o("span",cs,"设备: "+k(g.device_name||"未知设备"),1),c(i,{type:g.is_trusted?"success":"warning",size:"small"},{default:r(()=>[I(k(g.is_trusted?"可信":"待审核"),1)]),_:2},1032,["type"]),o("span",rs," 登录时间: "+k(G(g.created_at)),1)])])]),o("div",ls,[c(_,{type:"danger",size:"small",onClick:b=>E(s,g),disabled:!C.canManageFunction(s),icon:u(q)},{default:r(()=>n[5]||(n[5]=[I(" 清空此UUID ",-1)])),_:2,__:[5]},1032,["onClick","disabled","icon"])])]))),128)),y(v[s.id],s).hiddenCount>0?(l(),p("div",ds,[c(d,{title:`还有 ${y(v[s.id],s).hiddenCount} 个UUID未显示`,type:"info",closable:!1,"show-icon":""},{default:r(()=>n[6]||(n[6]=[o("span",null,"由于权限限制，仅显示前4个UUID。如需查看全部UUID，请联系全域管理员。",-1)])),_:2},1032,["title"])])):X("",!0)])):(l(),p("div",us,[c($,{description:"该用户暂未绑定任何UUID","image-size":60})]))])],2))),128))])),[[f,x.value]])])])}}},ms=ie(_s,[["__scopeId","data-v-ec335bf3"]]),gs={key:0,class:"client-management-container"},ps={class:"main-content"},fs={class:"card-header"},hs={class:"header-actions"},vs={class:"organization-tree-container"},ys=["data-type"],Us={class:"node-info"},ks={class:"node-label"},Ds={key:1,class:"username-info"},$s={class:"card-header"},Is={key:0,class:"header-actions"},Cs={key:0,class:"uuid-management-content"},ws={key:1,class:"empty-state"},bs={key:1,class:"access-denied-container"},xs={__name:"index",setup(C){const w=ce(),{getPermissionLevel:D,canAccessLoginManagementTab:P}=w,Z=Q(()=>{const a=w.isLoggedIn,t=w.canAccessLoginManagementTab;return w.getPermissionLevel,a&&t}),{filteredTree:N,loading:x,expandedKeys:v,defaultExpandedKeys:M,searchText:L,allExpanded:S,refreshData:R,expandAll:G}=Ae({autoLoad:!0,enableCache:!0,maxExpandLevel:3}),y=j([]);j([]);const B=j(null),A={children:"children",label:"name"},T=Q(()=>y.value.some(a=>E(a))),E=a=>{const t=D.value,d=a.permission_level||4;return t===0?!0:t===1&&d===0?!1:t===1?!0:t===2?d>=2:!1},H=a=>{const t="node-icon";switch(a.type){case"organization":return`${t} org-icon`;case"admin_group":return`${t} admin-icon`;case"normal_user_group":return`${t} user-group-icon`;case"user":return`${t} user-icon`;default:return t}},J=a=>{switch(a){case"全域管理员":return"danger";case"超级管理员":return"warning";case"管理员":return"primary";case"普通用户":return"success";case"新用户":return"info";default:return"info"}},e=(a,t)=>{a.type==="user"?y.value.some($=>$.id===a.id)||(y.value=[a]):t.expanded=!t.expanded},n=(a,t,d)=>{if(a.type!=="user"&&t){Be(()=>{B.value.setChecked(a.id,!1,!1)});return}},_=(a,t)=>{if(a.type!=="user")return;const d=[],$=f=>{f.forEach(s=>{s.type==="user"&&d.push(s),s.children&&s.children.length>0&&$(s.children)})};t.checkedNodes.forEach(f=>{f.type==="user"?d.push(f):f.children&&$(f.children)}),y.value=d},m=()=>{R()},i=async()=>{var $,f;if(y.value.length===0){h.warning("请先选择要清空UUID的用户");return}const a=y.value.filter(s=>E(s)),t=y.value.filter(s=>!E(s));if(a.length===0){h.warning("您没有权限清空所选用户的UUID");return}let d=`确定要清空选中的 ${a.length} 个用户的所有UUID吗？此操作不可撤销。`;t.length>0&&(d+=`

注意：${t.length} 个用户因权限不足将被跳过。`);try{await Y.confirm(d,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});let s=0,g=0,b=0;const V=a.map(async O=>{try{const z=await se(O.id,[]);z&&z.success&&(s+=z.cleared_count||0,g++)}catch(z){console.error(`清空用户 ${O.username} UUID失败:`,z),b++}});await Promise.all(V),b===0?h.success(`成功清空 ${g} 个用户的 ${s} 个设备UUID`):h.warning(`操作完成：成功 ${g} 个用户，失败 ${b} 个用户`),y.value=[],m()}catch(s){if(s!=="cancel"){console.error("批量清空UUID失败:",s);const g=((f=($=s.response)==null?void 0:$.data)==null?void 0:f.detail)||s.message||"批量清空UUID失败";h.error(g)}}};return re(()=>{}),(a,t)=>{const d=me,$=Te,f=de,s=_e,g=we,b=Ce,V=Ie,O=ge,z=De,pe=$e,fe=ue;return Z.value?(l(),p("div",gs,[t[5]||(t[5]=o("div",{class:"page-header"},[o("h2",null,"设备绑定中心"),o("p",null,"管理用户设备绑定和UUID信息")],-1)),o("div",ps,[c(z,{gutter:20,class:"content-row"},{default:r(()=>[c(V,{span:8},{default:r(()=>[c(b,{class:"organization-card"},{header:r(()=>[o("div",fs,[t[3]||(t[3]=o("span",{class:"header-title"},"组织架构",-1)),o("div",hs,[c($,{modelValue:u(L),"onUpdate:modelValue":t[0]||(t[0]=K=>Ee(L)?L.value=K:null),placeholder:"搜索组织或用户",size:"small",style:{width:"200px"},clearable:""},{prefix:r(()=>[c(d,null,{default:r(()=>[c(u(Le))]),_:1})]),_:1},8,["modelValue"]),c(f,{onClick:u(G),size:"small",icon:u(S)?"Minus":"Plus"},{default:r(()=>[I(k(u(S)?"收起":"展开"),1)]),_:1},8,["onClick","icon"]),c(f,{onClick:u(R),loading:u(x),size:"small",icon:u(W)},{default:r(()=>t[2]||(t[2]=[I(" 刷新 ",-1)])),_:1,__:[2]},8,["onClick","loading","icon"])])])]),default:r(()=>[le((l(),p("div",vs,[c(g,{ref_key:"orgTreeRef",ref:B,data:u(N),props:A,"node-key":"id","default-expanded-keys":u(M),"expanded-keys":u(v),"expand-on-click-node":!1,"check-on-click-node":!1,"show-checkbox":"","check-strictly":!0,"allow-drop":()=>!1,"allow-drag":()=>!1,onCheck:_,onNodeClick:e,onCheckChange:n,class:"organization-tree"},{default:r(({node:K,data:U})=>[o("div",{class:ee(["tree-node",{"is-user":U.type==="user","is-organization":U.type==="organization","is-role-group":U.type==="admin_group"||U.type==="normal_user_group"}]),"data-type":U.type},[o("div",Us,[c(d,{class:ee(["node-icon",H(U)])},{default:r(()=>[U.type==="organization"?(l(),F(u(be),{key:0})):U.type==="admin_group"?(l(),F(u(xe),{key:1})):U.type==="normal_user_group"?(l(),F(u(Me),{key:2})):(l(),F(u(ze),{key:3}))]),_:2},1032,["class"]),o("span",ks,k(K.label),1),U.type==="user"?(l(),F(s,{key:0,type:J(U.role_name),size:"small",class:"role-tag"},{default:r(()=>[I(k(U.role_name),1)]),_:2},1032,["type"])):X("",!0),U.type==="user"&&U.username?(l(),p("span",Ds," ("+k(U.username)+") ",1)):X("",!0)])],10,ys)]),_:1},8,["data","default-expanded-keys","expanded-keys"])])),[[fe,u(x)]])]),_:1})]),_:1}),c(V,{span:16},{default:r(()=>[c(b,{class:"uuid-management-card"},{header:r(()=>[o("div",$s,[t[4]||(t[4]=o("span",{class:"header-title"},"UUID管理",-1)),y.value.length>0?(l(),p("div",Is,[c(f,{type:"danger",size:"small",onClick:i,disabled:!T.value},{default:r(()=>[c(d,null,{default:r(()=>[c(u(q))]),_:1}),I(" 批量清空UUID ("+k(y.value.length)+"个用户) ",1)]),_:1},8,["disabled"])])):X("",!0)])]),default:r(()=>[y.value.length>0?(l(),p("div",Cs,[c(ms,{users:y.value,"can-manage-function":E,onUuidCleared:m},null,8,["users"])])):(l(),p("div",ws,[c(O,{description:"请从左侧选择用户以查看其设备信息"})]))]),_:1})]),_:1})]),_:1})])])):(l(),p("div",bs,[c(pe,{icon:"warning",title:"访问被拒绝","sub-title":"您没有权限访问此页面，请联系管理员获取相应权限。"},{extra:r(()=>[c(f,{type:"primary",onClick:t[1]||(t[1]=K=>a.$router.push("/dashboard"))},{default:r(()=>t[6]||(t[6]=[I(" 返回工作台 ",-1)])),_:1,__:[6]})]),_:1})]))}}},Zs=ie(xs,[["__scopeId","data-v-3b809d35"]]);export{Zs as default};
