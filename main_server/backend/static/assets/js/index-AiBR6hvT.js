import{dB as n,u as t,s as r,dC as u}from"./index-BALd70Fs.js";const a=n.create({baseURL:"",timeout:1e4,headers:{"Content-Type":"application/json"}});a.interceptors.request.use(e=>{const s=t().token;return s&&(e.headers.Authorization=`Bearer ${s}`),e},e=>(console.error("请求拦截器错误:",e),Promise.reject(e)));a.interceptors.response.use(e=>e,e=>{if(console.error("响应拦截器错误:",e),e.response){const{status:o,data:s}=e.response;switch(o){case 401:r.error("登录已过期，请重新登录"),t().logout(),u.push("/login");break;case 403:r.error("权限不足");break;case 404:r.error("请求的资源不存在");break;case 500:r.error("服务器内部错误");break;default:r.error((s==null?void 0:s.message)||"请求失败")}}else e.request?r.error("网络连接失败"):r.error("请求配置错误");return Promise.reject(e)});export{a};
