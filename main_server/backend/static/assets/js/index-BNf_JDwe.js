import{_ as mt}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                 *//* empty css                    *//* empty css                  *//* empty css               *//* empty css                */import{r as c,u as dt,aN as ce,o as ut,bM as Dt,s as f,a as qe,cA as bt,y as B,i as _,w as t,a9 as at,c as L,d as s,z as J,e,E as ft,p as k,L as _t,t as m,a7 as kt,n as d,S as Cs,R as Ot,dh as jt,di as Ts,m as gt,dj as Ss,d3 as rs,Y as xs,dk as Yt,aa as wt,x as At,a4 as Re,b as Nt,c$ as vt,dl as Vs,O as Ds,P as As,Q as zs,M as Es,k as zt,d2 as pt,ad as Et,ae as Ut,a5 as Oe,a6 as st,ai as Zt,cN as ps,cO as _s,dm as Gt,dn as Us,dp as Ps,ac as Ft,af as Ht,a2 as Vt,ab as Qt,cY as vs,dc as Ls,C as Is,cR as Xt,cX as js,D as Os,dq as Ms,f as qt,j as Kt,cQ as Bs,h as Mt,dr as Rs,cS as ms,aj as fs,bz as Bt,d8 as Rt,c_ as es,cZ as ts,cx as us,cK as ss,cL as ls,d5 as Ns,ag as Gs,B as Fs,Z as Hs,_ as qs,cT as Ks,cU as Ys,d9 as Js,ak as Ws,dg as Zs,bZ as Qs}from"./index-msvS5Uas.js";import{e as Xs,g as gs,f as el}from"./slaveServers-osXiX8y2.js";import{g as ys}from"./permission-assignment-C3k5CQzu.js";/* empty css                   *//* empty css                  *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                          *//* empty css                         */import re from"./websocket-BU-XNXt0.js";/* empty css                   *//* empty css                     *//* empty css                        *//* empty css                 *//* empty css                *//* empty css                *//* empty css                       *//* empty css                 *//* empty css                       *//* empty css                        */import{u as tl}from"./useOrgUsersAdapter-BIp1upO0.js";import"./index-CBxMtV9u.js";import"./users-CjWk2F0r.js";import"./useOrganizationTree-aYaBbMvP.js";function sl(){const pe=c(!1),P=c([]),G=c([]),_e=c([]),Z=c({slaveServers:{total:0,online:0,offline:0},devices:{total:0,available:0,connected:0,virtual:0},groups:{total:0,withDevices:0},permissions:{totalAssignments:0,activeUsers:0}});let H=null;const te=dt(),ae=ce(()=>te.canAccessDeviceManagementTab),E=ce(()=>te.canAccessSlaveServerTab),j=ce(()=>te.canAccessDeviceGroupTab),Q=ce(()=>te.canAccessPermissionAssignmentTab),le=ce(()=>{const T=[];return ae.value&&T.push({name:"devices",label:"USB设备管理",icon:"Monitor"}),E.value&&T.push({name:"slaves",label:"分布式节点管理",icon:"Monitor"}),j.value&&T.push({name:"groups",label:"资源调度分组",icon:"Collection"}),Q.value&&T.push({name:"permissions",label:"授权范围管理",icon:"Key"}),T}),F=async()=>{try{const T=await gs();T.success&&(P.value=T.data||[],R())}catch(T){console.error("获取从服务器列表失败:",T)}},ne=async()=>{try{const T=await ys();T.success&&(G.value=T.data||[],w())}catch(T){console.error("获取设备分组列表失败:",T)}},oe=async()=>{try{const T=await Xs();if(T.success){const U=T.data;Z.value.slaveServers={total:U.total_servers||0,online:U.online_servers||0,offline:U.offline_servers||0},Z.value.devices={total:U.total_devices||0,available:U.available_devices||0,connected:U.connected_devices||0,virtual:U.virtual_devices||0},console.log("统计数据已更新:",{servers:Z.value.slaveServers,devices:Z.value.devices})}}catch(T){console.error("获取统计数据失败:",T),R()}},R=()=>{const T=P.value.length,U=P.value.filter(D=>D.status==="online"||D.is_online).length;Z.value.slaveServers={total:T,online:U,offline:T-U}},w=()=>{const T=G.value.length,U=G.value.filter(D=>D.device_count>0).length;Z.value.groups={total:T,withDevices:U}},r=async()=>{pe.value=!0;try{await Promise.all([F(),ne(),oe()]),f.success("数据刷新成功")}catch(T){console.error("刷新数据失败:",T),f.error("数据刷新失败")}finally{pe.value=!1}},V=()=>{const T=()=>{const U=Z.value.slaveServers.total;return U>1e3?1e4:U>100?15e3:3e4};oe(),H=setInterval(()=>{oe()},T())},C=()=>{H&&(clearInterval(H),H=null)};return ut(()=>{r(),V()}),Dt(()=>{C()}),{loading:pe,slaveServers:P,deviceGroups:G,devices:_e,statistics:Z,hasDeviceManagePermission:ae,hasSlaveServerPermission:E,hasDeviceGroupPermission:j,hasPermissionAssignPermission:Q,visibleTabs:le,refreshAllData:r,fetchSlaveServers:F,fetchDeviceGroups:ne,fetchStatistics:oe,startAutoRefresh:V,stopAutoRefresh:C}}const ll={class:"occupied-dialog-content"},ol={class:"device-info"},nl={class:"device-header"},al={class:"device-details"},il={class:"device-name"},rl={class:"device-id"},ul={class:"occupier-info"},dl={class:"section-title"},cl={class:"user-card"},pl={class:"user-avatar"},_l={class:"user-details"},vl={class:"user-name"},ml={class:"user-contact"},fl={key:0,class:"user-email"},gl={class:"contact-actions"},yl={class:"time-info"},hl={class:"section-title"},bl={class:"time-details"},kl={class:"time-item"},wl={class:"time-value"},$l={class:"time-item"},Cl={class:"time-value"},Tl={class:"time-item"},Sl={class:"time-value"},xl={key:0,class:"occupation-note"},Vl={class:"section-title"},Dl={class:"note-content"},Al={class:"action-suggestions"},zl={class:"section-title"},El={class:"suggestions-list"},Ul={class:"suggestion-item"},Pl={class:"suggestion-item"},Ll={class:"suggestion-item"},Il={class:"dialog-footer"},jl={__name:"DeviceOccupiedDialog",props:{modelValue:{type:Boolean,default:!1},deviceId:{type:[String,Number],default:null}},emits:["update:modelValue","release-requested"],setup(pe,{emit:P}){const G=pe,_e=P,Z=dt(),H=c(!1),te=c(!1),ae=c(!1),E=qe({device_id:"",device_name:"",custom_name:"",device_type:"unknown"}),j=qe({user_name:"",user_contact:"",user_email:"",start_time:null,duration:0,estimated_end_time:null,note:""});bt(()=>G.modelValue,w=>{ae.value=w,w&&G.deviceId&&Q()}),bt(ae,w=>{_e("update:modelValue",w)});const Q=async()=>{H.value=!0;try{const w=await fetch(`/api/v1/devices/${G.deviceId}`,{method:"GET",headers:{Authorization:`Bearer ${Z.token}`,"Content-Type":"application/json"}});if(!w.ok)throw new Error(`获取设备信息失败: HTTP ${w.status}`);const r=await w.json();if(Object.assign(E,{device_id:r.device_id,device_name:r.device_name,custom_name:r.custom_name,device_type:r.device_type}),r.status==="occupied"){const V=await fetch(`/api/v1/devices/${G.deviceId}/occupation`,{method:"GET",headers:{Authorization:`Bearer ${Z.token}`,"Content-Type":"application/json"}});if(V.ok){const C=await V.json();Object.assign(j,{user_name:C.user_name,user_contact:C.user_contact,user_email:C.user_email,start_time:C.start_time,duration:C.current_duration,estimated_end_time:C.estimated_end_time,note:C.note})}else Object.assign(j,{user_name:r.current_user_name,user_contact:r.current_user_contact,user_email:"",start_time:r.occupied_start_time,duration:r.occupied_duration,estimated_end_time:r.estimated_end_time,note:r.occupation_note})}else throw new Error("设备未被占用")}catch(w){console.error("获取设备占用信息失败:",w),f.error(`获取设备占用信息失败: ${w.message}`)}finally{H.value=!1}},le=w=>w?new Date(w).toLocaleString():"N/A",F=w=>{if(!w)return"0分钟";const r=Math.floor(w/3600),V=Math.floor(w%3600/60);return r>0?`${r}小时${V}分钟`:`${V}分钟`},ne=async w=>{try{w==="phone"?(await Re.confirm(`确定要拨打电话 ${j.user_contact} 联系 ${j.user_name} 吗？`,"确认拨打电话",{confirmButtonText:"拨打",cancelButtonText:"取消",type:"info"}),f.success("正在为您拨打电话...")):w==="message"&&(await Re.prompt(`请输入要发送给 ${j.user_name} 的消息：`,"发送消息",{confirmButtonText:"发送",cancelButtonText:"取消",inputPlaceholder:"请输入消息内容...",inputType:"textarea"}),f.success("消息已发送"))}catch{}},oe=async()=>{try{await Re.confirm(`确定要向 ${j.user_name} 发送设备释放请求吗？`,"确认发送释放请求",{confirmButtonText:"发送",cancelButtonText:"取消",type:"warning"}),te.value=!0;const w=await fetch(`/api/v1/devices/${G.deviceId}/release-request`,{method:"POST",headers:{Authorization:`Bearer ${Z.token}`,"Content-Type":"application/json"}});if(!w.ok){const V=await w.json();throw new Error(V.detail||`HTTP ${w.status}`)}const r=await w.json();f.success(r.message||"释放请求已发送"),_e("release-requested",G.deviceId)}catch(w){w.message!=="cancel"&&(console.error("发送释放请求失败:",w),f.error(`发送释放请求失败: ${w.message}`))}finally{te.value=!1}},R=()=>{ae.value=!1};return(w,r)=>{const V=ft,C=kt,T=gt,U=At,D=wt;return _(),B(U,{modelValue:ae.value,"onUpdate:modelValue":r[3]||(r[3]=z=>ae.value=z),title:"设备占用信息",width:"500px","before-close":R},{footer:t(()=>[s("div",Il,[e(T,{onClick:R},{default:t(()=>r[17]||(r[17]=[d("关闭",-1)])),_:1,__:[17]}),e(T,{type:"warning",onClick:oe,loading:te.value},{default:t(()=>[e(V,null,{default:t(()=>[e(k(Yt))]),_:1}),r[18]||(r[18]=d(" 请求释放 ",-1))]),_:1,__:[18]},8,["loading"]),e(T,{type:"primary",onClick:r[2]||(r[2]=z=>ne("phone")),disabled:!j.user_contact},{default:t(()=>[e(V,null,{default:t(()=>[e(k(jt))]),_:1}),r[19]||(r[19]=d(" 联系用户 ",-1))]),_:1,__:[19]},8,["disabled"])])]),default:t(()=>[at((_(),L("div",ll,[s("div",ol,[s("div",nl,[e(V,{class:"device-icon"},{default:t(()=>[e(k(_t))]),_:1}),s("div",al,[s("div",il,m(E.custom_name||E.device_name),1),s("div",rl,"设备ID: "+m(E.device_id),1)]),e(C,{type:"warning",size:"large"},{default:t(()=>[e(V,null,{default:t(()=>[e(k(Cs))]),_:1}),r[4]||(r[4]=d(" 被占用 ",-1))]),_:1,__:[4]})])]),s("div",ul,[s("div",dl,[e(V,null,{default:t(()=>[e(k(Ot))]),_:1}),r[5]||(r[5]=s("span",null,"占用用户信息",-1))]),s("div",cl,[s("div",pl,[e(V,null,{default:t(()=>[e(k(Ot))]),_:1})]),s("div",_l,[s("div",vl,m(j.user_name||"N/A"),1),s("div",ml,[e(V,null,{default:t(()=>[e(k(jt))]),_:1}),s("span",null,m(j.user_contact||"N/A"),1)]),j.user_email?(_(),L("div",fl,[e(V,null,{default:t(()=>[e(k(Ts))]),_:1}),s("span",null,m(j.user_email),1)])):J("",!0)]),s("div",gl,[e(T,{type:"primary",size:"small",onClick:r[0]||(r[0]=z=>ne("phone")),disabled:!j.user_contact},{default:t(()=>[e(V,null,{default:t(()=>[e(k(jt))]),_:1}),r[6]||(r[6]=d(" 拨打电话 ",-1))]),_:1,__:[6]},8,["disabled"]),e(T,{type:"success",size:"small",onClick:r[1]||(r[1]=z=>ne("message")),disabled:!j.user_contact},{default:t(()=>[e(V,null,{default:t(()=>[e(k(Ss))]),_:1}),r[7]||(r[7]=d(" 发送消息 ",-1))]),_:1,__:[7]},8,["disabled"])])])]),s("div",yl,[s("div",hl,[e(V,null,{default:t(()=>[e(k(rs))]),_:1}),r[8]||(r[8]=s("span",null,"占用时间信息",-1))]),s("div",bl,[s("div",kl,[r[9]||(r[9]=s("span",{class:"time-label"},"开始时间：",-1)),s("span",wl,m(le(j.start_time)),1)]),s("div",$l,[r[10]||(r[10]=s("span",{class:"time-label"},"持续时间：",-1)),s("span",Cl,m(F(j.duration)),1)]),s("div",Tl,[r[11]||(r[11]=s("span",{class:"time-label"},"预计结束：",-1)),s("span",Sl,m(le(j.estimated_end_time)||"未设置"),1)])])]),j.note?(_(),L("div",xl,[s("div",Vl,[e(V,null,{default:t(()=>[e(k(xs))]),_:1}),r[12]||(r[12]=s("span",null,"占用说明",-1))]),s("div",Dl,m(j.note),1)])):J("",!0),s("div",Al,[s("div",zl,[e(V,null,{default:t(()=>[e(k(Yt))]),_:1}),r[13]||(r[13]=s("span",null,"操作建议",-1))]),s("div",El,[s("div",Ul,[e(V,{class:"suggestion-icon"},{default:t(()=>[e(k(jt))]),_:1}),r[14]||(r[14]=s("span",null,"联系占用用户协商使用时间",-1))]),s("div",Pl,[e(V,{class:"suggestion-icon"},{default:t(()=>[e(k(rs))]),_:1}),r[15]||(r[15]=s("span",null,"等待设备自动释放（如有预计结束时间）",-1))]),s("div",Ll,[e(V,{class:"suggestion-icon"},{default:t(()=>[e(k(Yt))]),_:1}),r[16]||(r[16]=s("span",null,"发送释放请求通知",-1))])])])])),[[D,H.value]])]),_:1},8,["modelValue"])}}},Ol=mt(jl,[["__scopeId","data-v-eabf33b8"]]),Ml={class:"device-management"},Bl={class:"management-header"},Rl={class:"header-right"},Nl={class:"search-filters"},Gl={class:"filter-row"},Fl={class:"filter-group"},Hl={class:"filter-group"},ql={class:"filter-group"},Kl={class:"filter-group"},Yl={class:"filter-group"},Jl={class:"device-type-filter-panel"},Wl={class:"panel-header"},Zl={class:"panel-title"},Ql={class:"panel-actions"},Xl={class:"device-type-checkboxes"},eo={class:"checkbox-grid"},to={class:"checkbox-content"},so={class:"type-icon"},lo={class:"type-label"},oo={class:"type-count"},no={class:"device-list"},ao={class:"card-header"},io={class:"header-actions"},ro={class:"selected-count"},uo=["onClick"],co={class:"name-primary"},po={key:0,class:"name-secondary"},_o={class:"last-connected"},vo={key:0},mo={class:"time"},fo={class:"device-remark"},go={key:1,class:"disabled-text"},yo={class:"pagination-wrapper"},ho={key:0,class:"device-usage-panel"},bo={class:"usage-panel-header"},ko={class:"usage-panel-title"},wo={class:"device-usage-content"},$o={class:"device-basic-info"},Co={class:"info-item"},To={class:"info-value"},So={class:"info-item"},xo={class:"info-value"},Vo={class:"info-item"},Do={class:"usage-details"},Ao={key:0,class:"usage-info"},zo={class:"usage-grid"},Eo={class:"usage-item"},Uo={class:"usage-value"},Po={class:"usage-item"},Lo={class:"usage-value"},Io={class:"usage-item"},jo={class:"usage-item"},Oo={class:"usage-value masked-phone"},Mo={key:0,class:"usage-item"},Bo={class:"usage-value"},Ro={key:1,class:"no-usage"},No={key:2,class:"device-offline"},Go={__name:"DeviceManagement",setup(pe){Nt();const P=dt(),G=c(!1),_e=c(!1),Z=c(""),H=c(""),te=c(""),ae=c(""),E=c("created_at"),j=c(!1),Q=c([]),le=c(1),F=c(20),ne=c(!1),oe=c(null),R=c(!1),w=c(null),r=c([]),V=c([]),C=qe({total_devices:0,online_devices:0,hardware_devices:0,occupied_devices:0}),T=qe({total_devices:0,available_devices:0,online_servers:0,total_servers:0,data_freshness:"unknown",last_update:null}),U=c("hybrid");c(!0);const D=c({}),z=c([]),K=c(null),ve=c(!1);let me=null;const ue=ce(()=>{const a=Object.keys(D.value).filter(n=>D.value[n]);return a.length===0?[]:r.value.filter(n=>{const S=be(n);return a.includes(S)})}),Ae=ce(()=>{let a=ue.value;if(Z.value){const n=Z.value.toLowerCase();a=a.filter(S=>S.device_name&&S.device_name.toLowerCase().includes(n)||S.custom_name&&S.custom_name.toLowerCase().includes(n)||S.device_id&&S.device_id.toLowerCase().includes(n)||S.description&&S.description.toLowerCase().includes(n))}return H.value&&(a=a.filter(n=>n.server_id===H.value)),te.value&&(a=a.filter(n=>n.device_type===te.value)),ae.value&&(a=a.filter(n=>n.status===ae.value)),a.sort((n,S)=>{switch(E.value){case"device_name":return(n.device_name||"").localeCompare(S.device_name||"");case"server_group":return(n.server_name||"").localeCompare(S.server_name||"");case"last_connected":return new Date(S.last_connected||0)-new Date(n.last_connected||0);case"created_at":default:return new Date(S.created_at||0)-new Date(n.created_at||0)}}),a}),X=ce(()=>Ae.value.length),ee=ce(()=>{const a=P.user;if(!a)return!1;const S=["admin","super_admin","global_admin"].includes(a.role),q=a.permission_level<=2;return!S&&!q}),xe=ce(()=>{const a=P.user;if(!a)return!1;const S=["admin","super_admin","global_admin"].includes(a.role),q=a.permission_level<=2;return S||q}),Te=async()=>{G.value=!0;try{await Promise.all([we(),Ke()])}catch(a){console.error("数据刷新失败:",a),f.error("数据刷新失败")}finally{G.value=!1}},we=async()=>{try{const a={page:le.value,page_size:F.value,search:Z.value||void 0,server_id:H.value||void 0,device_type:te.value||void 0,status:ae.value||void 0,sort_by:E.value,sort_order:"desc"};Object.keys(a).forEach(q=>{a[q]===void 0&&delete a[q]});const n=await fetch("/api/v1/devices?"+new URLSearchParams(a),{method:"GET",headers:{Authorization:`Bearer ${P.token}`,"Content-Type":"application/json"}});if(!n.ok)throw new Error(`HTTP ${n.status}: ${n.statusText}`);const S=await n.json();r.value=S.devices||[],N(),w.value=new Date().toLocaleString()}catch(a){console.error("获取设备列表失败:",a),f.error(`获取设备列表失败: ${a.message}`)}},Ke=async()=>{try{const a=await fetch(`/api/v1/devices/stats/summary?mode=${U.value}`,{method:"GET",headers:{Authorization:`Bearer ${P.token}`,"Content-Type":"application/json"}});if(!a.ok)throw new Error(`HTTP ${a.status}: ${a.statusText}`);const n=await a.json();Object.assign(C,{total_devices:n.total_devices||0,online_devices:n.online_devices||0,hardware_devices:n.hardware_devices||0,occupied_devices:n.occupied_devices||0}),U.value!=="database"&&Object.assign(T,{total_devices:n.total_devices||0,available_devices:n.available_devices||0,online_servers:n.online_servers||0,total_servers:n.total_servers||0,data_freshness:n.data_freshness||"unknown",last_update:n.last_update}),w.value=new Date().toLocaleString(),console.log("统计数据获取成功:",n)}catch(a){console.error("获取统计数据失败:",a)}},Ee={ca_lock:{label:"CA锁",color:"danger",icon:"🔐",description:"CA证书锁、银行U盾、数字证书设备",defaultVisible:!0},encryption_key:{label:"加密锁",color:"warning",icon:"🔒",description:"软件加密锁、硬件加密锁设备",defaultVisible:!0},bank_ukey:{label:"银行U盾",color:"primary",icon:"🏦",description:"银行数字证书U盾设备",defaultVisible:!0},financial_lock:{label:"财务锁",color:"warning",icon:"💰",description:"用友、金蝶等财务软件加密锁",defaultVisible:!0},cost_lock:{label:"造价加密锁",color:"primary",icon:"🏗️",description:"广联达、新点等造价软件加密锁",defaultVisible:!0},other_lock:{label:"其他加密锁",color:"danger",icon:"🔒",description:"其他类型的加密锁设备",defaultVisible:!1},storage:{label:"存储设备",color:"info",icon:"💾",description:"U盘、移动硬盘等存储设备",defaultVisible:!1},peripheral_device:{label:"外设",color:"success",icon:"🖨️",description:"打印机、扫描仪等外围设备",defaultVisible:!1},video_device:{label:"视频设备",color:"primary",icon:"📹",description:"摄像头、采集卡等视频设备",defaultVisible:!1},audio_device:{label:"音频设备",color:"warning",icon:"🎵",description:"麦克风、耳机、声卡等音频设备",defaultVisible:!1},bluetooth_device:{label:"蓝牙设备",color:"info",icon:"📶",description:"蓝牙适配器、蓝牙设备",defaultVisible:!1},hub:{label:"Hub设备",color:"",icon:"🔌",description:"USB Hub、集线器等",defaultVisible:!1},input:{label:"输入设备",color:"",icon:"⌨️",description:"键盘、鼠标等输入设备",defaultVisible:!1},communication:{label:"通信设备",color:"",icon:"📡",description:"网络适配器等通信设备",defaultVisible:!1},hardware:{label:"硬件设备",color:"",icon:"🔧",description:"其他硬件设备",defaultVisible:!1},unknown:{label:"未知设备",color:"",icon:"❓",description:"未识别的设备类型",defaultVisible:!1}},be=a=>a.final_device_type?Ne(a.final_device_type,a):Ne(a.device_type||"unknown",a),Ne=(a,n=null)=>{if(n){if(n.identification_source==="usb_ids_enhanced")return"encryption_key";if(n.usb_ids_vendor_name&&n.usb_ids_vendor_name.trim()){const q=n.usb_ids_vendor_name.toLowerCase();if(q.includes("senseshield")||q.includes("rockey")||q.includes("hasp")||q.includes("sentinel")||q.includes("safenet")||q.includes("aladdin"))return"encryption_key";if(n.usb_ids_device_name){const ie=n.usb_ids_device_name.toLowerCase();if(ie.includes("dongle")||ie.includes("key")||ie.includes("lock")||ie.includes("token"))return"encryption_key"}}}return{ca_lock:"ca_lock",encryption_key:"encryption_key",bank_ukey:"bank_ukey",financial_lock:"financial_lock",cost_lock:"cost_lock",other_lock:"other_lock",video_device:"video_device",audio_device:"audio_device",bluetooth_device:"bluetooth_device",peripheral_device:"peripheral_device",storage:"storage",input:"input",communication:"communication",hub:"hub",hardware:"hardware",unknown:"unknown",CA锁:"ca_lock",加密锁:"encryption_key",银行U盾:"bank_ukey",encryption_lock:"encryption_key",encryption:"encryption_key",printer_scanner:"peripheral_device",printer:"peripheral_device",unknown_error:"unknown",virtual:"unknown",system:"unknown"}[a]||"unknown"},Ye=()=>{const a=localStorage.getItem("omnilink-device-type-filter");if(a)try{D.value=JSON.parse(a)}catch(n){console.warn("Failed to parse saved device type filter:",n),Ge()}else Ge();z.value=Object.keys(D.value).filter(n=>D.value[n])},Ue=()=>{localStorage.setItem("omnilink-device-type-filter",JSON.stringify(D.value))},Me=a=>{D.value={},Object.keys(Ee).forEach(n=>{D.value[n]=a.includes(n)}),Ue()},Xe=()=>{Object.keys(Ee).forEach(a=>{D.value[a]=!0}),z.value=Object.keys(Ee),Ue()},Ge=()=>{D.value={},Object.keys(Ee).forEach(a=>{D.value[a]=Ee[a].defaultVisible}),z.value=Object.keys(D.value).filter(a=>D.value[a]),Ue()},Je=a=>{if(!r.value||r.value.length===0)return 0;let n=r.value;if(Z.value){const S=Z.value.toLowerCase();n=n.filter(q=>{var ie,Fe,Ce,je;return((ie=q.device_name)==null?void 0:ie.toLowerCase().includes(S))||((Fe=q.custom_name)==null?void 0:Fe.toLowerCase().includes(S))||((Ce=q.device_id)==null?void 0:Ce.toLowerCase().includes(S))||((je=q.serial_number)==null?void 0:je.toLowerCase().includes(S))})}return H.value&&(n=n.filter(S=>S.slave_server_id===H.value)),ae.value&&(n=n.filter(S=>S.status===ae.value)),n.filter(S=>be(S)===a).length},$=a=>({idle:"success",occupied:"warning",damaged:"danger",offline:"info"})[a]||"info",g=a=>({idle:"空闲",occupied:"被占用",damaged:"硬件损坏",offline:"离线"})[a]||"未知",W=a=>a?new Date(a).toLocaleString():"N/A",I=a=>{a?Q.value=[...Ae.value]:Q.value=[]},ke=a=>{Q.value=a,j.value=a.length===Ae.value.length},fe=async a=>{switch(a){case"batch-edit":case"batch-group":case"batch-delete":if(Q.value.length===0){f.warning("请先选择要操作的设备");return}f.info(`批量操作: ${a}, 选中 ${Q.value.length} 个设备`);break;default:f.warning("未知的批量操作")}},ge=async()=>{try{await Re.confirm("此操作将使用智能分类器重新分析所有设备的类型，支持精准识别CA锁、财务锁、造价加密锁等。是否继续？","智能重新分类设备类型",{confirmButtonText:"开始分类",cancelButtonText:"取消",type:"warning"}),_e.value=!0;try{const a=await fetch("/api/v1/devices/batch-reclassify-types",{method:"POST",headers:{Authorization:`Bearer ${P.token}`,"Content-Type":"application/json"}});if(!a.ok)throw new Error(`HTTP error! status: ${a.status}`);const n=await a.json();f.success(`智能分类完成！总计: ${n.statistics.total}, 成功: ${n.statistics.updated}`),await Te()}finally{_e.value=!1}}catch(a){_e.value=!1,a!=="cancel"&&(console.error("智能重新分类失败:",a),f.error("智能重新分类失败: "+a.message))}},y=a=>{F.value=a,le.value=1},v=a=>{le.value=a},he=a=>{a.status==="occupied"&&(oe.value=a.id,ne.value=!0)},de=async a=>{try{const n=await fetch(`/api/v1/devices/${a}/release-request`,{method:"POST",headers:{Authorization:`Bearer ${P.token}`,"Content-Type":"application/json"}});if(!n.ok){const q=await n.json();throw new Error(q.detail||`HTTP ${n.status}`)}const S=await n.json();f.success(S.message||"设备释放请求已发送"),Te()}catch(n){console.error("发送释放请求失败:",n),f.error(`发送释放请求失败: ${n.message}`)}},Se=a=>{if(!a||typeof a!="string")return"***";if(a.length<=4)return a;const n=a.substring(0,3),S=a.substring(a.length-4);return`${n}***${S}`},Ie=async a=>{if(K.value=a,ve.value=!0,a.status==="occupied")try{const n=await fetch(`/api/v1/devices/${a.id}/occupation`,{method:"GET",headers:{Authorization:`Bearer ${P.token}`,"Content-Type":"application/json"}});if(n.ok){const S=await n.json();K.value={...a,...S}}}catch(n){console.error("获取设备占用信息失败:",n)}f.info(`已选择设备: ${a.custom_name||a.device_name}`)},et=a=>!(!P.user||a.status!=="idle"),ze=async a=>{var n,S,q;try{await Re.confirm(`确定要使用设备 "${a.custom_name||a.device_name}" 吗？`,"确认使用设备",{confirmButtonText:"确定使用",cancelButtonText:"取消",type:"info"});const ie=await fetch(`/api/v1/devices/${a.id}/occupy`,{method:"POST",headers:{Authorization:`Bearer ${P.token}`,"Content-Type":"application/json"},body:JSON.stringify({user_contact:((n=P.user)==null?void 0:n.phone)||((S=P.user)==null?void 0:S.email)||"",note:`用户 ${(q=P.user)==null?void 0:q.username} 开始使用设备`})});if(!ie.ok){const Fe=await ie.json();throw new Error(Fe.detail||`HTTP ${ie.status}`)}f.success(`设备 "${a.custom_name||a.device_name}" 使用成功`),await Te(),await Ie(a)}catch(ie){ie.message!=="cancel"&&(console.error("使用设备失败:",ie),f.error(`使用设备失败: ${ie.message}`))}},Ve=async a=>{try{let n="未知用户",S="";try{const ie=await fetch(`/api/v1/devices/${a.id}/occupation`,{method:"GET",headers:{Authorization:`Bearer ${P.token}`,"Content-Type":"application/json"}});if(ie.ok){const Fe=await ie.json();n=Fe.user_name||"未知用户",S=Fe.user_contact||""}}catch(ie){console.warn("无法获取占用用户信息:",ie)}await Re.confirm(`确定要强制断开设备 "${a.custom_name||a.device_name}" 吗？

当前使用用户：${n}
联系方式：${S}

强制断开后将向用户发送通知。`,"确认强制断开",{confirmButtonText:"确定断开",cancelButtonText:"取消",type:"warning"});const q=await fetch(`/api/v1/devices/${a.id}/release`,{method:"POST",headers:{Authorization:`Bearer ${P.token}`,"Content-Type":"application/json"}});if(!q.ok){const ie=await q.json();throw new Error(ie.detail||`HTTP ${q.status}`)}f.success(`设备 "${a.custom_name||a.device_name}" 已强制断开`),await Te()}catch(n){n.message!=="cancel"&&(console.error("强制断开设备失败:",n),f.error(`强制断开设备失败: ${n.message}`))}},Pe=()=>{re.setConnectionChangeCallback(n=>{R.value=n,console.log(n?"WebSocket连接已建立":"WebSocket连接已断开")}),re.subscribe("device_updates"),re.addEventListener("device_status_update",A),re.addEventListener("device_connected",We),re.addEventListener("device_disconnected",tt),re.addEventListener("device_occupied",Ze),re.addEventListener("device_released",it),re.addEventListener("device_added",De),re.addEventListener("device_updated",p),re.addEventListener("device_deleted",u),re.addEventListener("device_release_requested",x);const a=P.token;a&&re.connect(a)},A=a=>{console.log("设备状态更新:",a);const n=r.value.findIndex(S=>S.id===a.device_id);n!==-1&&(r.value[n].status=a.new_status,w.value=new Date().toLocaleString(),N(),f.info(`设备状态更新: ${r.value[n].device_name} -> ${g(a.new_status)}`))},We=a=>{console.log("设备连接:",a);const n=r.value.findIndex(S=>S.id===a.device_id);n!==-1&&(r.value[n].status="occupied",r.value[n].last_connected=a.timestamp,r.value[n].last_connected_user=a.user_name||"Unknown",N(),f.success(`设备已连接: ${r.value[n].device_name}`))},tt=a=>{console.log("设备断开:",a);const n=r.value.findIndex(S=>S.id===a.device_id);n!==-1&&(r.value[n].status="idle",N(),f.info(`设备已断开: ${r.value[n].device_name}`))},Ze=a=>{console.log("设备被占用:",a);const n=r.value.findIndex(S=>S.id===a.device_id);n!==-1&&(r.value[n].status="occupied",r.value[n].last_connected_user=a.user_name,r.value[n].last_connected=a.timestamp,N(),f.warning(`设备被占用: ${r.value[n].device_name} (${a.user_name})`))},it=a=>{console.log("设备已释放:",a);const n=r.value.findIndex(S=>S.id===a.device_id);n!==-1&&(r.value[n].status="idle",N(),f.success(`设备已释放: ${r.value[n].device_name}`))},De=a=>{console.log("设备已添加:",a),Te(),f.success(`新设备已添加: ${a.device_name}`)},p=a=>{console.log("设备已更新:",a),Te(),f.info("设备信息已更新")},u=a=>{console.log("设备已删除:",a);const n=r.value.findIndex(S=>S.id===a.device_id);n!==-1&&(r.value.splice(n,1),N(),f.info(`设备已删除: ${a.device_name}`))},x=a=>{var n;console.log("收到设备释放请求:",a),a.requester_id!==((n=P.user)==null?void 0:n.id)&&f({type:"warning",message:`${a.requester_name} 请求您释放设备: ${a.device_name}`,duration:1e4,showClose:!0})},N=()=>{C.total_devices=r.value.length,C.online_devices=r.value.filter(a=>a.status!=="offline").length,C.hardware_devices=r.value.filter(a=>a.device_type!=="unknown").length,C.occupied_devices=r.value.filter(a=>a.status==="occupied").length},Y=()=>{re.removeEventListener("device_status_update",A),re.removeEventListener("device_connected",We),re.removeEventListener("device_disconnected",tt),re.removeEventListener("device_occupied",Ze),re.removeEventListener("device_released",it),re.removeEventListener("device_added",De),re.removeEventListener("device_updated",p),re.removeEventListener("device_deleted",u),re.removeEventListener("device_release_requested",x),re.unsubscribe("device_updates")};return ut(()=>{Ye(),Te(),Pe(),me=setInterval(Te,6e4)}),Dt(()=>{me&&clearInterval(me),Y()}),(a,n)=>{const S=ft,q=gt,ie=zs,Fe=As,Ce=Es,je=zt,$e=Ut,Qe=Et,lt=Zt,Be=kt,rt=Gt,ot=_s,yt=ps,He=Ht,$t=Ft,Ct=Qt,l=vs,o=Ls,h=wt;return _(),L(Oe,null,[s("div",Ml,[s("div",Bl,[n[17]||(n[17]=s("div",{class:"header-left"},[s("div",{class:"page-title"},[s("h2",null,"USB设备管理")])],-1)),s("div",Rl,[e(q,{onClick:Te,loading:G.value,type:"primary"},{default:t(()=>[e(S,null,{default:t(()=>[e(k(vt))]),_:1}),n[11]||(n[11]=d(" 刷新数据 ",-1))]),_:1,__:[11]},8,["loading"]),e(q,{onClick:ge,type:"warning",loading:_e.value},{default:t(()=>[e(S,null,{default:t(()=>[e(k(Vs))]),_:1}),n[12]||(n[12]=d(" 智能重新分类 ",-1))]),_:1,__:[12]},8,["loading"]),e(Ce,{onCommand:fe},{dropdown:t(()=>[e(Fe,null,{default:t(()=>[e(ie,{command:"batch-edit"},{default:t(()=>n[14]||(n[14]=[d("批量编辑",-1)])),_:1,__:[14]}),e(ie,{command:"batch-group"},{default:t(()=>n[15]||(n[15]=[d("批量分组",-1)])),_:1,__:[15]}),e(ie,{divided:"",command:"batch-delete"},{default:t(()=>n[16]||(n[16]=[d("批量删除",-1)])),_:1,__:[16]})]),_:1})]),default:t(()=>[e(q,{type:"success"},{default:t(()=>[n[13]||(n[13]=d(" 批量操作 ",-1)),e(S,{class:"el-icon--right"},{default:t(()=>[e(k(Ds))]),_:1})]),_:1,__:[13]})]),_:1})])]),s("div",Nl,[e(lt,null,{default:t(()=>[s("div",Gl,[s("div",Fl,[e(je,{modelValue:Z.value,"onUpdate:modelValue":n[0]||(n[0]=i=>Z.value=i),placeholder:"搜索设备名称、ID、描述...",style:{width:"300px"},clearable:""},{prefix:t(()=>[e(S,null,{default:t(()=>[e(k(pt))]),_:1})]),_:1},8,["modelValue"])]),s("div",Hl,[e(Qe,{modelValue:H.value,"onUpdate:modelValue":n[1]||(n[1]=i=>H.value=i),placeholder:"筛选服务器",style:{width:"200px"},clearable:""},{default:t(()=>[e($e,{label:"全部服务器",value:""}),(_(!0),L(Oe,null,st(V.value,i=>(_(),B($e,{key:i.id,label:i.name,value:i.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),s("div",ql,[e(Qe,{modelValue:te.value,"onUpdate:modelValue":n[2]||(n[2]=i=>te.value=i),placeholder:"筛选类型",style:{width:"150px"},clearable:""},{default:t(()=>[e($e,{label:"全部类型",value:""}),e($e,{label:"加密锁",value:"encryption_key"}),e($e,{label:"存储设备",value:"storage"}),e($e,{label:"输入设备",value:"input"}),e($e,{label:"通信设备",value:"communication"}),e($e,{label:"硬件设备",value:"hardware"}),e($e,{label:"未知设备",value:"unknown"})]),_:1},8,["modelValue"])]),s("div",Kl,[e(Qe,{modelValue:ae.value,"onUpdate:modelValue":n[3]||(n[3]=i=>ae.value=i),placeholder:"筛选状态",style:{width:"120px"},clearable:""},{default:t(()=>[e($e,{label:"全部状态",value:""}),e($e,{label:"空闲",value:"idle"}),e($e,{label:"被占用",value:"occupied"}),e($e,{label:"硬件损坏",value:"damaged"}),e($e,{label:"离线",value:"offline"})]),_:1},8,["modelValue"])]),s("div",Yl,[e(Qe,{modelValue:E.value,"onUpdate:modelValue":n[4]||(n[4]=i=>E.value=i),placeholder:"排序方式",style:{width:"150px"}},{default:t(()=>[e($e,{label:"注册时间",value:"created_at"}),e($e,{label:"设备名称",value:"device_name"}),e($e,{label:"服务器分组",value:"server_group"}),e($e,{label:"最后连接",value:"last_connected"})]),_:1},8,["modelValue"])])])]),_:1})]),s("div",Jl,[e(lt,null,{header:t(()=>[s("div",Wl,[s("div",Zl,[e(S,null,{default:t(()=>[e(k(Ps))]),_:1}),n[18]||(n[18]=s("span",null,"设备类型过滤",-1)),e(Be,{size:"small",type:"info"},{default:t(()=>[d(" 显示"+m(Object.keys(D.value).filter(i=>D.value[i]).length)+"种类型，共"+m(ue.value.length)+"个设备 ",1)]),_:1})]),s("div",Ql,[e(q,{size:"small",onClick:Xe},{default:t(()=>n[19]||(n[19]=[d("全选",-1)])),_:1,__:[19]}),e(q,{size:"small",onClick:Ge},{default:t(()=>n[20]||(n[20]=[d("重置为默认",-1)])),_:1,__:[20]})])])]),default:t(()=>[s("div",Xl,[e(yt,{modelValue:z.value,"onUpdate:modelValue":n[5]||(n[5]=i=>z.value=i),onChange:Me},{default:t(()=>[s("div",eo,[(_(),L(Oe,null,st(Ee,(i,O)=>e(ot,{key:O,label:O,class:"device-type-checkbox"},{default:t(()=>[s("div",to,[s("span",so,m(i.icon),1),s("span",lo,[d(m(i.label.replace(/^[🔐💾⌨️📡🖨️🔌🔧❓]\s/,""))+" ",1),s("span",oo,"（"+m(Je(O))+"）",1)]),e(rt,{content:i.description,placement:"top"},{default:t(()=>[e(S,{class:"info-icon"},{default:t(()=>[e(k(Us))]),_:1})]),_:2},1032,["content"])])]),_:2},1032,["label"])),64))])]),_:1},8,["modelValue"])])]),_:1})]),s("div",no,[e(lt,null,{header:t(()=>[s("div",ao,[n[22]||(n[22]=s("span",null,"设备列表",-1)),s("div",io,[e(ot,{modelValue:j.value,"onUpdate:modelValue":n[6]||(n[6]=i=>j.value=i),onChange:I},{default:t(()=>n[21]||(n[21]=[d("全选",-1)])),_:1,__:[21]},8,["modelValue"]),s("span",ro,"已选择 "+m(Q.value.length)+" 个设备",1)])])]),default:t(()=>[at((_(),B($t,{data:Ae.value,onSelectionChange:ke,stripe:"",style:{width:"100%"}},{default:t(()=>[e(He,{type:"selection",width:"55"}),e(He,{prop:"device_id",label:"设备ID",width:"120"}),e(He,{label:"设备名称","min-width":"200"},{default:t(({row:i})=>[s("div",{class:"device-name",onClick:O=>Ie(i),style:{cursor:"pointer"}},[s("div",co,m(i.custom_name||i.device_name),1),i.custom_name?(_(),L("div",po,m(i.device_name),1)):J("",!0)],8,uo)]),_:1}),e(He,{label:"状态",width:"100"},{default:t(({row:i})=>[e(Be,{type:$(i.status),size:"small",class:Vt({"clickable-status":i.status==="occupied"}),onClick:O=>i.status==="occupied"?he(i):null},{default:t(()=>[d(m(g(i.status)),1)]),_:2},1032,["type","class","onClick"])]),_:1}),e(He,{label:"位置",width:"120"},{default:t(({row:i})=>[s("code",null,m(i.physical_port||"N/A"),1)]),_:1}),e(He,{label:"最后连接",width:"150"},{default:t(({row:i})=>[s("div",_o,[i.last_connected_user?(_(),L("div",vo,m(i.last_connected_user),1)):J("",!0),s("div",mo,m(W(i.last_connected)),1)])]),_:1}),e(He,{label:"备注信息","min-width":"200"},{default:t(({row:i})=>[s("div",fo,m(i.remark||"无备注"),1)]),_:1}),e(He,{label:"操作",width:"150",fixed:"right"},{default:t(({row:i})=>[ee.value?(_(),L(Oe,{key:0},[et(i)?(_(),B(q,{key:0,size:"small",type:"primary",onClick:O=>ze(i)},{default:t(()=>n[23]||(n[23]=[d(" 使用 ",-1)])),_:2,__:[23]},1032,["onClick"])):(_(),L("span",go,"不可用"))],64)):xe.value?(_(),L(Oe,{key:1},[i.status!=="occupied"?(_(),B(q,{key:0,size:"small",type:"primary",onClick:O=>ze(i)},{default:t(()=>n[24]||(n[24]=[d(" 使用 ",-1)])),_:2,__:[24]},1032,["onClick"])):(_(),B(q,{key:1,size:"small",type:"danger",onClick:O=>Ve(i)},{default:t(()=>n[25]||(n[25]=[d(" 强制断开 ",-1)])),_:2,__:[25]},1032,["onClick"]))],64)):J("",!0)]),_:1})]),_:1},8,["data"])),[[h,G.value]]),s("div",yo,[e(Ct,{"current-page":le.value,"onUpdate:currentPage":n[7]||(n[7]=i=>le.value=i),"page-size":F.value,"onUpdate:pageSize":n[8]||(n[8]=i=>F.value=i),"page-sizes":[20,50,100,200],total:X.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:y,onCurrentChange:v},null,8,["current-page","page-size","total"])])]),_:1})])]),ve.value&&K.value?(_(),L("div",ho,[e(lt,null,{header:t(()=>[s("div",bo,[s("div",ko,[e(S,null,{default:t(()=>[e(k(_t))]),_:1}),s("span",null,m(K.value.custom_name||K.value.device_name)+" - 使用情况",1)]),e(q,{size:"small",onClick:n[9]||(n[9]=i=>ve.value=!1)},{default:t(()=>[e(S,null,{default:t(()=>[e(k(Is))]),_:1}),n[26]||(n[26]=d(" 关闭 ",-1))]),_:1,__:[26]})])]),default:t(()=>[s("div",wo,[s("div",$o,[s("div",Co,[n[27]||(n[27]=s("span",{class:"info-label"},"设备ID:",-1)),s("span",To,m(K.value.device_id),1)]),s("div",So,[n[28]||(n[28]=s("span",{class:"info-label"},"位置:",-1)),s("span",xo,m(K.value.physical_port||"N/A"),1)]),s("div",Vo,[n[29]||(n[29]=s("span",{class:"info-label"},"状态:",-1)),e(Be,{type:$(K.value.status),size:"small"},{default:t(()=>[d(m(g(K.value.status)),1)]),_:1},8,["type"])])]),s("div",Do,[K.value.status==="occupied"?(_(),L("div",Ao,[n[36]||(n[36]=s("h4",null,"当前使用信息",-1)),s("div",zo,[s("div",Eo,[n[30]||(n[30]=s("span",{class:"usage-label"},"使用用户:",-1)),s("span",Uo,m(K.value.user_name||K.value.current_user_name||"未知"),1)]),s("div",Po,[n[31]||(n[31]=s("span",{class:"usage-label"},"开始时间:",-1)),s("span",Lo,m(W(K.value.start_time||K.value.occupied_start_time)),1)]),s("div",Io,[n[33]||(n[33]=s("span",{class:"usage-label"},"使用状况:",-1)),e(Be,{type:"warning",size:"small"},{default:t(()=>n[32]||(n[32]=[d("使用中",-1)])),_:1,__:[32]})]),s("div",jo,[n[34]||(n[34]=s("span",{class:"usage-label"},"联系电话:",-1)),s("span",Oo,m(Se(K.value.user_contact||K.value.current_user_contact)),1)]),K.value.note?(_(),L("div",Mo,[n[35]||(n[35]=s("span",{class:"usage-label"},"使用说明:",-1)),s("span",Bo,m(K.value.note),1)])):J("",!0)])])):K.value.status==="idle"?(_(),L("div",Ro,[e(l,{description:"设备当前空闲，暂无使用记录","image-size":100})])):(_(),L("div",No,[e(o,{icon:"warning",title:"设备离线","sub-title":"设备当前处于离线状态，无法查看使用情况"})]))])])]),_:1})])):J("",!0),e(Ol,{modelValue:ne.value,"onUpdate:modelValue":n[10]||(n[10]=i=>ne.value=i),"device-id":oe.value,onReleaseRequested:de},null,8,["modelValue","device-id"])],64)}}},Fo=mt(Go,[["__scopeId","data-v-46de51a6"]]),Ho={class:"slave-server-management"},qo={class:"toolbar"},Ko={class:"toolbar-left"},Yo={class:"toolbar-right"},Jo={class:"server-name"},Wo={class:"server-name-text"},Zo={class:"server-name-display"},Qo={class:"status-tags"},Xo={class:"ip-address"},en={class:"device-count"},tn={key:0,class:"heartbeat-time"},sn={key:1,class:"text-muted"},ln={key:0,class:"config-check-time"},on={key:1,class:"text-muted"},nn={class:"pagination-container"},an={__name:"SlaveServerManagement",emits:["stats-update"],setup(pe,{expose:P,emit:G}){const _e=G,Z=Nt(),H=dt(),te=c(!1),ae=c(!1),E=c(!1),j=c(!1),Q=c(!1),le=c([]),F=c([]),ne=c(""),oe=c(""),R=c(1),w=c(20),r=c(0),V=c(),C=qe({server_name:"",server_ip:"",server_port:8889,vh_port:7575,location:"",description:""}),T={server_name:[{required:!0,message:"请输入服务器名称",trigger:"blur"}],server_ip:[{required:!0,message:"请输入IP地址",trigger:"blur"},{pattern:/^(\d{1,3}\.){3}\d{1,3}$/,message:"请输入正确的IP地址格式",trigger:"blur"}],server_port:[{required:!0,message:"请输入服务端口",trigger:"blur"}],vh_port:[{required:!0,message:"请输入VH端口",trigger:"blur"}]},U=ce(()=>!0),D=ce(()=>!0),z=ce(()=>!0),K=ce(()=>!0),ve=ce(()=>{let $=le.value;if(ne.value&&($=$.filter(W=>W.status===ne.value)),oe.value){const W=oe.value.toLowerCase();$=$.filter(I=>I.name&&I.name.toLowerCase().includes(W)||I.ip_address&&I.ip_address.toLowerCase().includes(W))}const g=$.sort((W,I)=>W.status==="online"&&I.status!=="online"?-1:W.status!=="online"&&I.status==="online"?1:W.id-I.id);return r.value=g.length,g}),me=ce(()=>{const $=(R.value-1)*w.value,g=$+w.value;return ve.value.slice($,g)}),ue=$=>$==="online"?"success":"danger",Ae=$=>$?new Date($).toLocaleString():"",X=async()=>{te.value=!0;try{const $=await gs();le.value=$.data||[],r.value=le.value.length,console.log("从服务器列表数据:",$.data),$.data&&$.data.length>0&&(console.log("第一个服务器的数据:",$.data[0]),console.log("第一个服务器的last_seen字段:",$.data[0].last_seen)),ee()}catch($){console.error("加载从服务器数据失败:",$),f.error("加载从服务器数据失败")}finally{te.value=!1}},ee=()=>{const $={total:le.value.length,online:le.value.filter(g=>g.status==="online").length,offline:le.value.filter(g=>g.status==="offline").length};_e("stats-update",$)},xe=()=>{},Te=$=>{w.value=$,X()},we=$=>{R.value=$,X()},Ke=$=>{Z.push(`/device-center/slave-server/${$.id}`)},Ee=async($,g)=>{try{await Re.confirm(`确定要${g==="restart"?"重启":"控制"}服务器 "${$.name}" 吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),f.success(`服务器 "${$.name}" ${g==="restart"?"重启":"控制"}成功`)}catch{}},be=async()=>{try{await V.value.validate(),ae.value=!0,await el(C),f.success("从服务器添加成功"),Q.value=!1,Ne(),X(),X()}catch($){console.error("添加从服务器失败:",$),f.error("添加从服务器失败")}finally{ae.value=!1}},Ne=()=>{var $;Object.assign(C,{server_name:"",server_ip:"",server_port:8889,vh_port:7575,location:"",description:""}),($=V.value)==null||$.clearValidate()},Ye=$=>{F.value=$},Ue=async()=>{if(F.value.length===0){f.warning("请先选择要同步的从服务器");return}try{await Re.confirm(`确定要强制同步选中的 ${F.value.length} 个从服务器的设备数据吗？

此操作将：
1. 清空选中从服务器的现有设备数据
2. 重新从从服务器获取最新设备信息
3. 应用USB.IDS增强识别`,"确认强制同步",{confirmButtonText:"确定同步",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!0}),E.value=!0;const $=F.value.map(I=>I.id),g=await fetch("/api/v1/slave/force-sync",{method:"POST",headers:{Authorization:`Bearer ${H.token}`,"Content-Type":"application/json"},body:JSON.stringify({slave_ids:$})});if(!g.ok)throw new Error(`HTTP ${g.status}: ${g.statusText}`);const W=await g.json();if(W.success){f.success(`强制同步完成！${W.message}`);const I=W.results.filter(fe=>fe.success).length,ke=W.results.filter(fe=>!fe.success).length;if(ke>0){const fe=W.results.filter(ge=>!ge.success).map(ge=>`服务器${ge.slave_id}: ${ge.message}`).join(`
`);f.warning(`同步完成，但有 ${ke} 个服务器同步失败：
${fe}`)}await X(),F.value=[]}else throw new Error(W.message||"强制同步失败")}catch($){console.error("强制同步失败:",$),f.error(`强制同步失败: ${$.message}`)}finally{E.value=!1}},Me=async()=>{if(F.value.length===0){f.warning("请先选择要删除的从服务器");return}try{const $=F.value.map(ke=>ke.name).join("、"),g=`
      <div style="text-align: left;">
        <p><strong>确定要删除以下 ${F.value.length} 个从服务器吗？</strong></p>
        <p style="color: #666; font-size: 14px; margin: 10px 0;">${$}</p>

        <p><strong>此操作将执行深度清理：</strong></p>
        <ul style="margin: 10px 0; padding-left: 20px; color: #666;">
          <li>🗑️ 删除从服务器记录</li>
          <li>🔌 删除所有关联的USB设备记录</li>
          <li>👥 从设备分组中移除这些设备</li>
          <li>👤 清除设备的直接用户分配关系</li>
          <li>🔄 更新设备占用记录状态</li>
          <li>🧹 清理空的设备分组</li>
          <li>📋 保留审计日志（合规要求）</li>
        </ul>

        <p style="color: #e74c3c; font-weight: bold;">⚠️ 此操作不可撤销！</p>
      </div>
    `;await Re.confirm(g,"确认深度删除",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"error",dangerouslyUseHTMLString:!0,customClass:"bulk-delete-confirm"}),j.value=!0;const W=F.value.map(ke=>ke.id),I=await Ge(W);if(I.batch_results.successful_deletions>0){const ke=`✅ 成功删除 ${I.batch_results.successful_deletions} 个从服务器`,fe=`📊 清理统计：设备 ${I.batch_results.overall_stats.total_devices_deleted} 个，分组关联 ${I.batch_results.overall_stats.total_group_assignments_removed} 个，空分组清理 ${I.batch_results.overall_stats.total_empty_groups_cleaned} 个`;f.success(`${ke}
${fe}`)}if(I.batch_results.failed_deletions>0){const ke=I.batch_results.deletion_details.filter(fe=>fe.status==="failed").map(fe=>`服务器 ${fe.server_id}: ${fe.error}`).join(`
`);f.error(`❌ ${I.batch_results.failed_deletions} 个服务器删除失败：
${ke}`)}await X(),F.value=[]}catch($){$!=="cancel"&&(console.error("批量删除失败:",$),f.error(`批量删除失败: ${$.message}`))}finally{j.value=!1}},Xe=async $=>{try{const g=await fetch(`/api/v1/slave/${$}`,{method:"DELETE",headers:{Authorization:`Bearer ${H.token}`,"Content-Type":"application/json"}});if(!g.ok){const I=await g.json().catch(()=>({}));throw new Error(I.detail||`HTTP ${g.status}: ${g.statusText}`)}return await g.json()}catch(g){throw console.error(`删除从服务器 ${$} 失败:`,g),g}},Ge=async $=>{try{const g=await fetch("/api/v1/slave/batch-delete",{method:"POST",headers:{Authorization:`Bearer ${H.token}`,"Content-Type":"application/json"},body:JSON.stringify($)});if(!g.ok){const I=await g.json().catch(()=>({}));throw new Error(I.detail||`HTTP ${g.status}: ${g.statusText}`)}return await g.json()}catch(g){throw console.error("批量删除从服务器失败:",g),g}},Je=async $=>{try{const g=`
      <div style="text-align: left;">
        <p><strong>确定要删除从服务器 "${$.name}" 吗？</strong></p>

        <p><strong>此操作将执行深度清理：</strong></p>
        <ul style="margin: 10px 0; padding-left: 20px; color: #666;">
          <li>🗑️ 删除从服务器记录</li>
          <li>🔌 删除所有关联的USB设备记录</li>
          <li>👥 从设备分组中移除这些设备</li>
          <li>👤 清除设备的直接用户分配关系</li>
          <li>🔄 更新设备占用记录状态</li>
          <li>🧹 清理空的设备分组</li>
          <li>📋 保留审计日志（合规要求）</li>
        </ul>

        <p style="color: #e74c3c; font-weight: bold;">⚠️ 此操作不可撤销！</p>
      </div>
    `;await Re.confirm(g,"确认深度删除",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"error",dangerouslyUseHTMLString:!0});const W=await Xe($.id);if(W.deletion_stats){const I=W.deletion_stats,ke=`✅ 从服务器 "${$.name}" 删除成功`;let fe=`📊 清理统计：设备 ${I.devices_deleted} 个，分组关联 ${I.device_group_assignments_removed} 个`;if(I.empty_groups_cleaned>0&&(fe+=`，清理空分组 ${I.empty_groups_cleaned} 个`),I.affected_groups&&I.affected_groups.length>0){const ge=I.affected_groups.map(y=>y.name).join("、");fe+=`
🔗 受影响的设备分组：${ge}`}if(I.affected_users&&I.affected_users.length>0){const ge=I.affected_users.map(y=>y.username).join("、");fe+=`
👤 受影响的用户：${ge}`}f.success(`${ke}
${fe}`)}else f.success(`从服务器 "${$.name}" 删除成功`);await X()}catch(g){g!=="cancel"&&(console.error("删除从服务器失败:",g),f.error(`删除失败: ${g.message}`))}};return P({refreshData:X}),ut(()=>{X(),console.log("用户权限调试信息:",{user:H.user,canManageSlaves:U.value,canDeleteSlaves:D.value,canCreateSlaves:z.value,canControlSlaves:K.value})}),($,g)=>{const W=ft,I=gt,ke=Ut,fe=Et,ge=zt,y=Ht,v=Gt,he=kt,de=Ft,Se=Qt,Ie=Kt,et=Bs,ze=qt,Ve=At,Pe=wt;return _(),L("div",Ho,[s("div",qo,[s("div",Ko,[e(I,{type:"primary",onClick:X,loading:te.value},{default:t(()=>[e(W,null,{default:t(()=>[e(k(vt))]),_:1}),g[13]||(g[13]=d(" 刷新列表 ",-1))]),_:1,__:[13]},8,["loading"]),z.value?(_(),B(I,{key:0,type:"success",onClick:g[0]||(g[0]=A=>Q.value=!0)},{default:t(()=>[e(W,null,{default:t(()=>[e(k(Xt))]),_:1}),g[14]||(g[14]=d(" 添加分布式节点 ",-1))]),_:1,__:[14]})):J("",!0),U.value?(_(),B(I,{key:1,type:"warning",onClick:Ue,loading:E.value,disabled:F.value.length===0},{default:t(()=>[e(W,null,{default:t(()=>[e(k(vt))]),_:1}),d(" 强制同步数据 ("+m(F.value.length)+") ",1)]),_:1},8,["loading","disabled"])):J("",!0),D.value?(_(),B(I,{key:2,type:"danger",onClick:Me,loading:j.value,disabled:F.value.length===0},{default:t(()=>[e(W,null,{default:t(()=>[e(k(js))]),_:1}),d(" 批量删除节点 ("+m(F.value.length)+") ",1)]),_:1},8,["loading","disabled"])):J("",!0)]),s("div",Yo,[e(fe,{modelValue:ne.value,"onUpdate:modelValue":g[1]||(g[1]=A=>ne.value=A),placeholder:"筛选状态",style:{width:"120px","margin-right":"8px"},clearable:"",onChange:xe},{default:t(()=>[e(ke,{label:"全部",value:""}),e(ke,{label:"在线",value:"online"}),e(ke,{label:"离线",value:"offline"})]),_:1},8,["modelValue"]),e(ge,{modelValue:oe.value,"onUpdate:modelValue":g[2]||(g[2]=A=>oe.value=A),placeholder:"搜索服务器名称...",style:{width:"200px"},clearable:"",onInput:xe},{prefix:t(()=>[e(W,null,{default:t(()=>[e(k(pt))]),_:1})]),_:1},8,["modelValue"])])]),at((_(),B(de,{data:me.value,stripe:"",style:{width:"100%"},onSelectionChange:Ye},{default:t(()=>[e(y,{type:"selection",width:"50"}),e(y,{prop:"id",label:"ID",width:"50"}),e(y,{label:"分布式节点名称","min-width":"220",sortable:""},{default:t(({row:A})=>[s("div",Jo,[e(W,{class:"server-icon"},{default:t(()=>[e(k(_t))]),_:1}),s("div",Wo,[e(v,{content:A.name,placement:"top",disabled:A.name.length<=25},{default:t(()=>[s("span",Zo,m(A.name),1)]),_:2},1032,["content","disabled"]),s("div",Qo,[A.is_online?(_(),B(he,{key:0,type:"success",size:"small"},{default:t(()=>g[15]||(g[15]=[d("在线",-1)])),_:1,__:[15]})):(_(),B(he,{key:1,type:"danger",size:"small"},{default:t(()=>g[16]||(g[16]=[d("离线",-1)])),_:1,__:[16]}))])])])]),_:1}),e(y,{label:"IP地址",width:"120"},{default:t(({row:A})=>[s("span",Xo,m(A.ip_address)+":"+m(A.port),1)]),_:1}),e(y,{label:"VH端口",width:"65"},{default:t(({row:A})=>[s("span",null,m(A.vh_port),1)]),_:1}),e(y,{label:"状态",width:"85"},{default:t(({row:A})=>[e(he,{type:ue(A.status),size:"small"},{default:t(()=>[e(W,{class:"status-icon"},{default:t(()=>[A.status==="online"?(_(),B(k(Os),{key:0})):(_(),B(k(Ms),{key:1}))]),_:2},1024),d(" "+m(A.status==="online"?"在线":"离线"),1)]),_:2},1032,["type"])]),_:1}),e(y,{label:"设备数量",width:"75"},{default:t(({row:A})=>[s("span",en,m(A.device_count||0),1)]),_:1}),e(y,{label:"最后心跳验证",width:"160","class-name":"no-wrap"},{default:t(({row:A})=>[A.last_seen&&A.last_seen!=="null"&&A.last_seen!==""?(_(),L("span",tn,m(A.last_seen),1)):(_(),L("span",sn," 无心跳记录 "))]),_:1}),e(y,{label:"最后配置核对",width:"160","class-name":"no-wrap"},{default:t(({row:A})=>[A.last_config_check?(_(),L("span",ln,m(Ae(A.last_config_check)),1)):(_(),L("span",on,"无核对记录"))]),_:1}),e(y,{label:"操作",width:"160",fixed:"right","class-name":"no-wrap"},{default:t(({row:A})=>[e(I,{type:"primary",size:"small",onClick:We=>Ke(A)},{default:t(()=>g[17]||(g[17]=[d(" 详情 ",-1)])),_:2,__:[17]},1032,["onClick"]),K.value?(_(),B(I,{key:0,type:"warning",size:"small",onClick:We=>Ee(A,"restart")},{default:t(()=>g[18]||(g[18]=[d(" 重启 ",-1)])),_:2,__:[18]},1032,["onClick"])):J("",!0),D.value?(_(),B(I,{key:1,type:"danger",size:"small",onClick:We=>Je(A)},{default:t(()=>g[19]||(g[19]=[d(" 删除 ",-1)])),_:2,__:[19]},1032,["onClick"])):J("",!0)]),_:1})]),_:1},8,["data"])),[[Pe,te.value]]),s("div",nn,[e(Se,{"current-page":R.value,"onUpdate:currentPage":g[3]||(g[3]=A=>R.value=A),"page-size":w.value,"onUpdate:pageSize":g[4]||(g[4]=A=>w.value=A),"page-sizes":[10,20,50,100],total:r.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Te,onCurrentChange:we},null,8,["current-page","page-size","total"])]),e(Ve,{modelValue:Q.value,"onUpdate:modelValue":g[12]||(g[12]=A=>Q.value=A),title:"添加从服务器",width:"500px",onClose:Ne},{footer:t(()=>[e(I,{onClick:g[11]||(g[11]=A=>Q.value=!1)},{default:t(()=>g[20]||(g[20]=[d("取消",-1)])),_:1,__:[20]}),e(I,{type:"primary",onClick:be,loading:ae.value},{default:t(()=>g[21]||(g[21]=[d(" 确认添加 ",-1)])),_:1,__:[21]},8,["loading"])]),default:t(()=>[e(ze,{ref_key:"addFormRef",ref:V,model:C,rules:T,"label-width":"100px"},{default:t(()=>[e(Ie,{label:"服务器名称",prop:"server_name"},{default:t(()=>[e(ge,{modelValue:C.server_name,"onUpdate:modelValue":g[5]||(g[5]=A=>C.server_name=A),placeholder:"请输入服务器名称"},null,8,["modelValue"])]),_:1}),e(Ie,{label:"IP地址",prop:"server_ip"},{default:t(()=>[e(ge,{modelValue:C.server_ip,"onUpdate:modelValue":g[6]||(g[6]=A=>C.server_ip=A),placeholder:"请输入IP地址"},null,8,["modelValue"])]),_:1}),e(Ie,{label:"服务端口",prop:"server_port"},{default:t(()=>[e(et,{modelValue:C.server_port,"onUpdate:modelValue":g[7]||(g[7]=A=>C.server_port=A),min:1,max:65535,placeholder:"8889"},null,8,["modelValue"])]),_:1}),e(Ie,{label:"VH端口",prop:"vh_port"},{default:t(()=>[e(et,{modelValue:C.vh_port,"onUpdate:modelValue":g[8]||(g[8]=A=>C.vh_port=A),min:1,max:65535,placeholder:"7575"},null,8,["modelValue"])]),_:1}),e(Ie,{label:"位置"},{default:t(()=>[e(ge,{modelValue:C.location,"onUpdate:modelValue":g[9]||(g[9]=A=>C.location=A),placeholder:"请输入服务器位置"},null,8,["modelValue"])]),_:1}),e(Ie,{label:"描述"},{default:t(()=>[e(ge,{modelValue:C.description,"onUpdate:modelValue":g[10]||(g[10]=A=>C.description=A),type:"textarea",placeholder:"请输入服务器描述",rows:3},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},rn=mt(an,[["__scopeId","data-v-c35ab19a"]]),un={class:"nested-group-tree"},dn={class:"tree-header"},cn={class:"header-actions"},pn={class:"tree-content"},_n={class:"tree-node"},vn={class:"node-content"},mn={class:"node-info"},fn={class:"node-name"},gn={class:"node-tags"},yn={class:"node-stats"},hn={class:"stat-item"},bn={key:0,class:"stat-item"},kn={class:"node-actions"},wn={class:"permission-indicators"},$n={class:"action-buttons"},Cn={key:1},Tn={key:0,class:"level-warning"},Sn={class:"dialog-footer"},xn={class:"device-details"},Vn={class:"device-header"},Dn={class:"device-actions"},An={class:"device-name"},zn={class:"primary-name"},En={key:0,class:"secondary-name"},Un={class:"reorganize-content"},Pn={class:"dialog-footer"},Ln={class:"dialog-footer"},In={__name:"NestedGroupTree",props:{refreshTrigger:{type:Number,default:0}},emits:["group-created","group-updated","group-deleted"],setup(pe,{emit:P}){const G=pe,_e=P,Z=Nt(),H=c(),te=c(),ae=c(),E=c(),j=c([]),Q=c(!1),le=c(!1),F=c(!1),ne=c(!1),oe=c(!1),R=c(!1),w=c(!1),r=c(!1),V=c(null),C=c([]),T=c([]),U={children:"children",label:"name"},D=qe({name:"",group_type:"",description:"",parent_group_id:null}),z=qe({group_name:"",group_description:"",device_ids:[],source_group_id:null}),K={name:[{required:!0,message:"请输入分组名称",trigger:"blur"},{min:1,max:200,message:"分组名称长度在 1 到 200 个字符",trigger:"blur"}],group_type:[{required:!0,message:"请选择分组类型",trigger:"change"}]},ve={group_name:[{required:!0,message:"请输入新分组名称",trigger:"blur"},{min:1,max:200,message:"分组名称长度在 1 到 200 个字符",trigger:"blur"}]},me=c(),ue=qe({id:null,name:"",group_type:"",description:""}),Ae={name:[{required:!0,message:"请输入分组名称",trigger:"blur"},{min:1,max:200,message:"分组名称长度在 1 到 200 个字符",trigger:"blur"}],group_type:[{required:!0,message:"请选择分组类型",trigger:"change"}]},X=ce(()=>{if(!D.parent_group_id)return"";const y=(v,he)=>{for(const de of v){if(de.id===he)return de.name;if(de.children){const Se=y(de.children,he);if(Se)return Se}}return""};return y(j.value,D.parent_group_id)}),ee=ce(()=>{if(!D.parent_group_id)return 0;const y=(v,he)=>{for(const de of v){if(de.id===he)return de.nesting_level+1;if(de.children){const Se=y(de.children,he);if(Se!==null)return Se}}return null};return y(j.value,D.parent_group_id)||0}),xe=async()=>{try{const y=await fetch("/api/v1/device-groups/tree",{headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"}});if(y.ok)j.value=await y.json(),console.log("嵌套分组树加载成功:",j.value);else throw new Error(`HTTP ${y.status}`)}catch(y){console.error("加载嵌套分组树失败:",y),f.error(`加载嵌套分组树失败: ${y.message}`)}},Te=y=>{if(y.nesting_level>=3){f.warning("已达到最大嵌套深度（4层），无法创建子分组");return}D.parent_group_id=y.id,D.name="",D.group_type="nested",D.description="",Q.value=!0},we=async()=>{try{await te.value.validate(),oe.value=!0;const y=await fetch("/api/v1/device-groups/",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"},body:JSON.stringify(D)});if(y.ok){const v=await y.json();f.success("分组创建成功"),Q.value=!1,Ke(),xe(),_e("group-created",v)}else{const v=await y.json();throw new Error(v.detail||`HTTP ${y.status}`)}}catch(y){console.error("创建分组失败:",y),f.error(`创建分组失败: ${y.message}`)}finally{oe.value=!1}},Ke=()=>{D.name="",D.group_type="",D.description="",D.parent_group_id=null,Bt(()=>{var y;(y=te.value)==null||y.clearValidate()})},Ee=y=>{Z.push({name:"DeviceCenterGroupDetail",params:{id:y.id}})},be=y=>{ue.id=y.id,ue.name=y.name,ue.group_type=y.group_type,ue.description=y.description||"",ne.value=!0},Ne=async y=>{try{await Re.confirm(`确定要删除分组 "${y.name}" 吗？此操作将同时删除所有子分组。`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),f.info("删除功能开发中...")}catch{}},Ye=async y=>{try{V.value=y,R.value=!0,le.value=!0;const v=await fetch(`/api/v1/device-groups/${y.id}/devices`,{headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"}});if(v.ok)C.value=await v.json();else throw new Error(`HTTP ${v.status}`)}catch(v){console.error("加载分组设备失败:",v),f.error(`加载分组设备失败: ${v.message}`)}finally{R.value=!1}},Ue=()=>{V.value=null,C.value=[],le.value=!1},Me=y=>{T.value=y,z.device_ids=y.map(v=>v.id)},Xe=()=>{z.group_name="",z.group_description="",z.device_ids=[],z.source_group_id=null,T.value=[],F.value=!1},Ge=async()=>{try{await ae.value.validate(),w.value=!0;const y={group_name:z.group_name,group_description:z.group_description,device_ids:z.device_ids,source_group_id:V.value.id},v=await fetch("/api/v1/device-groups/reorganize",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"},body:JSON.stringify(y)});if(v.ok){const he=await v.json();f.success(`成功创建新分组: ${he.new_group_name}`),F.value=!1,Xe(),xe(),_e("group-created",he)}else{const he=await v.json();throw new Error(he.detail||`HTTP ${v.status}`)}}catch(y){console.error("设备重新分组失败:",y),f.error(`设备重新分组失败: ${y.message}`)}finally{w.value=!1}},Je=async()=>{try{await me.value.validate(),r.value=!0;const y=await fetch(`/api/v1/device-groups/${ue.id}`,{method:"PUT",headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"},body:JSON.stringify({name:ue.name,group_type:ue.group_type,description:ue.description})});if(y.ok)f.success("分组信息更新成功"),ne.value=!1,$(),xe();else{const v=await y.json();throw new Error(v.detail||`HTTP ${y.status}`)}}catch(y){console.error("更新分组失败:",y),f.error(`更新分组失败: ${y.message}`)}finally{r.value=!1}},$=()=>{ue.id=null,ue.name="",ue.group_type="",ue.description="",Bt(()=>{var y;(y=me.value)==null||y.clearValidate()})},g=y=>({server:"服务器",mixed:"混合",single:"单设备",nested:"嵌套"})[y]||y,W=y=>({server:"success",mixed:"primary",single:"warning",nested:"info",reorganized:"danger"})[y]||"info",I=y=>({online:"在线",offline:"离线",busy:"占用",available:"可用",error:"错误"})[y]||y,ke=y=>({online:"success",offline:"info",busy:"warning",available:"success",error:"danger"})[y]||"info",fe=y=>({ca_lock:"CA锁",encryption_key:"加密锁",bank_ukey:"银行U盾"})[y]||y,ge=y=>({ca_lock:"danger",encryption_key:"warning",bank_ukey:"primary"})[y]||"info";return bt(()=>G.refreshTrigger,()=>{xe()}),ut(()=>{xe()}),(y,v)=>{const he=ft,de=gt,Se=kt,Ie=Gt,et=ms,ze=zt,Ve=Kt,Pe=Ut,A=Et,We=fs,tt=qt,Ze=At,it=Mt("Operation"),De=Ht,p=Ft,u=wt;return _(),L("div",un,[s("div",dn,[v[19]||(v[19]=s("h3",null,"嵌套分组管理",-1)),s("div",cn,[e(de,{type:"primary",onClick:xe},{default:t(()=>[e(he,null,{default:t(()=>[e(k(vt))]),_:1}),v[17]||(v[17]=d(" 刷新 ",-1))]),_:1,__:[17]}),e(de,{type:"success",onClick:v[0]||(v[0]=x=>Q.value=!0)},{default:t(()=>[e(he,null,{default:t(()=>[e(k(Xt))]),_:1}),v[18]||(v[18]=d(" 创建分组 ",-1))]),_:1,__:[18]})])]),s("div",pn,[e(et,{ref_key:"treeRef",ref:H,data:j.value,props:U,"expand-on-click-node":!1,"default-expand-all":!1,"node-key":"id",class:"group-tree"},{default:t(({node:x,data:N})=>[s("div",_n,[s("div",vn,[s("div",mn,[s("span",fn,m(N.name),1),s("div",gn,[e(Se,{type:W(N.group_type),size:"small"},{default:t(()=>[d(m(g(N.group_type)),1)]),_:2},1032,["type"]),e(Se,{type:"info",size:"small"},{default:t(()=>[d(" L"+m(N.nesting_level),1)]),_:2},1024)])]),s("div",yn,[s("span",hn,[e(he,null,{default:t(()=>[e(k(_t))]),_:1}),d(" "+m(N.device_count)+"设备 ",1)]),N.child_count>0?(_(),L("span",bn,[e(he,null,{default:t(()=>[e(k(Rs))]),_:1}),d(" "+m(N.child_count)+"子组 ",1)])):J("",!0)])]),s("div",kn,[s("div",wn,[N.is_readonly?(_(),B(Se,{key:0,type:"info",size:"small"},{default:t(()=>v[20]||(v[20]=[d(" 只读 ",-1)])),_:1,__:[20]})):J("",!0),N.can_view_devices?J("",!0):(_(),B(Se,{key:1,type:"warning",size:"small"},{default:t(()=>v[21]||(v[21]=[d(" 无设备权限 ",-1)])),_:1,__:[21]}))]),s("div",$n,[e(de,{type:"primary",size:"small",onClick:Y=>Ee(N)},{default:t(()=>v[22]||(v[22]=[d(" 详情 ",-1)])),_:2,__:[22]},1032,["onClick"]),N.can_view_devices?(_(),B(de,{key:0,type:"info",size:"small",onClick:Y=>Ye(N)},{default:t(()=>v[23]||(v[23]=[d(" 查看设备 ",-1)])),_:2,__:[23]},1032,["onClick"])):J("",!0),N.can_manage?(_(),L(Oe,{key:1},[e(de,{type:"success",size:"small",onClick:Y=>Te(N),disabled:N.nesting_level>=3},{default:t(()=>[N.nesting_level>=3?(_(),B(Ie,{key:0,content:"已达到最大嵌套深度（4层）",placement:"top"},{default:t(()=>v[24]||(v[24]=[s("span",null,"添加子组",-1)])),_:1,__:[24]})):(_(),L("span",Cn,"添加子组"))]),_:2},1032,["onClick","disabled"]),e(de,{type:"warning",size:"small",onClick:Y=>be(N)},{default:t(()=>v[25]||(v[25]=[d(" 编辑 ",-1)])),_:2,__:[25]},1032,["onClick"]),e(de,{type:"danger",size:"small",onClick:Y=>Ne(N)},{default:t(()=>v[26]||(v[26]=[d(" 删除 ",-1)])),_:2,__:[26]},1032,["onClick"])],64)):J("",!0)])])])]),_:1},8,["data"])]),e(Ze,{modelValue:Q.value,"onUpdate:modelValue":v[5]||(v[5]=x=>Q.value=x),title:D.parent_group_id?"创建子分组":"创建顶级分组",width:"500px",onClose:Ke},{footer:t(()=>[s("div",Sn,[e(de,{onClick:v[4]||(v[4]=x=>Q.value=!1)},{default:t(()=>v[27]||(v[27]=[d("取消",-1)])),_:1,__:[27]}),e(de,{type:"primary",onClick:we,loading:oe.value,disabled:ee.value>=4},{default:t(()=>v[28]||(v[28]=[d(" 创建 ",-1)])),_:1,__:[28]},8,["loading","disabled"])])]),default:t(()=>[e(tt,{ref_key:"createFormRef",ref:te,model:D,rules:K,"label-width":"120px"},{default:t(()=>[D.parent_group_id?(_(),B(Ve,{key:0,label:"父分组"},{default:t(()=>[e(ze,{value:X.value,disabled:"",placeholder:"顶级分组"},null,8,["value"])]),_:1})):J("",!0),e(Ve,{label:"分组名称",prop:"name"},{default:t(()=>[e(ze,{modelValue:D.name,"onUpdate:modelValue":v[1]||(v[1]=x=>D.name=x),placeholder:"请输入分组名称"},null,8,["modelValue"])]),_:1}),e(Ve,{label:"分组类型",prop:"group_type"},{default:t(()=>[e(A,{modelValue:D.group_type,"onUpdate:modelValue":v[2]||(v[2]=x=>D.group_type=x),placeholder:"请选择分组类型",style:{width:"100%"}},{default:t(()=>[e(Pe,{label:"服务器分组",value:"server"}),e(Pe,{label:"混合分组",value:"mixed"}),e(Pe,{label:"单设备分组",value:"single"}),e(Pe,{label:"嵌套分组",value:"nested"})]),_:1},8,["modelValue"])]),_:1}),e(Ve,{label:"分组描述",prop:"description"},{default:t(()=>[e(ze,{modelValue:D.description,"onUpdate:modelValue":v[3]||(v[3]=x=>D.description=x),type:"textarea",rows:3,placeholder:"请输入分组描述"},null,8,["modelValue"])]),_:1}),D.parent_group_id?(_(),B(Ve,{key:1,label:"嵌套层级"},{default:t(()=>[e(ze,{value:`第 ${ee.value} 层`,disabled:""},null,8,["value"]),ee.value>=3?(_(),L("div",Tn,[e(We,{title:"已达到最大嵌套深度（4层）",type:"warning",closable:!1,"show-icon":""})])):J("",!0)]),_:1})):J("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),e(Ze,{modelValue:le.value,"onUpdate:modelValue":v[7]||(v[7]=x=>le.value=x),title:"分组设备详情",width:"80%",onClose:Ue},{default:t(()=>{var x,N;return[s("div",xn,[s("div",Vn,[s("h4",null,m((x=V.value)==null?void 0:x.name)+" - 设备列表",1),s("div",Dn,[(N=V.value)!=null&&N.can_manage?(_(),B(de,{key:0,type:"success",onClick:v[6]||(v[6]=Y=>F.value=!0)},{default:t(()=>[e(he,null,{default:t(()=>[e(it)]),_:1}),v[29]||(v[29]=d(" 重新分组 ",-1))]),_:1,__:[29]})):J("",!0)])]),at((_(),B(p,{data:C.value,stripe:"",style:{width:"100%"}},{default:t(()=>[e(De,{prop:"name",label:"设备名称","min-width":"150"},{default:t(({row:Y})=>[s("div",An,[s("span",zn,m(Y.custom_name||Y.name),1),Y.custom_name&&Y.name!==Y.custom_name?(_(),L("span",En," ("+m(Y.name)+") ",1)):J("",!0)])]),_:1}),e(De,{prop:"device_type",label:"设备类型",width:"120"},{default:t(({row:Y})=>[e(Se,{type:ge(Y.device_type),size:"small"},{default:t(()=>[d(m(fe(Y.device_type)),1)]),_:2},1032,["type"])]),_:1}),e(De,{prop:"status",label:"状态",width:"100"},{default:t(({row:Y})=>[e(Se,{type:ke(Y.status),size:"small"},{default:t(()=>[d(m(I(Y.status)),1)]),_:2},1032,["type"])]),_:1}),e(De,{prop:"slave_server_name",label:"所属服务器",width:"150"}),e(De,{prop:"vid",label:"VID",width:"80"}),e(De,{prop:"pid",label:"PID",width:"80"}),e(De,{prop:"description",label:"描述","min-width":"200"})]),_:1},8,["data"])),[[u,R.value]])])]}),_:1},8,["modelValue"]),e(Ze,{modelValue:F.value,"onUpdate:modelValue":v[11]||(v[11]=x=>F.value=x),title:"设备重新分组",width:"60%",onClose:Xe},{footer:t(()=>[s("div",Pn,[e(de,{onClick:v[10]||(v[10]=x=>F.value=!1)},{default:t(()=>v[30]||(v[30]=[d("取消",-1)])),_:1,__:[30]}),e(de,{type:"primary",onClick:Ge,loading:w.value,disabled:T.value.length===0},{default:t(()=>v[31]||(v[31]=[d(" 创建新分组 ",-1)])),_:1,__:[31]},8,["loading","disabled"])])]),default:t(()=>[s("div",Un,[e(tt,{ref_key:"reorganizeFormRef",ref:ae,model:z,rules:ve,"label-width":"120px"},{default:t(()=>[e(Ve,{label:"新分组名称",prop:"group_name"},{default:t(()=>[e(ze,{modelValue:z.group_name,"onUpdate:modelValue":v[8]||(v[8]=x=>z.group_name=x),placeholder:"请输入新分组名称"},null,8,["modelValue"])]),_:1}),e(Ve,{label:"分组描述",prop:"group_description"},{default:t(()=>[e(ze,{modelValue:z.group_description,"onUpdate:modelValue":v[9]||(v[9]=x=>z.group_description=x),type:"textarea",rows:3,placeholder:"请输入分组描述"},null,8,["modelValue"])]),_:1}),e(Ve,{label:"选择设备"},{default:t(()=>[e(p,{ref_key:"deviceSelectionTable",ref:E,data:C.value,onSelectionChange:Me,"max-height":"300"},{default:t(()=>[e(De,{type:"selection",width:"55"}),e(De,{prop:"name",label:"设备名称","min-width":"150"}),e(De,{prop:"device_type",label:"类型",width:"100"}),e(De,{prop:"status",label:"状态",width:"80"})]),_:1},8,["data"])]),_:1})]),_:1},8,["model"])])]),_:1},8,["modelValue"]),e(Ze,{modelValue:ne.value,"onUpdate:modelValue":v[16]||(v[16]=x=>ne.value=x),title:"编辑分组",width:"500px",onClose:$},{footer:t(()=>[s("div",Ln,[e(de,{onClick:v[15]||(v[15]=x=>ne.value=!1)},{default:t(()=>v[32]||(v[32]=[d("取消",-1)])),_:1,__:[32]}),e(de,{type:"primary",onClick:Je,loading:r.value},{default:t(()=>v[33]||(v[33]=[d(" 确认修改 ",-1)])),_:1,__:[33]},8,["loading"])])]),default:t(()=>[e(tt,{ref_key:"editFormRef",ref:me,model:ue,rules:Ae,"label-width":"100px"},{default:t(()=>[e(Ve,{label:"分组名称",prop:"name"},{default:t(()=>[e(ze,{modelValue:ue.name,"onUpdate:modelValue":v[12]||(v[12]=x=>ue.name=x),placeholder:"请输入分组名称"},null,8,["modelValue"])]),_:1}),e(Ve,{label:"分组类型",prop:"group_type"},{default:t(()=>[e(A,{modelValue:ue.group_type,"onUpdate:modelValue":v[13]||(v[13]=x=>ue.group_type=x),placeholder:"请选择分组类型",style:{width:"100%"}},{default:t(()=>[e(Pe,{label:"标准分组",value:"standard"}),e(Pe,{label:"临时分组",value:"temporary"}),e(Pe,{label:"专用分组",value:"dedicated"}),e(Pe,{label:"重组分组",value:"reorganized"})]),_:1},8,["modelValue"])]),_:1}),e(Ve,{label:"描述信息",prop:"description"},{default:t(()=>[e(ze,{modelValue:ue.description,"onUpdate:modelValue":v[14]||(v[14]=x=>ue.description=x),type:"textarea",rows:3,placeholder:"请输入分组描述"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},jn=mt(In,[["__scopeId","data-v-81ed4342"]]),On={class:"device-group-management"},Mn={class:"toolbar"},Bn={class:"toolbar-left"},Rn={class:"toolbar-right"},Nn={class:"view-tabs"},Gn={class:"list-view"},Fn={class:"group-name"},Hn={class:"device-count"},qn={key:1,class:"text-muted"},Kn={class:"user-count"},Yn={class:"tree-view"},Jn={class:"pagination-container"},Wn={class:"assignment-content"},Zn={class:"assignment-stats"},Qn={class:"device-selection",style:{"margin-top":"20px"}},Xn={class:"dialog-footer"},ea={__name:"DeviceGroupManagement",emits:["stats-update"],setup(pe,{expose:P,emit:G}){const _e=G,Z=Nt(),H=dt(),te=c(!1),ae=c(!1),E=c(!1),j=c(!1),Q=c(!1),le=c(!1),F=c([]),ne=c(""),oe=c(""),R=c("list"),w=c(0),r=c(!1),V=c(null),C=c([]),T=c([]),U=c(),D=c(1),z=c(20),K=c(0),ve=c(),me=qe({name:"",group_type:"",description:""}),ue={name:[{required:!0,message:"请输入分组名称",trigger:"blur"}],group_type:[{required:!0,message:"请选择分组类型",trigger:"change"}]},Ae=c(),X=qe({id:null,name:"",group_type:"",description:""}),ee={name:[{required:!0,message:"请输入分组名称",trigger:"blur"}],group_type:[{required:!0,message:"请选择分组类型",trigger:"change"}]},xe=ce(()=>{let p=F.value;if(ne.value&&(p=p.filter(u=>u.group_type===ne.value)),oe.value){const u=oe.value.toLowerCase();p=p.filter(x=>x.name.toLowerCase().includes(u))}return p}),Te=p=>({single_device:"info",multi_device:"primary",server_group:"success",cross_group:"warning",batch_group:"danger"})[p]||"",we=p=>({single_device:"单设备分组",multi_device:"多设备分组",server_group:"服务器分组",cross_group:"交叉分组",batch_group:"批量分组"})[p]||"未知类型",Ke=p=>{switch(p){case"single_device":f.info("单设备分组：每个分组只包含一个设备");break;case"multi_device":f.info("多设备分组：可以包含多个不同的设备");break;case"server_group":f.info("服务器分组：按从服务器整体进行分组");break;case"cross_group":f.info("交叉分组：设备可以同时属于多个分组");break;case"batch_group":f.info("批量分组：支持批量设备的分组操作");break}},Ee=p=>p?new Date(p).toLocaleString():"",be=async()=>{te.value=!0;try{const p=await fetch("/api/v1/device-groups/",{headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"}});if(!p.ok)throw new Error(`HTTP ${p.status}: ${p.statusText}`);const u=await p.json();console.log("获取到的设备分组数据:",u),F.value=u.map(x=>({id:x.id,name:x.name,group_type:x.group_type,device_count:x.device_count||0,has_virtual_devices:x.has_virtual_devices||!1,user_count:x.user_count||0,description:x.description||"",created_at:x.created_at,updated_at:x.updated_at,is_active:x.is_active})),console.log("处理后的设备分组数据:",F.value),K.value=F.value.length,Ne()}catch(p){console.error("加载设备分组数据失败:",p),f.error(`加载设备分组数据失败: ${p.message}`),F.value=[],K.value=0}finally{te.value=!1}},Ne=()=>{const p={total:F.value.length,server:F.value.filter(u=>u.group_type==="server").length,mixed:F.value.filter(u=>u.group_type==="mixed").length};_e("stats-update",p)},Ye=()=>{},Ue=p=>{z.value=p,be()},Me=p=>{D.value=p,be()},Xe=p=>{try{console.log("点击详情按钮，分组信息:",p),console.log("准备跳转到路径:",`/device-center/device-group/${p.id}`),Z.push(`/device-center/device-group/${p.id}`).then(()=>{console.log("路由跳转成功")}).catch(u=>{console.error("路由跳转失败:",u),f.error(`跳转失败: ${u.message}`)})}catch(u){console.error("viewGroupDetail函数执行失败:",u),f.error(`操作失败: ${u.message}`)}},Ge=p=>{Object.assign(X,{id:p.id,name:p.name,group_type:p.group_type,description:p.description}),Q.value=!0},Je=async p=>{try{await Re.confirm(`确定要删除分组 "${p.name}" 吗？此操作不可恢复。`,"确认删除",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"}),f.success(`分组 "${p.name}" 删除成功`),be()}catch{}},$=async()=>{try{await ve.value.validate(),ae.value=!0;const p=await fetch("/api/v1/device-groups/",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"},body:JSON.stringify({name:me.name,group_type:me.type,description:me.description,auto_virtual:!1})});if(!p.ok){const x=await p.json();throw new Error(x.detail||`HTTP ${p.status}`)}const u=await p.json();console.log("分组创建成功:",u),f.success("设备分组创建成功"),j.value=!1,W(),be()}catch(p){console.error("创建设备分组失败:",p),f.error(`创建设备分组失败: ${p.message}`)}finally{ae.value=!1}},g=async()=>{try{await Ae.value.validate(),E.value=!0,await new Promise(p=>setTimeout(p,1e3)),f.success("设备分组修改成功"),Q.value=!1,I(),be()}catch(p){console.error("修改设备分组失败:",p),f.error("修改设备分组失败")}finally{E.value=!1}},W=()=>{var p;Object.assign(me,{name:"",group_type:"",description:""}),(p=ve.value)==null||p.clearValidate()},I=()=>{var p;Object.assign(X,{id:null,name:"",group_type:"",description:""}),(p=Ae.value)==null||p.clearValidate()};P({refreshData:be});const ke=p=>{console.log("分组创建事件:",p),f.success(`分组 "${p.group_name}" 已创建`),be()},fe=p=>{console.log("分组更新事件:",p),be()},ge=p=>{console.log("设备分配事件:",p),f.info(`${p.added_count||p.total_assigned} 个设备已分配到分组`),be()},y=async()=>{le.value=!0,await Promise.all([v(),be()])},v=async()=>{try{const p=await fetch("/api/v1/devices",{headers:{Authorization:`Bearer ${H.token}`,"Content-Type":"application/json"}});if(p.ok){const u=await p.json();T.value=u.devices||[]}else throw new Error(`HTTP ${p.status}`)}catch(p){console.error("加载可用设备失败:",p),f.error(`加载可用设备失败: ${p.message}`)}},he=p=>{V.value=p,C.value=[],U.value&&U.value.clearSelection()},de=p=>{C.value=p},Se=async()=>{try{r.value=!0;const p={assignments:[{group_id:V.value,device_ids:C.value.map(x=>x.id)}]},u=await fetch("/api/v1/device-groups/batch-assign",{method:"POST",headers:{Authorization:`Bearer ${H.token}`,"Content-Type":"application/json"},body:JSON.stringify(p)});if(u.ok){const x=await u.json(),N=x.assignments?x.assignments.reduce((Y,a)=>Y+a.added_devices.length,0):0;f.success(`成功分配 ${N} 个设备，可继续分配其他设备`),C.value=[],U.value&&U.value.clearSelection(),await v(),be()}else{const x=await u.json();throw new Error(x.detail||`HTTP ${u.status}`)}}catch(p){console.error("设备分配失败:",p),f.error(`设备分配失败: ${p.message}`)}finally{r.value=!1}},Ie=()=>{le.value=!1,f.success("设备分配操作已完成")},et=()=>{V.value=null,C.value=[],T.value=[],U.value&&U.value.clearSelection()},ze=p=>({ca_lock:"CA锁",encryption_key:"加密锁",bank_ukey:"银行U盾",other:"其他设备"})[p]||p,Ve=p=>({online:"在线",offline:"离线",busy:"占用",available:"可用",error:"错误"})[p]||p,Pe=p=>({online:"success",offline:"info",busy:"warning",available:"success",error:"danger"})[p]||"info",A=p=>{console.log("切换视图:",p),p==="tree"&&w.value++},We=p=>{console.log("嵌套分组创建:",p),be(),w.value++},tt=p=>{console.log("嵌套分组更新:",p),be(),w.value++},Ze=p=>{console.log("嵌套分组删除:",p),be(),w.value++},it=()=>{re.subscribe("device_updates"),re.on("device_group_created",ke),re.on("device_groups_batch_created",fe),re.on("devices_assigned_to_group",ge),re.on("devices_batch_assigned",ge)},De=()=>{re.off("device_group_created",ke),re.off("device_groups_batch_created",fe),re.off("devices_assigned_to_group",ge),re.off("devices_batch_assigned",ge),re.unsubscribe("device_updates")};return ut(()=>{be(),it()}),Dt(()=>{De()}),(p,u)=>{const x=ft,N=gt,Y=Ut,a=Et,n=zt,S=Mt("List"),q=es,ie=Mt("Operation"),Fe=ts,Ce=Ht,je=kt,$e=Gt,Qe=Ft,lt=Qt,Be=Kt,rt=qt,ot=At,yt=fs,He=ls,$t=ss,Ct=wt;return _(),L("div",On,[s("div",Mn,[s("div",Bn,[e(N,{type:"primary",onClick:be,loading:te.value},{default:t(()=>[e(x,null,{default:t(()=>[e(k(vt))]),_:1}),u[19]||(u[19]=d(" 刷新列表 ",-1))]),_:1,__:[19]},8,["loading"]),k(H).hasPermission("device.group")?(_(),B(N,{key:0,type:"success",onClick:u[0]||(u[0]=l=>j.value=!0)},{default:t(()=>[e(x,null,{default:t(()=>[e(k(Xt))]),_:1}),u[20]||(u[20]=d(" 创建分组 ",-1))]),_:1,__:[20]})):J("",!0),k(H).hasPermission("device.assign")?(_(),B(N,{key:1,type:"primary",onClick:y},{default:t(()=>[e(x,null,{default:t(()=>[e(k(Rt))]),_:1}),u[21]||(u[21]=d(" 设备分配 ",-1))]),_:1,__:[21]})):J("",!0)]),s("div",Rn,[e(a,{modelValue:ne.value,"onUpdate:modelValue":u[1]||(u[1]=l=>ne.value=l),placeholder:"筛选分组类型",style:{width:"180px","margin-right":"8px"},clearable:"",onChange:Ye},{default:t(()=>[e(Y,{label:"全部分组",value:""}),e(Y,{label:"单设备分组",value:"single_device"}),e(Y,{label:"多设备分组",value:"multi_device"}),e(Y,{label:"服务器分组",value:"server_group"}),e(Y,{label:"交叉分组",value:"cross_group"}),e(Y,{label:"批量分组",value:"batch_group"})]),_:1},8,["modelValue"]),e(n,{modelValue:oe.value,"onUpdate:modelValue":u[2]||(u[2]=l=>oe.value=l),placeholder:"搜索分组名称...",style:{width:"200px"},clearable:"",onInput:Ye},{prefix:t(()=>[e(x,null,{default:t(()=>[e(k(pt))]),_:1})]),_:1},8,["modelValue"])])]),s("div",Nn,[e(Fe,{modelValue:R.value,"onUpdate:modelValue":u[3]||(u[3]=l=>R.value=l),onTabChange:A},{default:t(()=>[e(q,{label:"列表视图",name:"list"},{label:t(()=>[s("span",null,[e(x,null,{default:t(()=>[e(S)]),_:1}),u[22]||(u[22]=d(" 列表视图 ",-1))])]),_:1}),e(q,{label:"嵌套树视图",name:"tree"},{label:t(()=>[s("span",null,[e(x,null,{default:t(()=>[e(ie)]),_:1}),u[23]||(u[23]=d(" 嵌套树视图 ",-1))])]),_:1})]),_:1},8,["modelValue"])]),at(s("div",Gn,[at((_(),B(Qe,{data:xe.value,stripe:"",style:{width:"100%"}},{default:t(()=>[e(Ce,{prop:"id",label:"ID",width:"80"}),e(Ce,{label:"分组名称","min-width":"150"},{default:t(({row:l})=>[s("div",Fn,[e(x,{class:"group-icon"},{default:t(()=>[e(k(Rt))]),_:1}),s("span",null,m(l.name),1)])]),_:1}),e(Ce,{label:"分组类型",width:"120"},{default:t(({row:l})=>[e(je,{type:Te(l.group_type),size:"small"},{default:t(()=>[d(m(we(l.group_type)),1)]),_:2},1032,["type"])]),_:1}),e(Ce,{label:"设备数量",width:"100"},{default:t(({row:l})=>[s("span",Hn,m(l.device_count||0),1)]),_:1}),e(Ce,{label:"虚拟设备",width:"100"},{default:t(({row:l})=>[l.has_virtual_devices?(_(),B(je,{key:0,type:"warning",size:"small"},{default:t(()=>u[24]||(u[24]=[d(" 有占位 ",-1)])),_:1,__:[24]})):(_(),L("span",qn,"无"))]),_:1}),e(Ce,{label:"权限用户",width:"100"},{default:t(({row:l})=>[s("span",Kn,m(l.user_count||0),1)]),_:1}),e(Ce,{label:"创建时间",width:"150"},{default:t(({row:l})=>[s("span",null,m(Ee(l.created_at)),1)]),_:1}),e(Ce,{prop:"description",label:"描述","min-width":"150"}),e(Ce,{label:"操作",width:"280",fixed:"right"},{default:t(({row:l})=>[e(N,{type:"primary",size:"small",onClick:o=>Xe(l)},{default:t(()=>u[25]||(u[25]=[d(" 详情 ",-1)])),_:2,__:[25]},1032,["onClick"]),l.is_readonly?(_(),B(je,{key:0,type:"info",size:"small",style:{margin:"0 5px"}},{default:t(()=>u[26]||(u[26]=[d(" 只读 ",-1)])),_:1,__:[26]})):J("",!0),l.can_manage?(_(),L(Oe,{key:1},[e(N,{type:"warning",size:"small",onClick:o=>Ge(l)},{default:t(()=>u[27]||(u[27]=[d(" 编辑 ",-1)])),_:2,__:[27]},1032,["onClick"]),e(N,{type:"danger",size:"small",onClick:o=>Je(l)},{default:t(()=>u[28]||(u[28]=[d(" 删除 ",-1)])),_:2,__:[28]},1032,["onClick"])],64)):l.is_readonly?(_(),B($e,{key:2,content:"此分组由上级管理员创建，您只有查看权限",placement:"top"},{default:t(()=>[e(N,{type:"info",size:"small",disabled:""},{default:t(()=>u[29]||(u[29]=[d(" 无权限 ",-1)])),_:1,__:[29]})]),_:1})):J("",!0)]),_:1})]),_:1},8,["data"])),[[Ct,te.value]])],512),[[us,R.value==="list"]]),at(s("div",Yn,[e(jn,{"refresh-trigger":w.value,onGroupCreated:We,onGroupUpdated:tt,onGroupDeleted:Ze},null,8,["refresh-trigger"])],512),[[us,R.value==="tree"]]),s("div",Jn,[e(lt,{"current-page":D.value,"onUpdate:currentPage":u[4]||(u[4]=l=>D.value=l),"page-size":z.value,"onUpdate:pageSize":u[5]||(u[5]=l=>z.value=l),"page-sizes":[10,20,50,100],total:K.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Ue,onCurrentChange:Me},null,8,["current-page","page-size","total"])]),e(ot,{modelValue:j.value,"onUpdate:modelValue":u[10]||(u[10]=l=>j.value=l),title:"创建设备分组",width:"500px",onClose:W},{footer:t(()=>[e(N,{onClick:u[9]||(u[9]=l=>j.value=!1)},{default:t(()=>u[35]||(u[35]=[d("取消",-1)])),_:1,__:[35]}),e(N,{type:"primary",onClick:$,loading:ae.value},{default:t(()=>u[36]||(u[36]=[d(" 确认创建 ",-1)])),_:1,__:[36]},8,["loading"])]),default:t(()=>[e(rt,{ref_key:"createFormRef",ref:ve,model:me,rules:ue,"label-width":"100px"},{default:t(()=>[e(Be,{label:"分组名称",prop:"name"},{default:t(()=>[e(n,{modelValue:me.name,"onUpdate:modelValue":u[6]||(u[6]=l=>me.name=l),placeholder:"请输入分组名称"},null,8,["modelValue"])]),_:1}),e(Be,{label:"分组类型",prop:"group_type"},{default:t(()=>[e(a,{modelValue:me.group_type,"onUpdate:modelValue":u[7]||(u[7]=l=>me.group_type=l),placeholder:"请选择分组类型",onChange:Ke},{default:t(()=>[e(Y,{label:"单设备分组",value:"single_device"},{default:t(()=>u[30]||(u[30]=[s("div",{class:"option-content"},[s("div",{class:"option-title"},"单设备分组"),s("div",{class:"option-desc"},"一个设备独立成组")],-1)])),_:1,__:[30]}),e(Y,{label:"多设备分组",value:"multi_device"},{default:t(()=>u[31]||(u[31]=[s("div",{class:"option-content"},[s("div",{class:"option-title"},"多设备分组"),s("div",{class:"option-desc"},"多个设备组成一个分组")],-1)])),_:1,__:[31]}),e(Y,{label:"服务器分组",value:"server_group"},{default:t(()=>u[32]||(u[32]=[s("div",{class:"option-content"},[s("div",{class:"option-title"},"服务器分组"),s("div",{class:"option-desc"},"以从服务器为单位进行分组")],-1)])),_:1,__:[32]}),e(Y,{label:"交叉分组",value:"cross_group"},{default:t(()=>u[33]||(u[33]=[s("div",{class:"option-content"},[s("div",{class:"option-title"},"交叉分组"),s("div",{class:"option-desc"},"一个设备可属于多个分组")],-1)])),_:1,__:[33]}),e(Y,{label:"批量分组",value:"batch_group"},{default:t(()=>u[34]||(u[34]=[s("div",{class:"option-content"},[s("div",{class:"option-title"},"批量分组"),s("div",{class:"option-desc"},"支持批量设备的分组操作")],-1)])),_:1,__:[34]})]),_:1},8,["modelValue"])]),_:1}),e(Be,{label:"描述"},{default:t(()=>[e(n,{modelValue:me.description,"onUpdate:modelValue":u[8]||(u[8]=l=>me.description=l),type:"textarea",placeholder:"请输入分组描述",rows:3},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),e(ot,{modelValue:Q.value,"onUpdate:modelValue":u[15]||(u[15]=l=>Q.value=l),title:"编辑设备分组",width:"500px",onClose:I},{footer:t(()=>[e(N,{onClick:u[14]||(u[14]=l=>Q.value=!1)},{default:t(()=>u[37]||(u[37]=[d("取消",-1)])),_:1,__:[37]}),e(N,{type:"primary",onClick:g,loading:E.value},{default:t(()=>u[38]||(u[38]=[d(" 确认修改 ",-1)])),_:1,__:[38]},8,["loading"])]),default:t(()=>[e(rt,{ref_key:"editFormRef",ref:Ae,model:X,rules:ee,"label-width":"100px"},{default:t(()=>[e(Be,{label:"分组名称",prop:"name"},{default:t(()=>[e(n,{modelValue:X.name,"onUpdate:modelValue":u[11]||(u[11]=l=>X.name=l),placeholder:"请输入分组名称"},null,8,["modelValue"])]),_:1}),e(Be,{label:"分组类型",prop:"group_type"},{default:t(()=>[e(a,{modelValue:X.group_type,"onUpdate:modelValue":u[12]||(u[12]=l=>X.group_type=l),placeholder:"请选择分组类型"},{default:t(()=>[e(Y,{label:"单设备分组",value:"single_device"}),e(Y,{label:"多设备分组",value:"multi_device"}),e(Y,{label:"服务器分组",value:"server_group"}),e(Y,{label:"交叉分组",value:"cross_group"}),e(Y,{label:"批量分组",value:"batch_group"})]),_:1},8,["modelValue"])]),_:1}),e(Be,{label:"描述"},{default:t(()=>[e(n,{modelValue:X.description,"onUpdate:modelValue":u[13]||(u[13]=l=>X.description=l),type:"textarea",placeholder:"请输入分组描述",rows:3},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),e(ot,{modelValue:le.value,"onUpdate:modelValue":u[18]||(u[18]=l=>le.value=l),title:"设备分配",width:"80%",onClose:et},{footer:t(()=>[s("div",Xn,[e(N,{onClick:u[17]||(u[17]=l=>le.value=!1)},{default:t(()=>u[42]||(u[42]=[d("取消",-1)])),_:1,__:[42]}),e(N,{type:"success",onClick:Ie},{default:t(()=>u[43]||(u[43]=[d(" 完成分配 ",-1)])),_:1,__:[43]}),e(N,{type:"primary",onClick:Se,loading:r.value,disabled:!V.value||C.value.length===0},{default:t(()=>u[44]||(u[44]=[d(" 分配设备 ",-1)])),_:1,__:[44]},8,["loading","disabled"])])]),default:t(()=>[s("div",Wn,[e(yt,{title:"批量分配提示",description:"选择目标分组和设备后点击【分配设备】，分配成功后可继续选择其他设备进行下一轮分配，完成所有操作后点击【完成分配】关闭对话框。",type:"info",closable:!1,style:{"margin-bottom":"20px"}}),e($t,{gutter:20},{default:t(()=>[e(He,{span:12},{default:t(()=>[u[39]||(u[39]=s("h4",null,"选择设备分组",-1)),e(a,{modelValue:V.value,"onUpdate:modelValue":u[16]||(u[16]=l=>V.value=l),placeholder:"请选择目标分组",style:{width:"100%"},onChange:he},{default:t(()=>[(_(!0),L(Oe,null,st(F.value,l=>(_(),B(Y,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1,__:[39]}),e(He,{span:12},{default:t(()=>[u[40]||(u[40]=s("h4",null,"设备分配状态",-1)),s("div",Zn,[e(je,{type:"info"},{default:t(()=>[d("待分配: "+m(T.value.length),1)]),_:1}),e(je,{type:"success",style:{"margin-left":"10px"}},{default:t(()=>[d("已选择: "+m(C.value.length),1)]),_:1})])]),_:1,__:[40]})]),_:1}),s("div",Qn,[u[41]||(u[41]=s("h4",null,"选择设备",-1)),e(Qe,{ref_key:"deviceSelectionTable",ref:U,data:T.value,onSelectionChange:de,"max-height":"400"},{default:t(()=>[e(Ce,{type:"selection",width:"55"}),e(Ce,{label:"设备名称","min-width":"150"},{default:t(({row:l})=>[d(m(l.custom_name||l.device_name||l.name||"未知设备"),1)]),_:1}),e(Ce,{prop:"device_type",label:"设备类型",width:"120"},{default:t(({row:l})=>[e(je,{size:"small"},{default:t(()=>[d(m(ze(l.device_type)),1)]),_:2},1024)]),_:1}),e(Ce,{prop:"status",label:"状态",width:"100"},{default:t(({row:l})=>[e(je,{type:Pe(l.status),size:"small"},{default:t(()=>[d(m(Ve(l.status)),1)]),_:2},1032,["type"])]),_:1}),e(Ce,{label:"所属服务器",width:"150"},{default:t(({row:l})=>{var o;return[d(m(l.server_name||((o=l.slave_server)==null?void 0:o.name)||l.slave_server_name||"未知服务器"),1)]}),_:1})]),_:1},8,["data"])])])]),_:1},8,["modelValue"])])}}},ta=mt(ea,[["__scopeId","data-v-01709d2b"]]),Jt="permission_audit_logs",sa=1e3,la={ASSIGN:"assign",REVOKE:"revoke",MODIFY:"modify",BATCH_ASSIGN:"batch_assign",BATCH_REVOKE:"batch_revoke",COPY:"copy",TEMPLATE_APPLY:"template_apply"},oa={DEVICE_GROUP:"device_group",DEVICE:"device",ORGANIZATION:"organization",USER:"user"};class xt{static getLogs(){try{const P=localStorage.getItem(Jt);return P?JSON.parse(P):[]}catch(P){return console.error("读取审计日志失败:",P),[]}}static saveLogs(P){try{const G=P.slice(-sa);localStorage.setItem(Jt,JSON.stringify(G))}catch(G){console.error("保存审计日志失败:",G)}}static addLog(P){const G=this.getLogs();G.push({...P,id:Date.now()+Math.random(),timestamp:new Date().toISOString(),userAgent:navigator.userAgent,sessionId:this.getSessionId()}),this.saveLogs(G)}static getSessionId(){let P=sessionStorage.getItem("audit_session_id");return P||(P=Date.now()+"_"+Math.random().toString(36).substr(2,9),sessionStorage.setItem("audit_session_id",P)),P}static clearLogs(){localStorage.removeItem(Jt)}}function na(){const pe=dt(),P=c([]),G=c(!1),_e=c([]),Z=c([]),H=qe({totalOperations:0,todayOperations:0,successRate:0,mostActiveUser:null,mostCommonOperation:null}),te=ce(()=>P.value.slice(-50).reverse()),ae=ce(()=>{const R=new Date().toDateString();return P.value.filter(w=>new Date(w.timestamp).toDateString()===R)}),E=ce(()=>pe.hasPermission("audit.view")||pe.canAccessDeviceManagement),j=ce(()=>pe.hasPermission("permission.manage")||pe.canAccessDeviceManagement),Q=async R=>{try{const w={operationType:R.type,operator:{id:pe.userInfo.id,username:pe.userInfo.username,role:pe.userInfo.role_name,permissionLevel:pe.userInfo.permission_level},targets:R.targets,permissions:R.permissions,details:R.details||{},result:"pending"};return xt.addLog(w),_e.value.push(w),w}catch(w){throw console.error("记录操作失败:",w),f.error(`操作记录失败: ${w.message}`),w}},le=(R,w,r={})=>{const V=_e.value.find(C=>C.id===R);if(V){V.result=w,V.completedAt=new Date().toISOString(),V.completionDetails=r;const C=xt.getLogs(),T=C.findIndex(D=>D.id===R);T!==-1&&(C[T]=V,xt.saveLogs(C));const U=_e.value.findIndex(D=>D.id===R);U!==-1&&_e.value.splice(U,1),Z.value.unshift(V),oe()}},F=()=>{try{G.value=!0,P.value=xt.getLogs(),oe()}catch(R){console.error("加载审计日志失败:",R),f.error("加载审计日志失败")}finally{G.value=!1}},ne=async()=>{try{await Re.confirm("确定要清除所有审计日志吗？此操作不可恢复。","确认清除",{confirmButtonText:"确定清除",cancelButtonText:"取消",type:"warning"}),xt.clearLogs(),P.value=[],Z.value=[],_e.value=[],oe(),f.success("审计日志已清除")}catch(R){R!=="cancel"&&(console.error("清除审计日志失败:",R),f.error("清除审计日志失败"))}},oe=()=>{const R=P.value;H.totalOperations=R.length,H.todayOperations=ae.value.length;const w=R.filter(C=>C.result==="success").length;H.successRate=R.length>0?(w/R.length*100).toFixed(2):0;const r={};R.forEach(C=>{var U;const T=(U=C.operator)==null?void 0:U.username;T&&(r[T]=(r[T]||0)+1)}),H.mostActiveUser=Object.keys(r).reduce((C,T)=>r[C]>r[T]?C:T,null);const V={};R.forEach(C=>{const T=C.operationType;T&&(V[T]=(V[T]||0)+1)}),H.mostCommonOperation=Object.keys(V).reduce((C,T)=>V[C]>V[T]?C:T,null)};return{auditLogs:P,loading:G,pendingOperations:_e,operationHistory:Z,statistics:H,recentLogs:te,todayLogs:ae,canViewAuditLogs:E,canManagePermissions:j,recordOperation:Q,completeOperation:le,loadAuditLogs:F,clearAuditLogs:ne,OPERATION_TYPES:la,PERMISSION_TYPES:oa}}const Wt={ca_lock:{label:"CA锁",color:"danger",icon:"🔐",description:"CA证书锁、银行U盾、数字证书设备"},encryption_key:{label:"加密锁",color:"warning",icon:"🔑",description:"软件加密锁、硬件加密锁设备"},bank_ukey:{label:"银行U盾",color:"primary",icon:"🏦",description:"银行数字证书U盾设备"},financial_lock:{label:"财务锁",color:"warning",icon:"💰",description:"用友、金蝶等财务软件加密锁"},cost_lock:{label:"造价加密锁",color:"primary",icon:"🏗️",description:"广联达、新点等造价软件加密锁"},other_lock:{label:"其他加密锁",color:"danger",icon:"🔒",description:"其他类型的加密锁设备"},video_device:{label:"视频设备",color:"success",icon:"📹",description:"摄像头、视频采集设备"},audio_device:{label:"音频设备",color:"info",icon:"🎤",description:"麦克风、音频设备"},storage:{label:"存储设备",color:"warning",icon:"💾",description:"U盘、移动硬盘等存储设备"},input:{label:"输入设备",color:"",icon:"⌨️",description:"键盘、鼠标等输入设备"},communication:{label:"通信设备",color:"",icon:"📡",description:"网络适配器等通信设备"},hardware:{label:"硬件设备",color:"",icon:"🔧",description:"其他硬件设备"},unknown:{label:"未知设备",color:"",icon:"❓",description:"未识别的设备类型"}};function ds(pe,P=null){return P&&P.identification_source==="usb_ids_enhanced"?"encryption_key":{ca_lock:"ca_lock",encryption_key:"encryption_key",bank_ukey:"bank_ukey",financial_lock:"financial_lock",cost_lock:"cost_lock",other_lock:"other_lock",video_device:"video_device",audio_device:"audio_device",storage:"storage",input:"input",communication:"communication",hardware:"hardware",unknown:"unknown"}[pe]||"unknown"}function cs(pe){return pe.final_device_type?ds(pe.final_device_type,pe):ds(pe.device_type||"unknown",pe)}const aa={class:"permission-assignment"},ia={class:"toolbar"},ra={class:"toolbar-left"},ua={class:"toolbar-right"},da={key:0,class:"current-selection"},ca={class:"selection-info"},pa={class:"selection-text"},_a={key:0,class:"selected-targets"},va={class:"target-count"},ma={key:1,class:"selected-devices"},fa={class:"device-count"},ga={class:"selection-actions"},ya={class:"main-content"},ha={class:"org-panel"},ba={class:"card-header"},ka={class:"header-controls"},wa={key:0,class:"batch-controls"},$a={class:"batch-selection-info"},Ca={key:0},Ta=["onDblclick"],Sa={class:"node-info"},xa={class:"node-label"},Va={key:0},Da={key:1},Aa={key:2},za={key:3},Ea={key:4,class:"org-type"},Ua={key:5,class:"user-role"},Pa={class:"device-panel"},La={class:"device-tabs"},Ia={class:"tab-label-wrapper"},ja={class:"tab-content"},Oa={class:"search-bar"},Ma={class:"device-list"},Ba=["onClick"],Ra={class:"device-info"},Na={class:"device-name"},Ga={class:"device-desc"},Fa={class:"device-meta"},Ha={class:"device-count"},qa={class:"tab-label-wrapper"},Ka={class:"tab-content"},Ya={class:"device-filters"},Ja={class:"server-option"},Wa={class:"server-name"},Za={class:"device-count"},Qa={class:"search-bar"},Xa={class:"device-list"},ei=["onClick"],ti={class:"device-info"},si={class:"device-name"},li={class:"device-desc"},oi={class:"device-meta"},ni={class:"device-id"},ai={class:"assign-confirm"},ii={__name:"PermissionAssignment",emits:["stats-update"],setup(pe,{expose:P,emit:G}){const _e=G,Z=dt(),H=re,{organizationTreeData:te,organizationsResponse:ae,selectedNode:E,loadOrganizationsWithUsers:j,refreshData:Q,getLevelName:le,computedTreeData:F}=tl({autoLoad:!0,enableCache:!0});na();const ne=c(!1),oe=c(!1),R=c(!1),w=c(!1),r=c(!1),V=c(!1),C=c(!1),T=c(!1),U=c("single"),D=c("group"),z=c([]),K=qe({permission_types:["use"],inherit_to_children:!0,inherit_to_users:!0,expires_at:null}),ve=c(),me=c(0),ue=c("groups"),Ae=c([]),X=c([]),ee=c([]),xe=c(""),Te=c(""),we=qe({name:"",type:"",server:"",group:""}),Ke=c([]),Ee=c(!1),be=c([]),Ne=c([]),Ye=l=>!l||!Array.isArray(l)?[]:l.filter(o=>o.name!=="新注册用户").map(o=>{const h={...o};return o.children&&o.children.length>0&&(h.children=Ye(o.children)),h}),Ue=ce(()=>{let l=F.value||[];return Array.isArray(l)&&l.length>0&&l.forEach((h,i)=>{h.children&&Array.isArray(h.children)&&h.children.forEach((O,M)=>{(O.type==="admin_group"||O.type==="normal_user_group")&&O.children&&O.children.length>0&&O.children.forEach(ye=>{!ye.name&&ye.username&&(ye.name=ye.username),!ye.full_name&&ye.name&&(ye.full_name=ye.name)})})}),Array.isArray(l)||(l=l?[l]:[]),Ye(l)}),Me=ce(()=>{const l=[],o=(h,i=0)=>{Array.isArray(h)&&h.forEach(O=>{i<=2&&O.type==="organization"&&(l.push(O.id),O.children&&O.children.length>0&&o(O.children,i+1))})};return o(Ue.value),l}),Xe={children:"children",label:"name"},Ge=ce(()=>z.value.reduce((l,o)=>l+(o.userCount||0),0)),Je=ce(()=>z.length>0),$=ce(()=>D.value==="group"?ee.value.some(l=>l.type==="group"):ee.value.some(l=>l.type==="device"||l.type==="server"));ce(()=>U.value==="single"?E.value?{name:E.value.name,type:E.value.type==="organization"?le(E.value.level):"用户",count:1}:null:z.value.length>0?{name:`${z.value.length} 个组织`,type:"批量选择",count:z.value.length}:null);const g=ce(()=>xe.value?Ae.value.filter(l=>l.name.toLowerCase().includes(xe.value.toLowerCase())||l.description&&l.description.toLowerCase().includes(xe.value.toLowerCase())):Ae.value),W=ce(()=>{let l=Ne.value.length>0?Ne.value:X.value;return Te.value&&(l=l.filter(o=>(o.name||o.device_name||"").toLowerCase().includes(Te.value.toLowerCase()))),l}),I=ce(()=>{if(!we.server)return Wt;const l=X.value.filter(h=>h.slave_server_id===we.server),o={};return l.forEach(h=>{const i=cs(h);i&&Wt[i]&&(o[i]=Wt[i])}),o}),ke=l=>{switch(l){case"server":return"success";case"mixed":return"warning";case"single":return"info";default:return""}},fe=l=>{switch(l){case"server":return"服务器分组";case"mixed":return"混合分组";case"single":return"单设备分组";default:return"未知类型"}},ge=()=>{U.value==="single"?E.value=null:(z.value=[],ve.value&&ve.value.setCheckedKeys([])),ee.value=[],f.info("已清除所有选择")},y=l=>{E.value=null,z.value=[],ee.value=[],me.value++,f.info(`已切换到${l==="single"?"单选":"多选"}模式`)},v=()=>{if(!ve.value)return;const l=[],o=i=>{i.forEach(O=>{O.type==="organization"&&l.push(O),O.children&&o(O.children)})};o(Ue.value);const h=l.map(i=>i.id);ve.value.setCheckedKeys(h),z.value=[...l],f.success(`已选择所有 ${l.length} 个组织`)},he=()=>{ve.value&&(ve.value.setCheckedKeys([]),z.value=[],f.info("已清空所有选择"))},de=()=>{if(!ve.value)return;const l=[],o=M=>{M.forEach(ye=>{ye.type==="organization"&&l.push(ye),ye.children&&o(ye.children)})};o(Ue.value);const h=z.value.map(M=>M.id),i=l.filter(M=>!h.includes(M.id)),O=i.map(M=>M.id);ve.value.setCheckedKeys(O),z.value=[...i],f.info(`反选完成，当前选择 ${i.length} 个组织`)},Se=l=>{const o=(M,ye)=>{Array.isArray(M)||(M=[M]);for(const Le of M){if(Le.id===ye)return Le;if(Le.children&&Array.isArray(Le.children)){const nt=o(Le.children,ye);if(nt)return nt}}return null},h=M=>{let ye=0;return M.users&&Array.isArray(M.users)&&(ye+=M.users.length),M.children&&Array.isArray(M.children)&&M.children.forEach(Le=>{const nt=h(Le);ye+=nt}),ye},i=o(ae.value||[],l);return i?h(i):0},Ie=(l,o)=>{var i;if(["organization","admin_group","normal_user_group"].includes(o.type)&&(console.log(`🖱️ 双击节点: ${o.name} (类型: ${o.type})`),ve.value)){const O=o.id;((i=ve.value.store.nodesMap[O])==null?void 0:i.expanded)?(ve.value.store.nodesMap[O].collapse(),console.log(`📁 收起节点: ${o.name}`)):(ve.value.store.nodesMap[O].expand(),console.log(`📂 展开节点: ${o.name}`))}},et=()=>{V.value=!V.value,console.log("🔧 PermissionAssignment - 切换展开状态:",V.value),Bt(()=>{ve.value?V.value?(console.log("🔧 PermissionAssignment - 展开所有节点"),ve.value.expandAll()):(console.log("🔧 PermissionAssignment - 收起所有节点"),ve.value.collapseAll()):console.warn("⚠️ PermissionAssignment - orgTreeRef 未找到")})},ze=l=>{ee.value=[]},Ve=()=>{},Pe=()=>{},A=()=>{let l=X.value;we.name&&(l=l.filter(o=>(o.name||o.device_name||"").toLowerCase().includes(we.name.toLowerCase()))),we.type&&(l=l.filter(o=>cs(o)===we.type)),we.server&&(l=l.filter(o=>o.slave_server_id===we.server)),Ne.value=l,console.log(`🔍 设备筛选完成: ${l.length}/${X.value.length} 个设备`)},We=async l=>{if(console.log(`🖥️ 分布式节点筛选变化: ${l||"全部节点"}`),ee.value=[],we.type="",l?await tt(l):await Qe(),A(),l){const o=Ke.value.find(h=>h.id===l);o&&f.info(`已切换到 ${o.name}，设备类型筛选已重置`)}},tt=async l=>{try{r.value=!0;const o=await fetch(`/api/v1/slave/${l}/devices`,{headers:{Authorization:`Bearer ${Z.token}`,"Content-Type":"application/json"}});if(!o.ok)throw new Error(`HTTP ${o.status}: ${o.statusText}`);const h=await o.json();console.log(`📱 获取分布式节点 ${l} 的设备数据:`,h);let i=h.devices||h||[];Array.isArray(i)||(console.warn("⚠️ 设备数据不是数组格式:",typeof i,i),i=i?[i]:[]),console.log("📱 处理后的设备列表:",i);const O=i.map(M=>{var ye;return{id:M.id,name:M.custom_name||M.device_name,status:rt(M.status),vid:M.vendor_id,pid:M.product_id,description:M.description||M.device_notes||"无描述",device_type:M.device_type,slave_server_id:l,slave_server_name:((ye=M.slave_server)==null?void 0:ye.name)||"Unknown Server",physical_port:M.physical_port,is_shared:M.is_shared,is_virtual:M.is_virtual}});X.value=O,console.log(`✅ 分布式节点设备加载完成: ${O.length} 个设备`)}catch(o){console.error(`❌ 加载分布式节点 ${l} 设备失败:`,o),f.error(`加载分布式节点设备失败: ${o.message}`),X.value=[]}finally{r.value=!1}},Ze=async()=>{try{oe.value=!0;const l=U.value==="single"?[E.value]:z.value;console.log("🔧 PermissionAssignment - 开始权限分配:",{模式:U.value,目标数量:l.length,设备数量:ee.value.length,目标列表:l.map(h=>h.name)}),await new Promise(h=>setTimeout(h,1e3));const o=U.value==="single"?`成功为 ${l[0].name} 分配 ${ee.value.length} 个设备权限`:`成功为 ${l.length} 个组织批量分配 ${ee.value.length} 个设备权限`;f.success(o),C.value=!1,ee.value=[],ot()}catch(l){console.error("权限分配失败:",l),f.error("权限分配失败")}finally{oe.value=!1}},it=()=>{C.value=!1},De=async()=>{try{if(oe.value=!0,z.value.length===0){f.warning("请先选择要分配权限的组织或用户");return}if(ee.value.length===0){f.warning("请先选择要分配的设备或分组");return}const l={assignment_mode:U.value,authorization_type:D.value,target_organizations:z.value.filter(i=>i.type==="organization").map(i=>i.id),target_users:z.value.filter(i=>i.type==="user").map(i=>i.id),device_groups:ee.value.filter(i=>i.type==="group").map(i=>i.id),devices:ee.value.filter(i=>i.type==="device").map(i=>i.id),slave_servers:ee.value.filter(i=>i.type==="server").map(i=>i.id),permission_types:K.permission_types,inherit_to_children:K.inherit_to_children,inherit_to_users:K.inherit_to_users,expires_at:K.expires_at},o=await fetch("/api/v1/permission-assignment/assign",{method:"POST",headers:{Authorization:`Bearer ${Z.token}`,"Content-Type":"application/json"},body:JSON.stringify(l)});if(!o.ok){const i=await o.json();throw new Error(i.detail||"权限分配失败")}const h=await o.json();f.success(h.message||"权限分配成功"),ge()}catch(l){let o="权限分配失败";l.message&&(l.message.includes("权限不足")?o="权限不足，您没有设备权限管理权限":l.message.includes("device_permission_management")?o="需要设备权限管理权限才能执行此操作":o=l.message),f.error(o)}finally{oe.value=!1}},p=async()=>{try{if(R.value=!0,z.value.length===0){f.warning("请先选择要撤销权限的组织或用户");return}await Re.confirm(`确定要撤销选中的 ${z.value.length} 个目标的设备权限吗？`,"确认撤销权限",{confirmButtonText:"确定撤销",cancelButtonText:"取消",type:"warning"});const l={assignment_mode:U.value,authorization_type:D.value,target_organizations:z.value.filter(i=>i.type==="organization").map(i=>i.id),target_users:z.value.filter(i=>i.type==="user").map(i=>i.id),device_groups:ee.value.filter(i=>i.type==="group").map(i=>i.id),devices:ee.value.filter(i=>i.type==="device").map(i=>i.id),slave_servers:ee.value.filter(i=>i.type==="server").map(i=>i.id),permission_types:[]};console.log("🔧 权限撤销请求数据:",l);const o=await fetch("/api/v1/permission-assignment/revoke",{method:"POST",headers:{Authorization:`Bearer ${Z.token}`,"Content-Type":"application/json"},body:JSON.stringify(l)});if(!o.ok){const i=await o.json();throw new Error(i.detail||"权限撤销失败")}const h=await o.json();console.log("✅ 权限撤销成功:",h),f.success(h.message||"权限撤销成功"),ge()}catch(l){l.message!=="cancel"&&(console.error("❌ 权限撤销失败:",l),f.error(l.message||"权限撤销失败"))}finally{R.value=!1}},u=l=>{const o=z.value.findIndex(h=>h.id===l.id&&h.type===l.type);o>-1&&z.value.splice(o,1)},x=l=>{const o=ee.value.findIndex(h=>h.id===l.id&&h.type===l.type);o>-1&&ee.value.splice(o,1)},N=()=>{T.value=!0},Y=(l,o)=>{if(["organization","admin_group","normal_user_group","user"].includes(l.type))if(U.value==="single")E.value=l,z.value=[l];else{const i=z.value.findIndex(O=>O.id===l.id&&O.type===l.type);i>-1?z.value.splice(i,1):z.value.push(l)}},a=(l,o)=>{z.value=o.checkedNodes.filter(h=>["organization","admin_group","normal_user_group","user"].includes(h.type))},n=l=>U.value==="single"?E.value&&E.value.id===l.id&&E.value.type===l.type:!1,S=l=>U.value==="multiple"?z.value.some(o=>o.id===l.id&&o.type===l.type):!1,q=l=>["organization","admin_group","normal_user_group"].includes(l.type)&&l.children&&l.children.length>0,ie=l=>{const o=ee.value.findIndex(h=>h.id===l);if(o>-1)ee.value.splice(o,1);else{const h=ue.value==="groups"?Ae.value.find(i=>i.id===l):X.value.find(i=>i.id===l);h&&ee.value.push({...h,type:ue.value==="groups"?"group":"device"})}},Fe=l=>({organization:"org-icon",admin_group:"admin-icon",normal_user_group:"user-icon",user:"user-icon"})[l.type]||"default-icon",Ce=l=>({online:"success",offline:"danger",busy:"warning",unknown:"info"})[l]||"info",je=async()=>{ne.value=!0;try{console.log("🔧 PermissionAssignment - 开始刷新数据"),await Promise.all([j(),$e(),Qe(),lt(),Be()]),me.value++,console.log("🔧 PermissionAssignment - 数据刷新完成，强制重新渲染，treeKey:",me.value),f.success("数据刷新成功")}catch(l){console.error("刷新数据失败:",l),f.error("刷新数据失败")}finally{ne.value=!1}},$e=async()=>{try{w.value=!0;const l=await ys();Ae.value=l.data||[]}catch(l){console.error("加载设备分组失败:",l),f.error("加载设备分组失败")}finally{w.value=!1}},Qe=async()=>{try{r.value=!0;const l=await fetch("/api/v1/devices",{headers:{Authorization:`Bearer ${Z.token}`,"Content-Type":"application/json"}});if(!l.ok)throw new Error(`HTTP ${l.status}: ${l.statusText}`);const o=await l.json();console.log("获取到的设备数据:",o);let h=o.devices||o||[];Array.isArray(h)||(console.warn("⚠️ 设备数据不是数组格式:",typeof h,h),h=h?[h]:[]),console.log("📱 处理后的设备列表:",h),X.value=h.map(i=>{var O;return{id:i.id,name:i.custom_name||i.device_name,status:rt(i.status),vid:i.vendor_id,pid:i.product_id,description:i.description||i.device_notes||"无描述",device_type:i.device_type,slave_server_name:((O=i.slave_server)==null?void 0:O.name)||"Unknown Server",physical_port:i.physical_port,is_shared:i.is_shared,is_virtual:i.is_virtual}}),console.log("处理后的设备数据:",X.value)}catch(l){console.error("加载设备列表失败:",l),f.error(`加载设备列表失败: ${l.message}`),X.value=[]}finally{r.value=!1}},lt=async()=>{try{Ee.value=!0,console.log("📡 开始加载分布式节点列表...");const l=await fetch("/api/v1/slave/list",{headers:{Authorization:`Bearer ${Z.token}`,"Content-Type":"application/json"}});if(console.log("📡 分布式节点API响应状态:",l.status),l.ok){const o=await l.json();console.log("📡 分布式节点API原始响应:",o);let h=[];o.success&&Array.isArray(o.data)?(h=o.data,console.log("📡 使用标准API响应格式，提取data字段:",h)):Array.isArray(o.servers)?(h=o.servers,console.log("📡 使用备用格式，提取servers字段:",h)):Array.isArray(o)?(h=o,console.log("📡 使用直接数组格式:",h)):(console.warn("⚠️ 未识别的API响应格式:",o),h=[]),console.log("📡 最终提取的服务器数据:",h),console.log("📡 服务器数据类型:",typeof h,"是否数组:",Array.isArray(h));const O=(Array.isArray(h)?h:[]).map(M=>{var Le,nt,Tt,St;const ye=((Le=M.usb_info)==null?void 0:Le.device_count)||(((nt=M.usb_info)==null?void 0:nt.devices)||[]).filter(ht=>!ht.is_virtual&&ht.description!=="USB Hub").length||0;return{...M,device_count:ye,status:M.is_online?"online":"offline",original_device_count:M.device_count,usb_device_count:(Tt=M.usb_info)==null?void 0:Tt.device_count,hub_count:(St=M.usb_info)==null?void 0:St.hub_count}});Ke.value=O,console.log(`📡 分布式节点列表加载完成: ${O.length} 个节点`),console.log("📡 节点详情:",O.map(M=>({id:M.id,name:M.name,device_count:M.device_count,status:M.status}))),O.length===0&&(console.warn("⚠️ 分布式节点列表为空，可能没有配置节点或节点都离线"),f.warning("当前没有可用的分布式节点"))}else{const o=await l.text();console.error("❌ 分布式节点API请求失败:",l.status,o),f.error(`加载分布式节点失败: HTTP ${l.status}`)}}catch(l){console.error("❌ 加载分布式节点列表失败:",l),f.error(`加载分布式节点列表失败: ${l.message}`)}finally{Ee.value=!1}},Be=async()=>{try{const l=await fetch("/api/v1/device-groups/",{headers:{Authorization:`Bearer ${Z.token}`,"Content-Type":"application/json"}});if(l.ok){const o=await l.json();be.value=o||[]}}catch(l){console.error("加载设备分组列表失败:",l)}},rt=l=>({online:"在线",offline:"离线",busy:"占用",available:"可用",error:"错误",maintenance:"维护中"})[l]||l||"未知",ot=()=>{var o,h;const l={total:X.value.length,users:((o=E.value)==null?void 0:o.type)==="user"?1:0,orgs:((h=E.value)==null?void 0:h.type)==="organization"?1:0};_e("stats-update",l)},yt=l=>{console.log("设备状态更新:",l);const o=X.value.findIndex(h=>h.id===l.device_id);o!==-1&&(X.value[o].status=rt(l.new_status),f.info(`设备状态更新: ${X.value[o].name} -> ${X.value[o].status}`),ot())},He=()=>{H.subscribe("device_updates"),H.on("device_status_update",yt);const l=Z.token;l&&H.connect(l)},$t=()=>{H.off("device_status_update",yt),H.unsubscribe("device_updates")};ut(()=>{He()}),Dt(()=>{$t()}),P({refreshData:je}),bt(te,(l,o)=>{console.log("🔍 PermissionAssignment - organizationTreeData 数据变化:",{新数据:l,新数据长度:(l==null?void 0:l.length)||0,旧数据长度:(o==null?void 0:o.length)||0,数据类型:typeof l,是否数组:Array.isArray(l)}),l&&l.length>0&&(me.value++,console.log("🔧 PermissionAssignment - 强制重新渲染树组件, treeKey:",me.value),Bt(()=>{Me.value&&Me.value.length>0&&console.log("🔧 PermissionAssignment - 设置默认展开节点:",Me.value)}))},{immediate:!0,deep:!0}),bt(Ue,l=>{console.log("🔍 PermissionAssignment - organizationTree computed 变化:",l)},{immediate:!0}),bt(Me,l=>{console.log("🔍 PermissionAssignment - defaultExpandedKeys 变化:",l)},{immediate:!0});const Ct=()=>{const l=o=>{console.log("🔄 PermissionAssignment - 接收到组织架构数据更新事件:",o.detail),o.detail.source!=="PermissionAssignment"&&(console.log("🔄 PermissionAssignment - 开始同步组织架构数据"),Q())};return typeof window<"u"&&(window.addEventListener("organizationDataUpdated",l),console.log("✅ PermissionAssignment - 跨模块数据同步监听器已设置")),()=>{typeof window<"u"&&(window.removeEventListener("organizationDataUpdated",l),console.log("🧹 PermissionAssignment - 跨模块数据同步监听器已清理"))}};return ut(async()=>{if(console.log("🔧 PermissionAssignment - 组件挂载完成"),console.log("🔍 PermissionAssignment - 初始 organizationTreeData:",te.value),console.log("🔍 PermissionAssignment - 初始 organizationTree:",Ue.value),console.log("🔍 PermissionAssignment - 初始 defaultExpandedKeys:",Me.value),!te.value||te.value.length===0){console.log("🔍 PermissionAssignment - 组织架构数据为空，手动加载");try{await j()}catch(o){console.error("❌ 手动加载组织架构数据失败:",o)}}try{await Promise.all([$e(),Qe(),lt(),Be()]),console.log("✅ 所有数据加载完成")}catch(o){console.error("❌ 数据加载过程中出现错误:",o)}const l=Ct();Dt(()=>{l()})}),(l,o)=>{const h=ft,i=gt,O=Ns,M=Gs,ye=Mt("Remove"),Le=kt,nt=ms,Tt=Zt,St=Js,ht=zt,ct=_s,os=es,Pt=ls,Lt=Ut,ns=Et,hs=ss,bs=ts,as=At,ks=ps,It=Kt,ws=Ws,$s=qt,is=wt;return _(),L("div",aa,[s("div",ia,[s("div",ra,[e(i,{type:"primary",onClick:je,loading:ne.value},{default:t(()=>[e(h,null,{default:t(()=>[e(k(vt))]),_:1}),o[18]||(o[18]=d(" 刷新数据 ",-1))]),_:1,__:[18]},8,["loading"]),e(M,{modelValue:U.value,"onUpdate:modelValue":o[0]||(o[0]=b=>U.value=b),class:"mode-selector"},{default:t(()=>[e(O,{label:"single"},{default:t(()=>o[19]||(o[19]=[d("单选模式",-1)])),_:1,__:[19]}),e(O,{label:"multiple"},{default:t(()=>o[20]||(o[20]=[d("多选模式",-1)])),_:1,__:[20]})]),_:1},8,["modelValue"]),e(M,{modelValue:D.value,"onUpdate:modelValue":o[1]||(o[1]=b=>D.value=b),class:"auth-type-selector"},{default:t(()=>[e(O,{label:"group"},{default:t(()=>o[21]||(o[21]=[d("分组授权",-1)])),_:1,__:[21]}),e(O,{label:"device"},{default:t(()=>o[22]||(o[22]=[d("设备授权",-1)])),_:1,__:[22]})]),_:1},8,["modelValue"])]),s("div",ua,[e(i,{type:"warning",onClick:p,disabled:!Je.value,loading:R.value},{default:t(()=>[e(h,null,{default:t(()=>[e(ye)]),_:1}),o[23]||(o[23]=d(" 撤销权限 ",-1))]),_:1,__:[23]},8,["disabled","loading"]),e(i,{type:"success",onClick:De,disabled:!Je.value||!$.value,loading:oe.value},{default:t(()=>[e(h,null,{default:t(()=>[e(k(Fs))]),_:1}),o[24]||(o[24]=d(" 分配权限 ",-1))]),_:1,__:[24]},8,["disabled","loading"])])]),Je.value?(_(),L("div",da,[s("div",ca,[e(h,{class:"selection-icon"},{default:t(()=>[e(k(Ot))]),_:1}),s("span",pa,[s("strong",null,m(U.value==="single"?"单选模式":"多选模式"),1),o[25]||(o[25]=d(" + ",-1)),s("strong",null,m(D.value==="group"?"分组授权":"设备授权"),1),z.value.length>0?(_(),L("div",_a,[(_(!0),L(Oe,null,st(z.value,b=>(_(),B(Le,{key:b.id,type:"primary",size:"small",closable:"",onClose:se=>u(b)},{default:t(()=>[d(m(b.name)+" ("+m(b.type)+") ",1)]),_:2},1032,["onClose"]))),128)),s("span",va,"共"+m(Ge.value)+"人",1)])):J("",!0),ee.value.length>0?(_(),L("div",ma,[(_(!0),L(Oe,null,st(ee.value,b=>(_(),B(Le,{key:b.id,type:"success",size:"small",closable:"",onClose:se=>x(b)},{default:t(()=>[d(m(b.name)+" ("+m(b.type)+") ",1)]),_:2},1032,["onClose"]))),128)),s("span",fa,"共"+m(ee.value.length)+"项",1)])):J("",!0)])]),s("div",ga,[e(i,{size:"small",onClick:ge},{default:t(()=>o[26]||(o[26]=[d("清除选择",-1)])),_:1,__:[26]}),e(i,{size:"small",type:"primary",onClick:N},{default:t(()=>o[27]||(o[27]=[d("权限设置",-1)])),_:1,__:[27]})])])):J("",!0),s("div",ya,[s("div",ha,[e(Tt,null,{header:t(()=>[s("div",ba,[o[30]||(o[30]=s("span",null,"组织架构",-1)),s("div",ka,[e(M,{modelValue:U.value,"onUpdate:modelValue":o[2]||(o[2]=b=>U.value=b),size:"small",onChange:y},{default:t(()=>[e(O,{value:"single"},{default:t(()=>o[28]||(o[28]=[d("单选",-1)])),_:1,__:[28]}),e(O,{value:"multiple"},{default:t(()=>o[29]||(o[29]=[d("多选",-1)])),_:1,__:[29]})]),_:1},8,["modelValue"]),e(i,{size:"small",onClick:et},{default:t(()=>[d(m(V.value?"收起全部":"展开全部"),1)]),_:1})])])]),default:t(()=>[U.value==="multiple"?(_(),L("div",wa,[e(i,{size:"small",onClick:v},{default:t(()=>o[31]||(o[31]=[d("全选",-1)])),_:1,__:[31]}),e(i,{size:"small",onClick:he},{default:t(()=>o[32]||(o[32]=[d("全不选",-1)])),_:1,__:[32]}),e(i,{size:"small",onClick:de},{default:t(()=>o[33]||(o[33]=[d("反选",-1)])),_:1,__:[33]}),s("span",$a,[d(" 已选择 "+m(z.value.length)+" 个组织 ",1),Ge.value>0?(_(),L("span",Ca,"，共 "+m(Ge.value)+" 人",1)):J("",!0)])])):J("",!0),(_(),B(nt,{ref_key:"orgTreeRef",ref:ve,data:Ue.value,props:Xe,"node-key":"id","default-expanded-keys":Me.value,"expand-on-click-node":!1,onNodeClick:Y,"highlight-current":U.value==="single","show-checkbox":U.value==="multiple","check-strictly":!0,onCheck:a,key:me.value,class:"org-tree","empty-text":"暂无组织架构数据"},{default:t(({node:b,data:se})=>[s("div",{class:Vt(["tree-node",{"is-selected":n(se),"is-multi-selected":S(se),"is-expandable":q(se)}]),onDblclick:Ii=>Ie(b,se)},[s("div",Sa,[e(h,{class:Vt(["node-icon",Fe(se)])},{default:t(()=>[se.type==="organization"?(_(),B(k(Hs),{key:0})):se.type==="admin_group"?(_(),B(k(qs),{key:1})):se.type==="normal_user_group"?(_(),B(k(Ot),{key:2})):(_(),B(k(Ks),{key:3}))]),_:2},1032,["class"]),s("span",xa,[d(m(se.type==="user"&&se.full_name||se.name)+" ",1),se.type==="organization"&&se.id?(_(),L("span",Va," ("+m(Se(se.id))+"人) ",1)):se.type==="admin_group"||se.type==="normal_user_group"?(_(),L("span",Da," ("+m(se.children&&se.children.length||se.userCount||0)+"人) ",1)):se.userCount!==void 0?(_(),L("span",Aa," ("+m(se.userCount)+"人) ",1)):se.users&&se.users.length>0?(_(),L("span",za," ("+m(se.users.length)+"人) ",1)):J("",!0),se.type==="organization"?(_(),L("span",Ea," ("+m(k(le)(se.level))+") ",1)):se.type==="user"?(_(),L("span",Ua," - "+m(se.role_name),1)):J("",!0)]),q(se)?(_(),B(h,{key:0,class:"expand-hint",title:"双击展开/收缩"},{default:t(()=>[e(k(Ys))]),_:1})):J("",!0)])],42,Ta)]),_:1},8,["data","default-expanded-keys","highlight-current","show-checkbox"]))]),_:1})]),s("div",Pa,[e(Tt,null,{header:t(()=>o[34]||(o[34]=[s("div",{class:"card-header"},[s("span",null,"设备权限分配")],-1)])),default:t(()=>[s("div",La,[e(bs,{modelValue:ue.value,"onUpdate:modelValue":o[8]||(o[8]=b=>ue.value=b),onTabChange:ze,class:"device-permission-tabs"},{default:t(()=>[e(os,{name:"groups"},{label:t(()=>[s("div",Ia,[e(h,null,{default:t(()=>[e(k(Rt))]),_:1}),o[35]||(o[35]=s("span",null,"分组",-1)),e(St,{value:g.value.length,max:99,class:"tab-badge"},null,8,["value"])])]),default:t(()=>[s("div",ja,[s("div",Oa,[e(ht,{modelValue:xe.value,"onUpdate:modelValue":o[3]||(o[3]=b=>xe.value=b),placeholder:"搜索设备分组...","prefix-icon":k(pt),clearable:"",onInput:Ve},null,8,["modelValue","prefix-icon"])]),at((_(),L("div",Ma,[(_(!0),L(Oe,null,st(g.value,b=>(_(),L("div",{key:b.id,class:Vt(["device-item",{"is-selected":ee.value.includes(b.id)}]),onClick:se=>ie(b.id)},[e(ct,{"model-value":ee.value.includes(b.id),onChange:se=>ie(b.id)},null,8,["model-value","onChange"]),s("div",Ra,[s("div",Na,m(b.name),1),s("div",Ga,m(b.description||"无描述"),1),s("div",Fa,[e(Le,{type:ke(b.group_type),size:"small"},{default:t(()=>[d(m(fe(b.group_type)),1)]),_:2},1032,["type"]),s("span",Ha,m(b.device_count||0)+"个设备",1)])])],10,Ba))),128))])),[[is,w.value]])])]),_:1}),e(os,{name:"devices"},{label:t(()=>[s("div",qa,[e(h,null,{default:t(()=>[e(k(_t))]),_:1}),o[36]||(o[36]=s("span",null,"设备",-1)),e(St,{value:W.value.length,max:99,class:"tab-badge"},null,8,["value"])])]),default:t(()=>[s("div",Ka,[s("div",Ya,[e(hs,{gutter:12},{default:t(()=>[e(Pt,{span:7},{default:t(()=>[e(ht,{modelValue:we.name,"onUpdate:modelValue":o[4]||(o[4]=b=>we.name=b),placeholder:"设备名称搜索",clearable:"",onInput:A},{prefix:t(()=>[e(h,null,{default:t(()=>[e(k(pt))]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(Pt,{span:5},{default:t(()=>[e(ns,{modelValue:we.type,"onUpdate:modelValue":o[5]||(o[5]=b=>we.type=b),placeholder:"设备类型",clearable:"",onChange:A,disabled:!we.server},{default:t(()=>[e(Lt,{label:"全部类型",value:""}),(_(!0),L(Oe,null,st(I.value,(b,se)=>(_(),B(Lt,{key:se,label:b.label,value:se},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),e(Pt,{span:7},{default:t(()=>[e(ns,{modelValue:we.server,"onUpdate:modelValue":o[6]||(o[6]=b=>we.server=b),placeholder:"分布式节点",clearable:"",onChange:We,loading:Ee.value},{default:t(()=>[e(Lt,{label:"全部节点",value:""}),(_(!0),L(Oe,null,st(Ke.value,b=>(_(),B(Lt,{key:b.id,label:`${b.name} (${b.device_count||0}个设备)`,value:b.id},{default:t(()=>[s("div",Ja,[s("span",Wa,m(b.name),1),e(Le,{type:b.status==="online"?"success":"danger",size:"small"},{default:t(()=>[d(m(b.status==="online"?"在线":"离线"),1)]),_:2},1032,["type"]),s("span",Za,m(b.device_count||0)+"个设备",1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1}),e(Pt,{span:5},{default:t(()=>[e(i,{type:"primary",onClick:A},{default:t(()=>[e(h,null,{default:t(()=>[e(k(pt))]),_:1}),o[37]||(o[37]=d(" 搜索 ",-1))]),_:1,__:[37]})]),_:1})]),_:1})]),s("div",Qa,[e(ht,{modelValue:Te.value,"onUpdate:modelValue":o[7]||(o[7]=b=>Te.value=b),placeholder:"搜索USB设备...","prefix-icon":k(pt),clearable:"",onInput:Pe},null,8,["modelValue","prefix-icon"])]),at((_(),L("div",Xa,[(_(!0),L(Oe,null,st(W.value,b=>(_(),L("div",{key:b.id,class:Vt(["device-item",{"is-selected":ee.value.includes(b.id)}]),onClick:se=>ie(b.id)},[e(ct,{"model-value":ee.value.includes(b.id),onChange:se=>ie(b.id)},null,8,["model-value","onChange"]),s("div",ti,[s("div",si,m(b.name||b.device_name),1),s("div",li,m(b.description||"无描述"),1),s("div",oi,[e(Le,{type:Ce(b.status),size:"small"},{default:t(()=>[d(m(b.status),1)]),_:2},1032,["type"]),s("span",ni,"VID:"+m(b.vid)+" PID:"+m(b.pid),1)])])],10,ei))),128))])),[[is,r.value]])])]),_:1})]),_:1},8,["modelValue"])])]),_:1})])]),e(as,{modelValue:C.value,"onUpdate:modelValue":o[10]||(o[10]=b=>C.value=b),title:"确认权限分配",width:"500px",onClose:it},{footer:t(()=>[e(i,{onClick:o[9]||(o[9]=b=>C.value=!1)},{default:t(()=>o[42]||(o[42]=[d("取消",-1)])),_:1,__:[42]}),e(i,{type:"primary",onClick:Ze,loading:oe.value},{default:t(()=>o[43]||(o[43]=[d(" 确认分配 ",-1)])),_:1,__:[43]},8,["loading"])]),default:t(()=>{var b;return[s("div",ai,[s("p",null,[o[38]||(o[38]=s("strong",null,"分配对象：",-1)),d(m((b=k(E))==null?void 0:b.name),1)]),s("p",null,[o[39]||(o[39]=s("strong",null,"分配类型：",-1)),d(m(ue.value==="groups"?"设备分组":"独立设备"),1)]),s("p",null,[o[40]||(o[40]=s("strong",null,"选中数量：",-1)),d(m(ee.value.length)+"个",1)]),o[41]||(o[41]=s("p",null,[s("strong",null,"权限类型："),d("使用权限")],-1))])]}),_:1},8,["modelValue"]),e(as,{modelValue:T.value,"onUpdate:modelValue":o[17]||(o[17]=b=>T.value=b),title:"权限设置",width:"600px"},{footer:t(()=>[e(i,{onClick:o[15]||(o[15]=b=>T.value=!1)},{default:t(()=>o[53]||(o[53]=[d("取消",-1)])),_:1,__:[53]}),e(i,{type:"primary",onClick:o[16]||(o[16]=b=>T.value=!1)},{default:t(()=>o[54]||(o[54]=[d(" 确定 ",-1)])),_:1,__:[54]})]),default:t(()=>[e($s,{model:K,"label-width":"120px"},{default:t(()=>[e(It,{label:"权限类型"},{default:t(()=>[e(ks,{modelValue:K.permission_types,"onUpdate:modelValue":o[11]||(o[11]=b=>K.permission_types=b)},{default:t(()=>[e(ct,{label:"view"},{default:t(()=>o[44]||(o[44]=[d("查看权限",-1)])),_:1,__:[44]}),e(ct,{label:"use"},{default:t(()=>o[45]||(o[45]=[d("使用权限",-1)])),_:1,__:[45]}),e(ct,{label:"manage"},{default:t(()=>o[46]||(o[46]=[d("管理权限",-1)])),_:1,__:[46]})]),_:1},8,["modelValue"]),o[47]||(o[47]=s("div",{class:"form-help"},"选择要分配的权限类型",-1))]),_:1,__:[47]}),e(It,{label:"权限继承"},{default:t(()=>[e(ct,{modelValue:K.inherit_to_children,"onUpdate:modelValue":o[12]||(o[12]=b=>K.inherit_to_children=b)},{default:t(()=>o[48]||(o[48]=[d(" 向下继承到子组织 ",-1)])),_:1,__:[48]},8,["modelValue"]),o[49]||(o[49]=s("div",{class:"form-help"},"选中后，权限将自动分配给所有子组织",-1))]),_:1,__:[49]}),e(It,{label:"用户继承"},{default:t(()=>[e(ct,{modelValue:K.inherit_to_users,"onUpdate:modelValue":o[13]||(o[13]=b=>K.inherit_to_users=b)},{default:t(()=>o[50]||(o[50]=[d(" 继承到组织内用户 ",-1)])),_:1,__:[50]},8,["modelValue"]),o[51]||(o[51]=s("div",{class:"form-help"},"选中后，权限将自动分配给组织内的所有用户",-1))]),_:1,__:[51]}),e(It,{label:"过期时间"},{default:t(()=>[e(ws,{modelValue:K.expires_at,"onUpdate:modelValue":o[14]||(o[14]=b=>K.expires_at=b),type:"datetime",placeholder:"选择过期时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",clearable:""},null,8,["modelValue"]),o[52]||(o[52]=s("div",{class:"form-help"},"留空表示永不过期",-1))]),_:1,__:[52]})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},ri=mt(ii,[["__scopeId","data-v-bf47e067"]]),ui={class:"device-center"},di={class:"statistics-panel"},ci={class:"stat-content"},pi={class:"stat-icon server-icon"},_i={class:"stat-info"},vi={class:"stat-value"},mi={class:"stat-detail"},fi={class:"stat-content"},gi={class:"stat-icon device-icon"},yi={class:"stat-info"},hi={class:"stat-value"},bi={class:"stat-detail"},ki={class:"stat-content"},wi={class:"stat-icon group-icon"},$i={class:"stat-info"},Ci={class:"stat-value"},Ti={class:"stat-detail"},Si={class:"stat-content"},xi={class:"stat-icon permission-icon"},Vi={class:"stat-info"},Di={class:"stat-value"},Ai={class:"stat-detail"},zi={class:"toolbar"},Ei={class:"toolbar-right"},Ui={class:"tab-label"},Pi={key:1,class:"no-permission"},Li={__name:"index",setup(pe){const{loading:P,statistics:G,visibleTabs:_e,refreshAllData:Z}=sl(),H=dt(),te=c("");return ut(()=>{console.log("DeviceCenter 组件已挂载"),_e.value.length>0&&(te.value=_e.value[0].name)}),(ae,E)=>{const j=ft,Q=Zt,le=ls,F=ss,ne=gt,oe=es,R=ts,w=vs;return _(),L("div",ui,[s("div",di,[e(F,{gutter:20},{default:t(()=>[k(H).canViewSlaveServerStats?(_(),B(le,{key:0,span:6},{default:t(()=>[e(Q,{class:"stat-card"},{default:t(()=>[s("div",ci,[s("div",pi,[e(j,null,{default:t(()=>[e(k(_t))]),_:1})]),s("div",_i,[E[2]||(E[2]=s("div",{class:"stat-title"},"分布式节点",-1)),s("div",vi,m(k(G).slaveServers.total),1),s("div",mi," 在线: "+m(k(G).slaveServers.online)+" | 离线: "+m(k(G).slaveServers.offline),1)])])]),_:1})]),_:1})):J("",!0),k(H).canViewUSBDeviceStats?(_(),B(le,{key:1,span:6},{default:t(()=>[e(Q,{class:"stat-card"},{default:t(()=>[s("div",fi,[s("div",gi,[e(j,null,{default:t(()=>[e(k(_t))]),_:1})]),s("div",yi,[E[3]||(E[3]=s("div",{class:"stat-title"},"USB设备管理中心",-1)),s("div",hi,m(k(G).devices.total),1),s("div",bi," 可用: "+m(k(G).devices.available)+" | 已连接: "+m(k(G).devices.connected),1)])])]),_:1})]),_:1})):J("",!0),k(H).canViewDeviceGroupStats?(_(),B(le,{key:2,span:6},{default:t(()=>[e(Q,{class:"stat-card"},{default:t(()=>[s("div",ki,[s("div",wi,[e(j,null,{default:t(()=>[e(k(Rt))]),_:1})]),s("div",$i,[E[4]||(E[4]=s("div",{class:"stat-title"},"资源调度分组",-1)),s("div",Ci,m(k(G).groups.total),1),s("div",Ti," 有设备: "+m(k(G).groups.withDevices),1)])])]),_:1})]),_:1})):J("",!0),k(H).canViewPermissionAssignmentStats?(_(),B(le,{key:3,span:6},{default:t(()=>[e(Q,{class:"stat-card"},{default:t(()=>[s("div",Si,[s("div",xi,[e(j,null,{default:t(()=>[e(k(Zs))]),_:1})]),s("div",Vi,[E[5]||(E[5]=s("div",{class:"stat-title"},"授权范围管理",-1)),s("div",Di,m(k(G).permissions.totalAssignments),1),s("div",Ai," 活跃用户: "+m(k(G).permissions.activeUsers),1)])])]),_:1})]),_:1})):J("",!0)]),_:1})]),s("div",zi,[E[7]||(E[7]=s("div",{class:"toolbar-left"},[s("h2",null,"USB设备管理中心")],-1)),s("div",Ei,[e(ne,{type:"primary",onClick:k(Z),loading:k(P)},{default:t(()=>[e(j,null,{default:t(()=>[e(k(vt))]),_:1}),E[6]||(E[6]=d(" 刷新数据 ",-1))]),_:1,__:[6]},8,["onClick","loading"])])]),e(Q,{class:"main-content"},{default:t(()=>[k(_e).length>0?(_(),B(R,{key:0,modelValue:te.value,"onUpdate:modelValue":E[0]||(E[0]=r=>te.value=r),type:"border-card"},{default:t(()=>[(_(!0),L(Oe,null,st(k(_e),r=>(_(),B(oe,{key:r.name,name:r.name,label:r.label},{label:t(()=>[s("span",Ui,[e(j,null,{default:t(()=>[(_(),B(Qs(r.icon)))]),_:2},1024),d(" "+m(r.label),1)])]),default:t(()=>[r.name==="devices"?(_(),B(Fo,{key:0})):J("",!0),r.name==="slaves"?(_(),B(rn,{key:1})):J("",!0),r.name==="groups"?(_(),B(ta,{key:2})):J("",!0),r.name==="permissions"?(_(),B(ri,{key:3})):J("",!0)]),_:2},1032,["name","label"]))),128))]),_:1},8,["modelValue"])):(_(),L("div",Pi,[e(w,{description:"您暂无权限访问设备管理功能"},{default:t(()=>[e(ne,{type:"primary",onClick:E[1]||(E[1]=r=>ae.$router.push("/dashboard"))},{default:t(()=>E[8]||(E[8]=[d(" 返回工作台 ",-1)])),_:1,__:[8]})]),_:1})]))]),_:1})])}}},hr=mt(Li,[["__scopeId","data-v-9f168561"]]);export{hr as default};
