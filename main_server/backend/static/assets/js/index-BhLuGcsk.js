import{_ as Ee}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                   *//* empty css                             *//* empty css                   *//* empty css                     *//* empty css                          *//* empty css                    *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  */import{ds as L,r as w,a as H,aN as M,o as Pe,bM as xe,c as P,d as u,e as i,w as a,p as N,cR as Re,m as he,a9 as Se,aa as Ne,y as v,x as Ue,s as f,ac as De,i as d,n as r,af as Ie,z as g,a7 as Oe,t as k,cW as ze,cV as Be,cX as Le,f as Fe,j as Te,k as $e,ad as Ae,ae as je,cN as Je,cO as Me,a5 as K,a6 as Q,X as qe,Y as Ge,Z as Xe,_ as ee,L as se,$ as Ze,a0 as We,R as Ye,a2 as q,E as He,bZ as Ke,dl as ae,al as Qe,am as es,a4 as ss}from"./index-msvS5Uas.js";function as(){return L.get("/api/v2/roles/")}function ns(x){return L.post("/api/v2/roles/",x)}function is(x,U){return L.put(`/api/v2/roles/${x}`,U)}function os(x){return L.delete(`/api/v2/roles/${x}`)}const ts={class:"role-management"},ls={class:"page-header"},rs={class:"header-actions"},ds={class:"role-list"},cs={class:"role-name"},ms={class:"navigation-permissions"},_s={class:"nav-permission-content"},us={key:0,class:"sub-permissions"},vs={key:1,class:"permission-actions"},ps={key:0,class:"change-indicator"},gs={class:"dialog-footer"},fs={key:0,class:"role-detail"},bs={__name:"index",setup(x){const U=w(!1),F=w(!1),R=w(!1),T=w(!1),V=w(!1),c=w(!1),p=w([]),C=w(null),I=w(),n=H({name:"",description:"",level_scope:0,permissions:[],navigation_permissions:[],device_sub_permissions:[],can_manage_users:!1,can_manage_devices:!1,can_view_reports:!1}),h=H({navigation_permissions:[],device_sub_permissions:[]}),G=M({get(){const s=[];return n.can_manage_users&&s.push("can_manage_users"),n.can_manage_devices&&s.push("can_manage_devices"),n.can_view_reports&&s.push("can_view_reports"),s},set(s){n.can_manage_users=s.includes("can_manage_users"),n.can_manage_devices=s.includes("can_manage_devices"),n.can_view_reports=s.includes("can_view_reports")}}),m=M(()=>n.name==="全域管理员"),$=M(()=>{if(m.value)return!1;const s=JSON.stringify(n.navigation_permissions.sort())!==JSON.stringify(h.navigation_permissions.sort()),e=JSON.stringify(n.device_sub_permissions.sort())!==JSON.stringify(h.device_sub_permissions.sort());return s||e}),ne={name:[{required:!0,message:"请输入角色名称",trigger:"blur"},{min:2,max:50,message:"角色名称长度在 2 到 50 个字符",trigger:"blur"}],description:[{required:!0,message:"请输入角色描述",trigger:"blur"}]},X=[{key:"dashboard",name:"工作台",icon:qe},{key:"applications",name:"处理事项",icon:Ge},{key:"org-users",name:"组织与用户管理",icon:Xe},{key:"user-registration",name:"新用户审核",icon:ee},{key:"device-center",name:"设备管理中心",icon:se},{key:"client-management",name:"设备绑定中心",icon:se},{key:"role-management",name:"角色管理",icon:ee},{key:"system-settings",name:"系统设置",icon:Ze},{key:"data-dashboard",name:"数据大屏",icon:We},{key:"profile-management",name:"个人资料管理",icon:Ye}],Z=[{key:"usb-devices",name:"USB设备管理"},{key:"slave-servers",name:"分布式节点管理"},{key:"device-groups",name:"资源调度分组"},{key:"permission-assignment",name:"授权范围管理"}],O=async()=>{U.value=!0;try{const s=await as();let e=s;s&&s.success&&s.data&&(console.log("🔧 loadRoles - 检测到API中间件包装格式，提取data字段"),e=s.data),console.log("loadRoles - 处理后的角色数据:",e),e&&e.roles?(p.value=Array.isArray(e.roles)?e.roles:[],console.log("加载角色列表成功:",p.value.length,"个角色"),e.filtered_by_permission&&console.log("权限过滤已生效")):Array.isArray(e)?(p.value=e,console.log("加载角色列表成功（数组格式）:",p.value.length,"个角色")):(p.value=[],console.log("角色数据格式异常，设置为空数组"))}catch(s){f.error("加载角色列表失败"),console.error("Load roles error:",s)}finally{U.value=!1}},ie=()=>{V.value=!1,A(),z(),R.value=!0},oe=s=>{V.value=!0,console.log("编辑角色原始数据:",s);const t={全域管理员:{navigation:X.map(y=>y.key),device:Z.map(y=>y.key),management:{can_manage_users:!0,can_manage_devices:!0,can_view_reports:!0}},超级管理员:{navigation:["dashboard","user-management","device-management","organization-management","role-management","system-settings","reports"],device:["device.view","device.connect","device.manage","device.assign","device.force_disconnect"],management:{can_manage_users:!0,can_manage_devices:!0,can_view_reports:!0}},管理员:{navigation:["dashboard","user-management","device-management","organization-management","reports"],device:["device.view","device.connect","device.assign","device.force_disconnect"],management:{can_manage_users:!0,can_manage_devices:!1,can_view_reports:!0}},普通用户:{navigation:["dashboard","device-management"],device:["device.view","device.connect"],management:{can_manage_users:!1,can_manage_devices:!1,can_view_reports:!1}},新用户:{navigation:["dashboard"],device:[],management:{can_manage_users:!1,can_manage_devices:!1,can_view_reports:!1}}}[s.name],l=s.navigation_permissions||[],_=s.device_sub_permissions||[],E=l.length>0?l:t?t.navigation:[],S=_.length>0?_:t?t.device:[],b=t?t.management:{};Object.assign(n,{id:s.id,name:s.name,description:s.description,permissions:s.permissions||[],navigation_permissions:E,device_sub_permissions:S,level_scope:s.level_scope||1,can_manage_users:s.can_manage_users!==void 0?s.can_manage_users:b.can_manage_users,can_manage_devices:s.can_manage_devices!==void 0?s.can_manage_devices:b.can_manage_devices,can_view_reports:s.can_view_reports!==void 0?s.can_view_reports:b.can_view_reports}),console.log("编辑角色最终数据:",{roleName:s.name,template:t,finalForm:n}),z(),c.value=!1,R.value=!0},te=s=>{C.value=s,T.value=!0},A=()=>{var s;Object.assign(n,{name:"",description:"",level_scope:0,permissions:[],navigation_permissions:[],device_sub_permissions:[],can_manage_users:!1,can_manage_devices:!1,can_view_reports:!1}),(s=I.value)==null||s.resetFields(),z(),c.value=!1},le=()=>{c.value=!0,f.info("现在可以编辑权限配置")},re=()=>{c.value=!1,R.value=!1,A()},de=async()=>{try{await _e(),c.value=!1}catch(s){console.error("保存角色失败:",s)}},z=()=>{h.navigation_permissions=[...n.navigation_permissions],h.device_sub_permissions=[...n.device_sub_permissions]},ce=()=>{z(),f.success("权限配置已确认")},me=()=>{n.navigation_permissions=[...h.navigation_permissions],n.device_sub_permissions=[...h.device_sub_permissions],f.info("权限配置已重置")},_e=async()=>{if(I.value)try{await I.value.validate(),F.value=!0;const s={name:n.name,description:n.description,permissions:n.permissions||[],navigation_permissions:n.navigation_permissions||[],device_sub_permissions:n.device_sub_permissions||[],level_scope:n.level_scope||1,can_manage_users:n.can_manage_users||!1,can_manage_devices:n.can_manage_devices||!1,can_view_reports:n.can_view_reports||!1};if(console.log("保存角色数据:",s),V.value){const e=await is(n.id,s);console.log("角色更新响应:",e),f.success("角色更新成功"),await ge(n.id,s)}else{const e=await ns(s);console.log("角色创建响应:",e),f.success("角色创建成功")}R.value=!1,await O(),V.value&&await fe(n.id)}catch(s){f.error(V.value?"角色更新失败":"角色创建失败"),console.error("Save role error:",s)}finally{F.value=!1}},ue=async s=>{try{await ss.confirm(`确定要删除角色 "${s.name}" 吗？此操作不可恢复。`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await os(s.id),f.success("角色删除成功"),await O()}catch(e){e!=="cancel"&&(f.error("角色删除失败"),console.error("Delete role error:",e))}},ve=s=>s.name==="全域管理员",pe=s=>["全域管理员","超级管理员","管理员","普通用户","新用户"].includes(s.name),ge=async(s,e)=>{try{typeof window<"u"&&window.dispatchEvent(new CustomEvent("rolePermissionUpdated",{detail:{roleId:s,roleName:e.name,permissions:{navigation:e.navigation_permissions||[],device:e.device_sub_permissions||[],management:{can_manage_users:e.can_manage_users,can_manage_devices:e.can_manage_devices,can_view_reports:e.can_view_reports}},timestamp:new Date().toISOString()}})),console.log("权限更新事件已发送:",s,e.name)}catch(t){console.error("发送权限更新事件失败:",t)}},fe=async s=>{try{if(!s)return;const e=await getRoleById(s),t=e.data||e;console.log("获取角色详情:",t),V.value&&n.id===s&&Object.assign(n,{...t,navigation_permissions:t.navigation_permissions||[],device_sub_permissions:t.device_sub_permissions||[],can_manage_users:t.can_manage_users||!1,can_manage_devices:t.can_manage_devices||!1,can_view_reports:t.can_view_reports||!1});const l=p.value.findIndex(_=>_.id===s);l!==-1&&(p.value[l]={...p.value[l],...t}),console.log("角色权限已刷新:",s)}catch(e){console.error("刷新角色权限失败:",e)}},be=()=>{typeof window<"u"&&(window.addEventListener("rolePermissionUpdated",s=>{const{roleId:e,roleName:t,permissions:l,timestamp:_}=s.detail;console.log("接收到权限更新事件:",{roleId:e,roleName:t,timestamp:_});const E=p.value.findIndex(S=>S.id===e);E!==-1&&(p.value[E]={...p.value[E],navigation_permissions:l.navigation,device_sub_permissions:l.device,...l.management}),V.value&&n.id===e&&Object.assign(n,{...n,navigation_permissions:l.navigation,device_sub_permissions:l.device,...l.management}),f.success(`角色"${t}"权限已同步更新`)}),window.addEventListener("devicePermissionChanged",s=>{const{userId:e,deviceId:t,action:l,timestamp:_}=s.detail;console.log("设备权限变更:",{userId:e,deviceId:t,action:l,timestamp:_}),O()}))},ye=()=>{typeof window<"u"&&(window.removeEventListener("rolePermissionUpdated",()=>{}),window.removeEventListener("devicePermissionChanged",()=>{}))};return Pe(()=>{O(),be()}),xe(()=>{ye()}),(s,e)=>{const t=he,l=Oe,_=Ie,E=De,S=$e,b=Te,y=je,we=Ae,D=Me,j=Je,J=He,ke=Fe,W=Ue,B=es,Ve=Qe,Ce=Ne;return d(),P("div",ts,[u("div",ls,[e[9]||(e[9]=u("div",{class:"header-content"},[u("h2",null,"角色管理"),u("p",null,"管理系统角色和权限分配，仅超级管理员可访问")],-1)),u("div",rs,[i(t,{type:"primary",onClick:ie,icon:N(Re)},{default:a(()=>e[8]||(e[8]=[r(" 创建角色 ",-1)])),_:1,__:[8]},8,["icon"])])]),u("div",ds,[Se((d(),v(E,{data:p.value,style:{width:"100%"}},{default:a(()=>[i(_,{prop:"name",label:"角色名称",width:"150"},{default:a(({row:o})=>[u("div",cs,[o.is_system_role?(d(),v(l,{key:0,type:"danger",size:"small"},{default:a(()=>e[10]||(e[10]=[r("系统",-1)])),_:1,__:[10]})):g("",!0),r(" "+k(o.name),1)])]),_:1}),i(_,{prop:"description",label:"角色描述",width:"140"}),i(_,{label:"权限范围",width:"100"},{default:a(({row:o})=>[o.level_scope===0?(d(),v(l,{key:0,type:"warning"},{default:a(()=>e[11]||(e[11]=[r("无限制",-1)])),_:1,__:[11]})):(d(),v(l,{key:1,type:"info"},{default:a(()=>[r(k(o.level_scope)+"级",1)]),_:2},1024))]),_:1}),i(_,{label:"状态",width:"80"},{default:a(({row:o})=>[i(l,{type:o.is_active?"success":"danger"},{default:a(()=>[r(k(o.is_active?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),i(_,{label:"操作","min-width":"250",fixed:"right"},{default:a(({row:o})=>[i(t,{size:"small",onClick:Y=>te(o),icon:N(ze)},{default:a(()=>e[12]||(e[12]=[r(" 查看 ",-1)])),_:2,__:[12]},1032,["onClick","icon"]),ve(o)?g("",!0):(d(),v(t,{key:0,size:"small",type:"primary",onClick:Y=>oe(o),icon:N(Be)},{default:a(()=>e[13]||(e[13]=[r(" 编辑 ",-1)])),_:2,__:[13]},1032,["onClick","icon"])),pe(o)?g("",!0):(d(),v(t,{key:1,size:"small",type:"danger",onClick:Y=>ue(o),icon:N(Le)},{default:a(()=>e[14]||(e[14]=[r(" 删除 ",-1)])),_:2,__:[14]},1032,["onClick","icon"]))]),_:1})]),_:1},8,["data"])),[[Ce,U.value]])]),i(W,{modelValue:R.value,"onUpdate:modelValue":e[6]||(e[6]=o=>R.value=o),title:V.value?"编辑角色":"创建角色",width:"900px",onClose:A},{footer:a(()=>[u("span",gs,[c.value?g("",!0):(d(),v(t,{key:0,onClick:le},{default:a(()=>e[22]||(e[22]=[r("开始修改",-1)])),_:1,__:[22]})),c.value?(d(),v(t,{key:1,onClick:re},{default:a(()=>e[23]||(e[23]=[r("取消修改",-1)])),_:1,__:[23]})):g("",!0),c.value?(d(),v(t,{key:2,type:"primary",onClick:de,loading:F.value},{default:a(()=>e[24]||(e[24]=[r(" 确认保存 ",-1)])),_:1,__:[24]},8,["loading"])):g("",!0)])]),default:a(()=>[i(ke,{model:n,rules:ne,ref_key:"roleFormRef",ref:I,"label-width":"120px"},{default:a(()=>[i(b,{label:"角色名称",prop:"name"},{default:a(()=>[i(S,{modelValue:n.name,"onUpdate:modelValue":e[0]||(e[0]=o=>n.name=o),placeholder:"请输入角色名称"},null,8,["modelValue"])]),_:1}),i(b,{label:"角色描述",prop:"description"},{default:a(()=>[i(S,{modelValue:n.description,"onUpdate:modelValue":e[1]||(e[1]=o=>n.description=o),type:"textarea",rows:3,placeholder:"请输入角色描述"},null,8,["modelValue"])]),_:1}),i(b,{label:"权限层级"},{default:a(()=>[i(we,{modelValue:n.level_scope,"onUpdate:modelValue":e[2]||(e[2]=o=>n.level_scope=o),placeholder:"选择权限层级范围"},{default:a(()=>[i(y,{label:"无限制",value:0}),i(y,{label:"1级权限",value:1}),i(y,{label:"2级权限",value:2}),i(y,{label:"3级权限",value:3}),i(y,{label:"4级权限",value:4})]),_:1},8,["modelValue"])]),_:1}),i(b,{label:"管理权限"},{default:a(()=>[i(j,{modelValue:G.value,"onUpdate:modelValue":e[3]||(e[3]=o=>G.value=o)},{default:a(()=>[i(D,{label:"can_manage_users"},{default:a(()=>e[15]||(e[15]=[r("用户管理",-1)])),_:1,__:[15]}),i(D,{label:"can_manage_devices"},{default:a(()=>e[16]||(e[16]=[r("设备管理",-1)])),_:1,__:[16]}),i(D,{label:"can_view_reports"},{default:a(()=>e[17]||(e[17]=[r("报告查看",-1)])),_:1,__:[17]})]),_:1},8,["modelValue"])]),_:1}),i(b,{label:"功能权限"},{default:a(()=>[u("div",ms,[e[21]||(e[21]=u("h4",null,"一级权限（导航菜单）",-1)),i(j,{modelValue:n.navigation_permissions,"onUpdate:modelValue":e[4]||(e[4]=o=>n.navigation_permissions=o),class:"navigation-group-grid",disabled:m.value||!c.value},{default:a(()=>[(d(),P(K,null,Q(X,o=>i(D,{key:o.key,label:o.key,disabled:m.value||!c.value,class:q(["nav-permission-item-grid",{"global-admin-item":m.value,"is-disabled":m.value}])},{default:a(()=>[u("div",_s,[i(J,{class:q(["nav-icon",{"global-admin-icon":m.value}])},{default:a(()=>[(d(),v(Ke(o.icon)))]),_:2},1032,["class"]),u("span",null,k(o.name),1),m.value?(d(),v(J,{key:0,class:"admin-badge"},{default:a(()=>[i(N(ae))]),_:1})):g("",!0)])]),_:2},1032,["label","disabled","class"])),64))]),_:1},8,["modelValue","disabled"]),n.navigation_permissions.includes("device-center")||m.value?(d(),P("div",us,[e[18]||(e[18]=u("h4",null,"设备管理中心 - 二级权限",-1)),i(j,{modelValue:n.device_sub_permissions,"onUpdate:modelValue":e[5]||(e[5]=o=>n.device_sub_permissions=o),class:"sub-permission-group",disabled:m.value||!c.value},{default:a(()=>[(d(),P(K,null,Q(Z,o=>i(D,{key:o.key,label:o.key,disabled:m.value||!c.value,class:q(["sub-permission-item",{"global-admin-item":m.value,"is-disabled":m.value||!c.value}])},{default:a(()=>[u("span",null,k(o.name),1),m.value?(d(),v(J,{key:0,class:"admin-badge"},{default:a(()=>[i(N(ae))]),_:1})):g("",!0)]),_:2},1032,["label","disabled","class"])),64))]),_:1},8,["modelValue","disabled"])])):g("",!0),!m.value&&c.value?(d(),P("div",vs,[i(t,{type:"success",onClick:ce,disabled:!$.value,size:"small"},{default:a(()=>e[19]||(e[19]=[r(" 确定修改 ",-1)])),_:1,__:[19]},8,["disabled"]),i(t,{onClick:me,disabled:!$.value,size:"small"},{default:a(()=>e[20]||(e[20]=[r(" 放弃修改 ",-1)])),_:1,__:[20]},8,["disabled"]),$.value?(d(),P("span",ps," 权限配置已修改 ")):g("",!0)])):g("",!0)])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),i(W,{modelValue:T.value,"onUpdate:modelValue":e[7]||(e[7]=o=>T.value=o),title:"角色详情",width:"500px"},{default:a(()=>[C.value?(d(),P("div",fs,[i(Ve,{column:1,border:""},{default:a(()=>[i(B,{label:"角色名称"},{default:a(()=>[r(k(C.value.name),1)]),_:1}),i(B,{label:"角色描述"},{default:a(()=>[r(k(C.value.description),1)]),_:1}),i(B,{label:"系统角色"},{default:a(()=>[i(l,{type:C.value.is_system_role?"danger":"success"},{default:a(()=>[r(k(C.value.is_system_role?"是":"否"),1)]),_:1},8,["type"])]),_:1}),i(B,{label:"权限层级"},{default:a(()=>[C.value.level_scope===0?(d(),v(l,{key:0,type:"warning"},{default:a(()=>e[25]||(e[25]=[r("无限制",-1)])),_:1,__:[25]})):(d(),v(l,{key:1,type:"info"},{default:a(()=>[r(k(C.value.level_scope)+"级",1)]),_:1}))]),_:1})]),_:1})])):g("",!0)]),_:1},8,["modelValue"])])}}},Os=Ee(bs,[["__scopeId","data-v-e4f71fad"]]);export{Os as default};
