import{_ as Ce}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                   *//* empty css                             *//* empty css                   *//* empty css                     *//* empty css                          *//* empty css                    *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  */import{ds as F,r as k,a as H,aN as M,o as Pe,bM as xe,c as x,d as u,e as i,w as n,p as S,cR as Re,m as he,a9 as Ne,aa as Se,y as v,x as De,s as b,ac as Ue,i as d,n as r,af as Ie,z as f,a7 as Oe,t as V,cW as ze,cV as Be,cX as Fe,f as Le,j as Te,k as $e,ad as Ae,ae as je,cN as Je,cO as Me,a5 as K,a6 as Q,X as qe,Y as Ge,Z as Xe,_ as ee,L as se,$ as Ze,a0 as We,R as Ye,a2 as q,E as He,bZ as Ke,dl as ae,al as Qe,am as es,a4 as ss}from"./index-Cp0qD91m.js";function as(){return F.get("/api/v2/roles/")}function ns(R){return F.post("/api/v2/roles/",R)}function is(R,D){return F.put(`/api/v2/roles/${R}`,D)}function os(R){return F.delete(`/api/v2/roles/${R}`)}const ts={class:"role-management"},ls={class:"page-header"},rs={class:"header-actions"},ds={class:"role-list"},cs={class:"role-name"},ms={class:"navigation-permissions"},_s={class:"nav-permission-content"},us={key:0,class:"sub-permissions"},vs={key:1,class:"permission-actions"},ps={key:0,class:"change-indicator"},gs={class:"dialog-footer"},fs={key:0,class:"role-detail"},bs={__name:"index",setup(R){const D=k(!1),L=k(!1),h=k(!1),T=k(!1),C=k(!1),m=k(!1),p=k([]),E=k(null),I=k(),a=H({name:"",description:"",level_scope:0,permissions:[],navigation_permissions:[],device_sub_permissions:[],can_manage_users:!1,can_manage_devices:!1,can_view_reports:!1}),N=H({navigation_permissions:[],device_sub_permissions:[]}),G=M({get(){const s=[];return a.can_manage_users&&s.push("can_manage_users"),a.can_manage_devices&&s.push("can_manage_devices"),a.can_view_reports&&s.push("can_view_reports"),s},set(s){a.can_manage_users=s.includes("can_manage_users"),a.can_manage_devices=s.includes("can_manage_devices"),a.can_view_reports=s.includes("can_view_reports")}}),_=M(()=>a.name==="全域管理员"),$=M(()=>{if(_.value)return!1;const s=JSON.stringify(a.navigation_permissions.sort())!==JSON.stringify(N.navigation_permissions.sort()),e=JSON.stringify(a.device_sub_permissions.sort())!==JSON.stringify(N.device_sub_permissions.sort());return s||e}),ne={name:[{required:!0,message:"请输入角色名称",trigger:"blur"},{min:2,max:50,message:"角色名称长度在 2 到 50 个字符",trigger:"blur"}],description:[{required:!0,message:"请输入角色描述",trigger:"blur"}]},X=[{key:"dashboard",name:"工作台",icon:qe},{key:"applications",name:"处理事项",icon:Ge},{key:"org-users",name:"组织与用户管理",icon:Xe},{key:"user-registration",name:"新用户审核",icon:ee},{key:"device-center",name:"设备管理中心",icon:se},{key:"client-management",name:"设备绑定中心",icon:se},{key:"role-management",name:"角色管理",icon:ee},{key:"system-settings",name:"系统设置",icon:Ze},{key:"data-dashboard",name:"数据大屏",icon:We},{key:"profile-management",name:"个人资料管理",icon:Ye}],Z=[{key:"usb-devices",name:"USB设备管理"},{key:"slave-servers",name:"分布式节点管理"},{key:"device-groups",name:"资源调度分组"},{key:"permission-assignment",name:"授权范围管理"}],O=async()=>{D.value=!0;try{const s=await as();let e=s;s&&s.success&&s.data&&(console.log("🔧 loadRoles - 检测到API中间件包装格式，提取data字段"),e=s.data),console.log("loadRoles - 处理后的角色数据:",e),e&&e.roles?(p.value=Array.isArray(e.roles)?e.roles:[],console.log("加载角色列表成功:",p.value.length,"个角色"),e.filtered_by_permission&&console.log("权限过滤已生效")):Array.isArray(e)?(p.value=e,console.log("加载角色列表成功（数组格式）:",p.value.length,"个角色")):(p.value=[],console.log("角色数据格式异常，设置为空数组"))}catch(s){b.error("加载角色列表失败"),console.error("Load roles error:",s)}finally{D.value=!1}},ie=()=>{C.value=!1,A(),z(),h.value=!0},oe=s=>{C.value=!0,console.log("编辑角色原始数据:",s);const t={全域管理员:{navigation:X.map(w=>w.key),device:Z.map(w=>w.key),management:{can_manage_users:!0,can_manage_devices:!0,can_view_reports:!0}},超级管理员:{navigation:["dashboard","user-management","device-management","organization-management","role-management","system-settings","reports"],device:["device.view","device.connect","device.manage","device.assign","device.force_disconnect"],management:{can_manage_users:!0,can_manage_devices:!0,can_view_reports:!0}},管理员:{navigation:["dashboard","user-management","device-management","organization-management","reports"],device:["device.view","device.connect","device.assign","device.force_disconnect"],management:{can_manage_users:!0,can_manage_devices:!1,can_view_reports:!0}},普通用户:{navigation:["dashboard","device-management"],device:["device.view","device.connect"],management:{can_manage_users:!1,can_manage_devices:!1,can_view_reports:!1}},新用户:{navigation:["dashboard"],device:[],management:{can_manage_users:!1,can_manage_devices:!1,can_view_reports:!1}}}[s.name],l=s.navigation_permissions||[],c=s.device_sub_permissions||[],g=l.length>0?l:t?t.navigation:[],P=c.length>0?c:t?t.device:[];console.log("权限配置详情:",{roleName:s.name,currentNav:l,currentDevice:c,finalNav:g,finalDevice:P});const y=t?t.management:{};Object.assign(a,{id:s.id,name:s.name,description:s.description,permissions:s.permissions||[],navigation_permissions:g,device_sub_permissions:P,level_scope:s.level_scope||1,can_manage_users:s.can_manage_users!==void 0?s.can_manage_users:y.can_manage_users,can_manage_devices:s.can_manage_devices!==void 0?s.can_manage_devices:y.can_manage_devices,can_view_reports:s.can_view_reports!==void 0?s.can_view_reports:y.can_view_reports}),console.log("编辑角色最终数据:",{roleName:s.name,template:t,finalForm:a}),z(),m.value=!1,h.value=!0},te=s=>{E.value=s,T.value=!0},A=()=>{var s;Object.assign(a,{name:"",description:"",level_scope:0,permissions:[],navigation_permissions:[],device_sub_permissions:[],can_manage_users:!1,can_manage_devices:!1,can_view_reports:!1}),(s=I.value)==null||s.resetFields(),z(),m.value=!1},le=()=>{m.value=!0,b.info("现在可以编辑权限配置")},re=()=>{m.value=!1,h.value=!1,A()},de=async()=>{try{await _e(),m.value=!1}catch(s){console.error("保存角色失败:",s)}},z=()=>{N.navigation_permissions=[...a.navigation_permissions],N.device_sub_permissions=[...a.device_sub_permissions]},ce=()=>{z(),b.success("权限配置已确认")},me=()=>{a.navigation_permissions=[...N.navigation_permissions],a.device_sub_permissions=[...N.device_sub_permissions],b.info("权限配置已重置")},_e=async()=>{if(I.value)try{await I.value.validate(),L.value=!0;const s={name:a.name,description:a.description,permissions:a.permissions||[],navigation_permissions:a.navigation_permissions||[],device_sub_permissions:a.device_sub_permissions||[],level_scope:a.level_scope||1,can_manage_users:a.can_manage_users||!1,can_manage_devices:a.can_manage_devices||!1,can_view_reports:a.can_view_reports||!1};if(console.log("保存角色数据:",s),C.value){const e=await is(a.id,s);console.log("角色更新响应:",e),b.success("角色更新成功"),await ge(a.id,s),await fe(a.id)}else{const e=await ns(s);console.log("角色创建响应:",e),b.success("角色创建成功")}await O(),h.value=!1}catch(s){b.error(C.value?"角色更新失败":"角色创建失败"),console.error("Save role error:",s)}finally{L.value=!1}},ue=async s=>{try{await ss.confirm(`确定要删除角色 "${s.name}" 吗？此操作不可恢复。`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await os(s.id),b.success("角色删除成功"),await O()}catch(e){e!=="cancel"&&(b.error("角色删除失败"),console.error("Delete role error:",e))}},ve=s=>s.name==="全域管理员",pe=s=>["全域管理员","超级管理员","管理员","普通用户","新用户"].includes(s.name),ge=async(s,e)=>{try{typeof window<"u"&&window.dispatchEvent(new CustomEvent("rolePermissionUpdated",{detail:{roleId:s,roleName:e.name,permissions:{navigation:e.navigation_permissions||[],device:e.device_sub_permissions||[],management:{can_manage_users:e.can_manage_users,can_manage_devices:e.can_manage_devices,can_view_reports:e.can_view_reports}},timestamp:new Date().toISOString()}})),console.log("权限更新事件已发送:",s,e.name)}catch(t){console.error("发送权限更新事件失败:",t)}},fe=async s=>{try{if(!s)return;const e=await getRoleById(s),t=e.data||e;if(console.log("获取角色详情:",t),C.value&&a.id===s){const c={id:t.id,name:t.name,description:t.description,permissions:t.permissions||[],navigation_permissions:t.navigation_permissions||[],device_sub_permissions:t.device_sub_permissions||[],level_scope:t.level_scope||1,can_manage_users:t.can_manage_users||!1,can_manage_devices:t.can_manage_devices||!1,can_view_reports:t.can_view_reports||!1};Object.keys(a).forEach(g=>{g in c&&(a[g]=c[g])}),console.log("表单数据已更新:",a)}const l=p.value.findIndex(c=>c.id===s);l!==-1&&(p.value[l]={...p.value[l],...t}),console.log("角色权限已刷新:",s)}catch(e){console.error("刷新角色权限失败:",e)}},be=()=>{typeof window<"u"&&(window.addEventListener("rolePermissionUpdated",s=>{const{roleId:e,roleName:t,permissions:l,timestamp:c}=s.detail;console.log("接收到权限更新事件:",{roleId:e,roleName:t,timestamp:c});const g=p.value.findIndex(P=>P.id===e);g!==-1&&(p.value[g]={...p.value[g],navigation_permissions:l.navigation,device_sub_permissions:l.device,...l.management}),C.value&&a.id===e&&Object.assign(a,{...a,navigation_permissions:l.navigation,device_sub_permissions:l.device,...l.management}),b.success(`角色"${t}"权限已同步更新`)}),window.addEventListener("devicePermissionChanged",s=>{const{userId:e,deviceId:t,action:l,timestamp:c}=s.detail;console.log("设备权限变更:",{userId:e,deviceId:t,action:l,timestamp:c}),O()}))},ye=()=>{typeof window<"u"&&(window.removeEventListener("rolePermissionUpdated",()=>{}),window.removeEventListener("devicePermissionChanged",()=>{}))};return Pe(()=>{O(),be()}),xe(()=>{ye()}),(s,e)=>{const t=he,l=Oe,c=Ie,g=Ue,P=$e,y=Te,w=je,we=Ae,U=Me,j=Je,J=He,ke=Le,W=De,B=es,Ve=Qe,Ee=Se;return d(),x("div",ts,[u("div",ls,[e[9]||(e[9]=u("div",{class:"header-content"},[u("h2",null,"角色管理"),u("p",null,"管理系统角色和权限分配，仅超级管理员可访问")],-1)),u("div",rs,[i(t,{type:"primary",onClick:ie,icon:S(Re)},{default:n(()=>e[8]||(e[8]=[r(" 创建角色 ",-1)])),_:1,__:[8]},8,["icon"])])]),u("div",ds,[Ne((d(),v(g,{data:p.value,style:{width:"100%"}},{default:n(()=>[i(c,{prop:"name",label:"角色名称",width:"150"},{default:n(({row:o})=>[u("div",cs,[o.is_system_role?(d(),v(l,{key:0,type:"danger",size:"small"},{default:n(()=>e[10]||(e[10]=[r("系统",-1)])),_:1,__:[10]})):f("",!0),r(" "+V(o.name),1)])]),_:1}),i(c,{prop:"description",label:"角色描述",width:"140"}),i(c,{label:"权限范围",width:"100"},{default:n(({row:o})=>[o.level_scope===0?(d(),v(l,{key:0,type:"warning"},{default:n(()=>e[11]||(e[11]=[r("无限制",-1)])),_:1,__:[11]})):(d(),v(l,{key:1,type:"info"},{default:n(()=>[r(V(o.level_scope)+"级",1)]),_:2},1024))]),_:1}),i(c,{label:"状态",width:"80"},{default:n(({row:o})=>[i(l,{type:o.is_active?"success":"danger"},{default:n(()=>[r(V(o.is_active?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),i(c,{label:"操作","min-width":"250",fixed:"right"},{default:n(({row:o})=>[i(t,{size:"small",onClick:Y=>te(o),icon:S(ze)},{default:n(()=>e[12]||(e[12]=[r(" 查看 ",-1)])),_:2,__:[12]},1032,["onClick","icon"]),ve(o)?f("",!0):(d(),v(t,{key:0,size:"small",type:"primary",onClick:Y=>oe(o),icon:S(Be)},{default:n(()=>e[13]||(e[13]=[r(" 编辑 ",-1)])),_:2,__:[13]},1032,["onClick","icon"])),pe(o)?f("",!0):(d(),v(t,{key:1,size:"small",type:"danger",onClick:Y=>ue(o),icon:S(Fe)},{default:n(()=>e[14]||(e[14]=[r(" 删除 ",-1)])),_:2,__:[14]},1032,["onClick","icon"]))]),_:1})]),_:1},8,["data"])),[[Ee,D.value]])]),i(W,{modelValue:h.value,"onUpdate:modelValue":e[6]||(e[6]=o=>h.value=o),title:C.value?"编辑角色":"创建角色",width:"900px",onClose:A},{footer:n(()=>[u("span",gs,[m.value?f("",!0):(d(),v(t,{key:0,onClick:le},{default:n(()=>e[22]||(e[22]=[r("开始修改",-1)])),_:1,__:[22]})),m.value?(d(),v(t,{key:1,onClick:re},{default:n(()=>e[23]||(e[23]=[r("取消修改",-1)])),_:1,__:[23]})):f("",!0),m.value?(d(),v(t,{key:2,type:"primary",onClick:de,loading:L.value},{default:n(()=>e[24]||(e[24]=[r(" 确认保存 ",-1)])),_:1,__:[24]},8,["loading"])):f("",!0)])]),default:n(()=>[i(ke,{model:a,rules:ne,ref_key:"roleFormRef",ref:I,"label-width":"120px"},{default:n(()=>[i(y,{label:"角色名称",prop:"name"},{default:n(()=>[i(P,{modelValue:a.name,"onUpdate:modelValue":e[0]||(e[0]=o=>a.name=o),placeholder:"请输入角色名称"},null,8,["modelValue"])]),_:1}),i(y,{label:"角色描述",prop:"description"},{default:n(()=>[i(P,{modelValue:a.description,"onUpdate:modelValue":e[1]||(e[1]=o=>a.description=o),type:"textarea",rows:3,placeholder:"请输入角色描述"},null,8,["modelValue"])]),_:1}),i(y,{label:"权限层级"},{default:n(()=>[i(we,{modelValue:a.level_scope,"onUpdate:modelValue":e[2]||(e[2]=o=>a.level_scope=o),placeholder:"选择权限层级范围"},{default:n(()=>[i(w,{label:"无限制",value:0}),i(w,{label:"1级权限",value:1}),i(w,{label:"2级权限",value:2}),i(w,{label:"3级权限",value:3}),i(w,{label:"4级权限",value:4})]),_:1},8,["modelValue"])]),_:1}),i(y,{label:"管理权限"},{default:n(()=>[i(j,{modelValue:G.value,"onUpdate:modelValue":e[3]||(e[3]=o=>G.value=o)},{default:n(()=>[i(U,{label:"can_manage_users"},{default:n(()=>e[15]||(e[15]=[r("用户管理",-1)])),_:1,__:[15]}),i(U,{label:"can_manage_devices"},{default:n(()=>e[16]||(e[16]=[r("设备管理",-1)])),_:1,__:[16]}),i(U,{label:"can_view_reports"},{default:n(()=>e[17]||(e[17]=[r("报告查看",-1)])),_:1,__:[17]})]),_:1},8,["modelValue"])]),_:1}),i(y,{label:"功能权限"},{default:n(()=>[u("div",ms,[e[21]||(e[21]=u("h4",null,"一级权限（导航菜单）",-1)),i(j,{modelValue:a.navigation_permissions,"onUpdate:modelValue":e[4]||(e[4]=o=>a.navigation_permissions=o),class:"navigation-group-grid",disabled:_.value||!m.value},{default:n(()=>[(d(),x(K,null,Q(X,o=>i(U,{key:o.key,label:o.key,disabled:_.value||!m.value,class:q(["nav-permission-item-grid",{"global-admin-item":_.value,"is-disabled":_.value}])},{default:n(()=>[u("div",_s,[i(J,{class:q(["nav-icon",{"global-admin-icon":_.value}])},{default:n(()=>[(d(),v(Ke(o.icon)))]),_:2},1032,["class"]),u("span",null,V(o.name),1),_.value?(d(),v(J,{key:0,class:"admin-badge"},{default:n(()=>[i(S(ae))]),_:1})):f("",!0)])]),_:2},1032,["label","disabled","class"])),64))]),_:1},8,["modelValue","disabled"]),a.navigation_permissions.includes("device-center")||_.value?(d(),x("div",us,[e[18]||(e[18]=u("h4",null,"设备管理中心 - 二级权限",-1)),i(j,{modelValue:a.device_sub_permissions,"onUpdate:modelValue":e[5]||(e[5]=o=>a.device_sub_permissions=o),class:"sub-permission-group",disabled:_.value||!m.value},{default:n(()=>[(d(),x(K,null,Q(Z,o=>i(U,{key:o.key,label:o.key,disabled:_.value||!m.value,class:q(["sub-permission-item",{"global-admin-item":_.value,"is-disabled":_.value||!m.value}])},{default:n(()=>[u("span",null,V(o.name),1),_.value?(d(),v(J,{key:0,class:"admin-badge"},{default:n(()=>[i(S(ae))]),_:1})):f("",!0)]),_:2},1032,["label","disabled","class"])),64))]),_:1},8,["modelValue","disabled"])])):f("",!0),!_.value&&m.value?(d(),x("div",vs,[i(t,{type:"success",onClick:ce,disabled:!$.value,size:"small"},{default:n(()=>e[19]||(e[19]=[r(" 确定修改 ",-1)])),_:1,__:[19]},8,["disabled"]),i(t,{onClick:me,disabled:!$.value,size:"small"},{default:n(()=>e[20]||(e[20]=[r(" 放弃修改 ",-1)])),_:1,__:[20]},8,["disabled"]),$.value?(d(),x("span",ps," 权限配置已修改 ")):f("",!0)])):f("",!0)])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),i(W,{modelValue:T.value,"onUpdate:modelValue":e[7]||(e[7]=o=>T.value=o),title:"角色详情",width:"500px"},{default:n(()=>[E.value?(d(),x("div",fs,[i(Ve,{column:1,border:""},{default:n(()=>[i(B,{label:"角色名称"},{default:n(()=>[r(V(E.value.name),1)]),_:1}),i(B,{label:"角色描述"},{default:n(()=>[r(V(E.value.description),1)]),_:1}),i(B,{label:"系统角色"},{default:n(()=>[i(l,{type:E.value.is_system_role?"danger":"success"},{default:n(()=>[r(V(E.value.is_system_role?"是":"否"),1)]),_:1},8,["type"])]),_:1}),i(B,{label:"权限层级"},{default:n(()=>[E.value.level_scope===0?(d(),v(l,{key:0,type:"warning"},{default:n(()=>e[25]||(e[25]=[r("无限制",-1)])),_:1,__:[25]})):(d(),v(l,{key:1,type:"info"},{default:n(()=>[r(V(E.value.level_scope)+"级",1)]),_:1}))]),_:1})]),_:1})])):f("",!0)]),_:1},8,["modelValue"])])}}},Os=Ce(bs,[["__scopeId","data-v-64fe0712"]]);export{Os as default};
