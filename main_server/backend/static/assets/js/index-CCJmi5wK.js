import{_ as <PERSON>}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                   *//* empty css                             *//* empty css                   *//* empty css                     *//* empty css                          *//* empty css                    *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  */import{ds as B,r as y,a as Z,a9 as <PERSON>,o as he,bN as xe,c as R,d as u,e as o,w as i,p as A,cR as Re,m as Ne,ab as Ae,ac as Se,y as v,x as De,s as p,ah as Ue,i as d,n as r,ak as Be,z as b,a7 as Ie,t as w,cW as Oe,cV as $e,cX as Fe,f as Le,j as Te,k as ze,ai as je,aj as Je,cN as qe,cO as Me,a5 as H,a6 as K,X as Ge,Y as Xe,Z as We,_ as Q,L as ee,$ as Ye,a0 as Ze,R as He,a2 as G,E as Ke,b_ as Qe,dl as se,af as es,ae as ss,a4 as as}from"./index-BRsehXTN.js";function ns(){return B.get("/api/v2/roles/")}function ae(k){return B.get(`/api/v2/roles/${k}`)}function is(k){return B.post("/api/v2/roles/",k)}function os(k,S){return B.put(`/api/v2/roles/${k}`,S)}function ls(k){return B.delete(`/api/v2/roles/${k}`)}const ts={class:"role-management"},rs={class:"page-header"},cs={class:"header-actions"},ds={class:"role-list"},ms={class:"role-name"},_s={class:"navigation-permissions"},us={class:"nav-permission-content"},vs={key:0,class:"sub-permissions"},ps={key:1,class:"permission-actions"},gs={key:0,class:"change-indicator"},fs={class:"dialog-footer"},bs={key:0,class:"role-detail"},ys={__name:"index",setup(k){const S=y(!1),L=y(!1),N=y(!1),T=y(!1),x=y(!1),m=y(!1),f=y([]),E=y(null),I=y(),n=Z({name:"",description:"",level_scope:0,permissions:[],navigation_permissions:[],device_sub_permissions:[],can_manage_users:!1,can_manage_devices:!1,can_view_reports:!1}),P=Z({navigation_permissions:[],device_sub_permissions:[]}),X=M({get(){const a=[];return n.can_manage_users&&a.push("can_manage_users"),n.can_manage_devices&&a.push("can_manage_devices"),n.can_view_reports&&a.push("can_view_reports"),a},set(a){n.can_manage_users=a.includes("can_manage_users"),n.can_manage_devices=a.includes("can_manage_devices"),n.can_view_reports=a.includes("can_view_reports")}}),_=M(()=>n.name==="全域管理员"),z=M(()=>{if(_.value)return!1;const a=JSON.stringify(n.navigation_permissions.sort())!==JSON.stringify(P.navigation_permissions.sort()),e=JSON.stringify(n.device_sub_permissions.sort())!==JSON.stringify(P.device_sub_permissions.sort());return a||e}),ne={name:[{required:!0,message:"请输入角色名称",trigger:"blur"},{min:2,max:50,message:"角色名称长度在 2 到 50 个字符",trigger:"blur"}],description:[{required:!0,message:"请输入角色描述",trigger:"blur"}]},ie=[{key:"dashboard",name:"工作台",icon:Ge},{key:"applications",name:"处理事项",icon:Xe},{key:"org-users",name:"组织与用户管理",icon:We},{key:"user-registration",name:"新用户审核",icon:Q},{key:"device-center",name:"设备管理中心",icon:ee},{key:"client-management",name:"设备绑定中心",icon:ee},{key:"role-management",name:"角色管理",icon:Q},{key:"system-settings",name:"系统设置",icon:Ye},{key:"data-dashboard",name:"数据大屏",icon:Ze},{key:"profile-management",name:"个人资料管理",icon:He}],oe=[{key:"usb-devices",name:"USB设备管理"},{key:"slave-servers",name:"分布式节点管理"},{key:"device-groups",name:"资源调度分组"},{key:"permission-assignment",name:"授权范围管理"}],O=async()=>{S.value=!0;try{const a=await ns();let e=a;a&&a.success&&a.data&&(console.log("🔧 loadRoles - 检测到API中间件包装格式，提取data字段"),e=a.data),console.log("loadRoles - 处理后的角色数据:",e),e&&e.roles?(f.value=Array.isArray(e.roles)?e.roles:[],console.log("加载角色列表成功:",f.value.length,"个角色"),e.filtered_by_permission&&console.log("权限过滤已生效")):Array.isArray(e)?(f.value=e,console.log("加载角色列表成功（数组格式）:",f.value.length,"个角色")):(f.value=[],console.log("角色数据格式异常，设置为空数组"))}catch(a){p.error("加载角色列表失败"),console.error("Load roles error:",a)}finally{S.value=!1}},le=()=>{x.value=!1,j(),$(),N.value=!0},te=async a=>{x.value=!0,console.log("编辑角色原始数据:",a);try{console.log("正在获取角色完整权限数据...");const e=await ae(a.id);let s=null;if(e&&e.success&&e.data)s=e.data;else if(e&&e.id)s=e;else throw new Error("获取角色详情失败");console.log("获取到的完整角色数据:",s);const t={全域管理员:{navigation:["dashboard","applications","org-users","user-registration","device-center","client-management","role-management","system-settings","data-dashboard"],device:["usb-devices","slave-servers","device-groups","permission-assignment"],management:{can_manage_users:!0,can_manage_devices:!0,can_view_reports:!0}},超级管理员:{navigation:["dashboard","applications","org-users","user-registration","device-center","client-management","role-management","system-settings"],device:["usb-devices","slave-servers","device-groups","permission-assignment"],management:{can_manage_users:!0,can_manage_devices:!0,can_view_reports:!0}},管理员:{navigation:["dashboard","applications","org-users","user-registration","device-center","client-management","role-management"],device:["usb-devices","device-groups"],management:{can_manage_users:!0,can_manage_devices:!0,can_view_reports:!0}},普通用户:{navigation:["dashboard","applications"],device:[],management:{can_manage_users:!1,can_manage_devices:!1,can_view_reports:!1}},新用户:{navigation:["dashboard","applications"],device:[],management:{can_manage_users:!1,can_manage_devices:!1,can_view_reports:!1}}}[s.name],g=Array.isArray(s.navigation_permissions)?s.navigation_permissions:[],V=Array.isArray(s.device_sub_permissions)?s.device_sub_permissions:[],C=g.length>0?g:t?t.navigation:[],h=V.length>0?V:t?t.device:[];console.log("权限配置详情:",{roleName:s.name,currentNav:g,currentDevice:V,finalNav:C,finalDevice:h});const D=t?t.management:{};Object.assign(n,{id:s.id,name:s.name,description:s.description,permissions:s.permissions||[],navigation_permissions:C,device_sub_permissions:h,level_scope:s.level_scope||1,can_manage_users:s.can_manage_users!==void 0?s.can_manage_users:D.can_manage_users,can_manage_devices:s.can_manage_devices!==void 0?s.can_manage_devices:D.can_manage_devices,can_view_reports:s.can_view_reports!==void 0?s.can_view_reports:D.can_view_reports}),console.log("编辑角色最终数据:",{roleName:s.name,template:t,finalForm:n}),$(),m.value=!1,N.value=!0}catch(e){console.error("获取角色详情失败:",e),p.error(`获取角色详情失败: ${e.message}`)}},re=a=>{E.value=a,T.value=!0},j=()=>{var a;Object.assign(n,{name:"",description:"",level_scope:0,permissions:[],navigation_permissions:[],device_sub_permissions:[],can_manage_users:!1,can_manage_devices:!1,can_view_reports:!1}),(a=I.value)==null||a.resetFields(),$(),m.value=!1},ce=()=>{m.value=!0,p.info("现在可以编辑权限配置")},de=()=>{m.value=!1,N.value=!1,j()},me=async()=>{try{await ve(),m.value=!1}catch(a){console.error("保存角色失败:",a)}},$=()=>{P.navigation_permissions=[...n.navigation_permissions],P.device_sub_permissions=[...n.device_sub_permissions]},_e=()=>{$(),p.success("权限配置已确认")},ue=()=>{n.navigation_permissions=[...P.navigation_permissions],n.device_sub_permissions=[...P.device_sub_permissions],p.info("权限配置已重置")},ve=async()=>{if(I.value)try{await I.value.validate(),L.value=!0;const a={name:n.name,description:n.description,permissions:n.permissions||[],navigation_permissions:n.navigation_permissions||[],device_sub_permissions:n.device_sub_permissions||[],level_scope:n.level_scope||1,can_manage_users:n.can_manage_users||!1,can_manage_devices:n.can_manage_devices||!1,can_view_reports:n.can_view_reports||!1};if(console.log("保存角色数据:",a),x.value){const e=await os(n.id,a);console.log("角色更新响应:",e),p.success("角色更新成功");try{await be(n.id,a),await new Promise(s=>setTimeout(s,100)),await ye(n.id),console.log("角色权限同步流程完成:",n.id)}catch(s){console.error("权限同步过程中出现错误:",s),p.warning("权限更新成功，但同步显示可能有延迟")}}else{const e=await is(a);console.log("角色创建响应:",e),p.success("角色创建成功")}await O(),N.value=!1}catch(a){p.error(x.value?"角色更新失败":"角色创建失败"),console.error("Save role error:",a)}finally{L.value=!1}},pe=async a=>{try{await as.confirm(`确定要删除角色 "${a.name}" 吗？此操作不可恢复。`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await ls(a.id),p.success("角色删除成功"),await O()}catch(e){e!=="cancel"&&(p.error("角色删除失败"),console.error("Delete role error:",e))}},ge=a=>a.name==="全域管理员",fe=a=>["全域管理员","超级管理员","管理员","普通用户","新用户"].includes(a.name),be=async(a,e)=>{try{typeof window<"u"&&window.dispatchEvent(new CustomEvent("rolePermissionUpdated",{detail:{roleId:a,roleName:e.name,permissions:{navigation:e.navigation_permissions||[],device:e.device_sub_permissions||[],management:{can_manage_users:e.can_manage_users,can_manage_devices:e.can_manage_devices,can_view_reports:e.can_view_reports}},timestamp:new Date().toISOString()}})),console.log("权限更新事件已发送:",a,e.name)}catch(s){console.error("发送权限更新事件失败:",s)}},ye=async a=>{try{if(!a)return;console.log("开始刷新角色权限:",a);const e=await ae(a);let s=null;if(e&&e.success&&e.data)s=e.data;else if(e&&e.id)s=e;else throw new Error("API响应格式异常");if(console.log("获取角色详情:",s),!s||!s.id)throw new Error("角色数据不完整");if(x.value&&n.id===a){const t={id:s.id,name:s.name||"",description:s.description||"",permissions:Array.isArray(s.permissions)?[...s.permissions]:[],navigation_permissions:Array.isArray(s.navigation_permissions)?[...s.navigation_permissions]:[],device_sub_permissions:Array.isArray(s.device_sub_permissions)?[...s.device_sub_permissions]:[],level_scope:s.level_scope||1,can_manage_users:!!s.can_manage_users,can_manage_devices:!!s.can_manage_devices,can_view_reports:!!s.can_view_reports};Object.keys(t).forEach(g=>{g in n&&(n[g]=t[g])}),P.navigation_permissions=[...t.navigation_permissions],P.device_sub_permissions=[...t.device_sub_permissions],console.log("表单数据已完全更新:",{roleId:a,roleName:n.name,navigationPermissions:n.navigation_permissions,devicePermissions:n.device_sub_permissions,managementPermissions:{can_manage_users:n.can_manage_users,can_manage_devices:n.can_manage_devices,can_view_reports:n.can_view_reports}})}const c=f.value.findIndex(t=>t.id===a);c!==-1&&(f.value[c]={...f.value[c],...s}),console.log("角色权限刷新完成:",a),p.success("权限数据已同步")}catch(e){console.error("刷新角色权限失败:",e),p.error(`刷新角色权限失败: ${e.message}`)}},we=()=>{typeof window<"u"&&(window.addEventListener("rolePermissionUpdated",a=>{const{roleId:e,roleName:s,permissions:c,timestamp:t}=a.detail;console.log("接收到权限更新事件:",{roleId:e,roleName:s,timestamp:t});const g=f.value.findIndex(V=>V.id===e);g!==-1&&(f.value[g]={...f.value[g],navigation_permissions:c.navigation,device_sub_permissions:c.device,...c.management}),x.value&&n.id===e&&Object.assign(n,{...n,navigation_permissions:c.navigation,device_sub_permissions:c.device,...c.management}),p.success(`角色"${s}"权限已同步更新`)}),window.addEventListener("devicePermissionChanged",a=>{const{userId:e,deviceId:s,action:c,timestamp:t}=a.detail;console.log("设备权限变更:",{userId:e,deviceId:s,action:c,timestamp:t}),O()}))},ke=()=>{typeof window<"u"&&(window.removeEventListener("rolePermissionUpdated",()=>{}),window.removeEventListener("devicePermissionChanged",()=>{}))};return he(()=>{O(),we()}),xe(()=>{ke()}),(a,e)=>{const s=Ne,c=Ie,t=Be,g=Ue,V=ze,C=Te,h=Je,D=je,U=Me,J=qe,q=Ke,Ee=Le,W=De,F=ss,Pe=es,Ve=Se;return d(),R("div",ts,[u("div",rs,[e[9]||(e[9]=u("div",{class:"header-content"},[u("h2",null,"角色管理"),u("p",null,"管理系统角色和权限分配，仅超级管理员可访问")],-1)),u("div",cs,[o(s,{type:"primary",onClick:le,icon:A(Re)},{default:i(()=>e[8]||(e[8]=[r(" 创建角色 ",-1)])),_:1,__:[8]},8,["icon"])])]),u("div",ds,[Ae((d(),v(g,{data:f.value,style:{width:"100%"}},{default:i(()=>[o(t,{prop:"name",label:"角色名称",width:"150"},{default:i(({row:l})=>[u("div",ms,[l.is_system_role?(d(),v(c,{key:0,type:"danger",size:"small"},{default:i(()=>e[10]||(e[10]=[r("系统",-1)])),_:1,__:[10]})):b("",!0),r(" "+w(l.name),1)])]),_:1}),o(t,{prop:"description",label:"角色描述",width:"140"}),o(t,{label:"权限范围",width:"100"},{default:i(({row:l})=>[l.level_scope===0?(d(),v(c,{key:0,type:"warning"},{default:i(()=>e[11]||(e[11]=[r("无限制",-1)])),_:1,__:[11]})):(d(),v(c,{key:1,type:"info"},{default:i(()=>[r(w(l.level_scope)+"级",1)]),_:2},1024))]),_:1}),o(t,{label:"状态",width:"80"},{default:i(({row:l})=>[o(c,{type:l.is_active?"success":"danger"},{default:i(()=>[r(w(l.is_active?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),o(t,{label:"操作","min-width":"250",fixed:"right"},{default:i(({row:l})=>[o(s,{size:"small",onClick:Y=>re(l),icon:A(Oe)},{default:i(()=>e[12]||(e[12]=[r(" 查看 ",-1)])),_:2,__:[12]},1032,["onClick","icon"]),ge(l)?b("",!0):(d(),v(s,{key:0,size:"small",type:"primary",onClick:Y=>te(l),icon:A($e)},{default:i(()=>e[13]||(e[13]=[r(" 编辑 ",-1)])),_:2,__:[13]},1032,["onClick","icon"])),fe(l)?b("",!0):(d(),v(s,{key:1,size:"small",type:"danger",onClick:Y=>pe(l),icon:A(Fe)},{default:i(()=>e[14]||(e[14]=[r(" 删除 ",-1)])),_:2,__:[14]},1032,["onClick","icon"]))]),_:1})]),_:1},8,["data"])),[[Ve,S.value]])]),o(W,{modelValue:N.value,"onUpdate:modelValue":e[6]||(e[6]=l=>N.value=l),title:x.value?"编辑角色":"创建角色",width:"900px",onClose:j},{footer:i(()=>[u("span",fs,[m.value?b("",!0):(d(),v(s,{key:0,onClick:ce},{default:i(()=>e[22]||(e[22]=[r("开始修改",-1)])),_:1,__:[22]})),m.value?(d(),v(s,{key:1,onClick:de},{default:i(()=>e[23]||(e[23]=[r("取消修改",-1)])),_:1,__:[23]})):b("",!0),m.value?(d(),v(s,{key:2,type:"primary",onClick:me,loading:L.value},{default:i(()=>e[24]||(e[24]=[r(" 确认保存 ",-1)])),_:1,__:[24]},8,["loading"])):b("",!0)])]),default:i(()=>[o(Ee,{model:n,rules:ne,ref_key:"roleFormRef",ref:I,"label-width":"120px"},{default:i(()=>[o(C,{label:"角色名称",prop:"name"},{default:i(()=>[o(V,{modelValue:n.name,"onUpdate:modelValue":e[0]||(e[0]=l=>n.name=l),placeholder:"请输入角色名称"},null,8,["modelValue"])]),_:1}),o(C,{label:"角色描述",prop:"description"},{default:i(()=>[o(V,{modelValue:n.description,"onUpdate:modelValue":e[1]||(e[1]=l=>n.description=l),type:"textarea",rows:3,placeholder:"请输入角色描述"},null,8,["modelValue"])]),_:1}),o(C,{label:"权限层级"},{default:i(()=>[o(D,{modelValue:n.level_scope,"onUpdate:modelValue":e[2]||(e[2]=l=>n.level_scope=l),placeholder:"选择权限层级范围"},{default:i(()=>[o(h,{label:"无限制",value:0}),o(h,{label:"1级权限",value:1}),o(h,{label:"2级权限",value:2}),o(h,{label:"3级权限",value:3}),o(h,{label:"4级权限",value:4})]),_:1},8,["modelValue"])]),_:1}),o(C,{label:"管理权限"},{default:i(()=>[o(J,{modelValue:X.value,"onUpdate:modelValue":e[3]||(e[3]=l=>X.value=l)},{default:i(()=>[o(U,{label:"can_manage_users"},{default:i(()=>e[15]||(e[15]=[r("用户管理",-1)])),_:1,__:[15]}),o(U,{label:"can_manage_devices"},{default:i(()=>e[16]||(e[16]=[r("设备管理",-1)])),_:1,__:[16]}),o(U,{label:"can_view_reports"},{default:i(()=>e[17]||(e[17]=[r("报告查看",-1)])),_:1,__:[17]})]),_:1},8,["modelValue"])]),_:1}),o(C,{label:"功能权限"},{default:i(()=>[u("div",_s,[e[21]||(e[21]=u("h4",null,"一级权限（导航菜单）",-1)),o(J,{modelValue:n.navigation_permissions,"onUpdate:modelValue":e[4]||(e[4]=l=>n.navigation_permissions=l),class:"navigation-group-grid",disabled:_.value||!m.value},{default:i(()=>[(d(),R(H,null,K(ie,l=>o(U,{key:l.key,label:l.key,disabled:_.value||!m.value,class:G(["nav-permission-item-grid",{"global-admin-item":_.value,"is-disabled":_.value}])},{default:i(()=>[u("div",us,[o(q,{class:G(["nav-icon",{"global-admin-icon":_.value}])},{default:i(()=>[(d(),v(Qe(l.icon)))]),_:2},1032,["class"]),u("span",null,w(l.name),1),_.value?(d(),v(q,{key:0,class:"admin-badge"},{default:i(()=>[o(A(se))]),_:1})):b("",!0)])]),_:2},1032,["label","disabled","class"])),64))]),_:1},8,["modelValue","disabled"]),n.navigation_permissions.includes("device-center")||_.value?(d(),R("div",vs,[e[18]||(e[18]=u("h4",null,"设备管理中心 - 二级权限",-1)),o(J,{modelValue:n.device_sub_permissions,"onUpdate:modelValue":e[5]||(e[5]=l=>n.device_sub_permissions=l),class:"sub-permission-group",disabled:_.value||!m.value},{default:i(()=>[(d(),R(H,null,K(oe,l=>o(U,{key:l.key,label:l.key,disabled:_.value||!m.value,class:G(["sub-permission-item",{"global-admin-item":_.value,"is-disabled":_.value||!m.value}])},{default:i(()=>[u("span",null,w(l.name),1),_.value?(d(),v(q,{key:0,class:"admin-badge"},{default:i(()=>[o(A(se))]),_:1})):b("",!0)]),_:2},1032,["label","disabled","class"])),64))]),_:1},8,["modelValue","disabled"])])):b("",!0),!_.value&&m.value?(d(),R("div",ps,[o(s,{type:"success",onClick:_e,disabled:!z.value,size:"small"},{default:i(()=>e[19]||(e[19]=[r(" 确定修改 ",-1)])),_:1,__:[19]},8,["disabled"]),o(s,{onClick:ue,disabled:!z.value,size:"small"},{default:i(()=>e[20]||(e[20]=[r(" 放弃修改 ",-1)])),_:1,__:[20]},8,["disabled"]),z.value?(d(),R("span",gs," 权限配置已修改 ")):b("",!0)])):b("",!0)])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),o(W,{modelValue:T.value,"onUpdate:modelValue":e[7]||(e[7]=l=>T.value=l),title:"角色详情",width:"500px"},{default:i(()=>[E.value?(d(),R("div",bs,[o(Pe,{column:1,border:""},{default:i(()=>[o(F,{label:"角色名称"},{default:i(()=>[r(w(E.value.name),1)]),_:1}),o(F,{label:"角色描述"},{default:i(()=>[r(w(E.value.description),1)]),_:1}),o(F,{label:"系统角色"},{default:i(()=>[o(c,{type:E.value.is_system_role?"danger":"success"},{default:i(()=>[r(w(E.value.is_system_role?"是":"否"),1)]),_:1},8,["type"])]),_:1}),o(F,{label:"权限层级"},{default:i(()=>[E.value.level_scope===0?(d(),v(c,{key:0,type:"warning"},{default:i(()=>e[25]||(e[25]=[r("无限制",-1)])),_:1,__:[25]})):(d(),v(c,{key:1,type:"info"},{default:i(()=>[r(w(E.value.level_scope)+"级",1)]),_:1}))]),_:1})]),_:1})])):b("",!0)]),_:1},8,["modelValue"])])}}},Is=Ce(ys,[["__scopeId","data-v-23105915"]]);export{Is as default};
