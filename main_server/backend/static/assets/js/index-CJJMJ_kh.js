import{_ as Ve}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                   *//* empty css                             *//* empty css                   *//* empty css                     *//* empty css                          *//* empty css                    *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  */import{ds as A,r as w,a as Y,a9 as q,o as Ce,bN as he,c as x,d as u,e as o,w as i,p as N,cR as xe,m as Re,ab as Ne,ac as Se,y as v,x as De,s as p,ah as Ue,i as c,n as r,ak as Ae,z as b,a7 as Be,t as k,cW as Ie,cV as Oe,cX as Fe,f as Le,j as $e,k as Te,ai as ze,aj as je,cN as Je,cO as qe,a5 as Z,a6 as H,X as Me,Y as Ge,Z as Xe,_ as K,L as Q,$ as We,a0 as Ye,R as Ze,a2 as M,E as He,b_ as Ke,dl as ee,af as Qe,ae as es,a4 as ss}from"./index-CwbwN4Sv.js";function as(){return A.get("/api/v2/roles/")}function ns(E){return A.get(`/api/v2/roles/${E}`)}function is(E){return A.post("/api/v2/roles/",E)}function os(E,S){return A.put(`/api/v2/roles/${E}`,S)}function ts(E){return A.delete(`/api/v2/roles/${E}`)}const ls={class:"role-management"},rs={class:"page-header"},cs={class:"header-actions"},ds={class:"role-list"},ms={class:"role-name"},_s={class:"navigation-permissions"},us={class:"nav-permission-content"},vs={key:0,class:"sub-permissions"},ps={key:1,class:"permission-actions"},gs={key:0,class:"change-indicator"},fs={class:"dialog-footer"},bs={key:0,class:"role-detail"},ys={__name:"index",setup(E){const S=w(!1),L=w(!1),R=w(!1),$=w(!1),C=w(!1),m=w(!1),g=w([]),P=w(null),B=w(),a=Y({name:"",description:"",level_scope:0,permissions:[],navigation_permissions:[],device_sub_permissions:[],can_manage_users:!1,can_manage_devices:!1,can_view_reports:!1}),V=Y({navigation_permissions:[],device_sub_permissions:[]}),G=q({get(){const s=[];return a.can_manage_users&&s.push("can_manage_users"),a.can_manage_devices&&s.push("can_manage_devices"),a.can_view_reports&&s.push("can_view_reports"),s},set(s){a.can_manage_users=s.includes("can_manage_users"),a.can_manage_devices=s.includes("can_manage_devices"),a.can_view_reports=s.includes("can_view_reports")}}),_=q(()=>a.name==="全域管理员"),T=q(()=>{if(_.value)return!1;const s=JSON.stringify(a.navigation_permissions.sort())!==JSON.stringify(V.navigation_permissions.sort()),e=JSON.stringify(a.device_sub_permissions.sort())!==JSON.stringify(V.device_sub_permissions.sort());return s||e}),se={name:[{required:!0,message:"请输入角色名称",trigger:"blur"},{min:2,max:50,message:"角色名称长度在 2 到 50 个字符",trigger:"blur"}],description:[{required:!0,message:"请输入角色描述",trigger:"blur"}]},ae=[{key:"dashboard",name:"工作台",icon:Me},{key:"applications",name:"处理事项",icon:Ge},{key:"org-users",name:"组织与用户管理",icon:Xe},{key:"user-registration",name:"新用户审核",icon:K},{key:"device-center",name:"设备管理中心",icon:Q},{key:"client-management",name:"设备绑定中心",icon:Q},{key:"role-management",name:"角色管理",icon:K},{key:"system-settings",name:"系统设置",icon:We},{key:"data-dashboard",name:"数据大屏",icon:Ye},{key:"profile-management",name:"个人资料管理",icon:Ze}],ne=[{key:"usb-devices",name:"USB设备管理"},{key:"slave-servers",name:"分布式节点管理"},{key:"device-groups",name:"资源调度分组"},{key:"permission-assignment",name:"授权范围管理"}],I=async()=>{S.value=!0;try{const s=await as();let e=s;s&&s.success&&s.data&&(console.log("🔧 loadRoles - 检测到API中间件包装格式，提取data字段"),e=s.data),console.log("loadRoles - 处理后的角色数据:",e),e&&e.roles?(g.value=Array.isArray(e.roles)?e.roles:[],console.log("加载角色列表成功:",g.value.length,"个角色"),e.filtered_by_permission&&console.log("权限过滤已生效")):Array.isArray(e)?(g.value=e,console.log("加载角色列表成功（数组格式）:",g.value.length,"个角色")):(g.value=[],console.log("角色数据格式异常，设置为空数组"))}catch(s){p.error("加载角色列表失败"),console.error("Load roles error:",s)}finally{S.value=!1}},ie=()=>{C.value=!1,z(),O(),R.value=!0},oe=s=>{C.value=!0,console.log("编辑角色原始数据:",s);const n={全域管理员:{navigation:["dashboard","applications","org-users","user-registration","device-center","client-management","role-management","system-settings","data-dashboard"],device:["usb-devices","slave-servers","device-groups","permission-assignment"],management:{can_manage_users:!0,can_manage_devices:!0,can_view_reports:!0}},超级管理员:{navigation:["dashboard","applications","org-users","user-registration","device-center","client-management","role-management","system-settings"],device:["usb-devices","slave-servers","device-groups","permission-assignment"],management:{can_manage_users:!0,can_manage_devices:!0,can_view_reports:!0}},管理员:{navigation:["dashboard","applications","org-users","user-registration","device-center","client-management"],device:["usb-devices","device-groups"],management:{can_manage_users:!0,can_manage_devices:!0,can_view_reports:!0}},普通用户:{navigation:["dashboard","applications"],device:[],management:{can_manage_users:!1,can_manage_devices:!1,can_view_reports:!1}},新用户:{navigation:["dashboard","applications"],device:[],management:{can_manage_users:!1,can_manage_devices:!1,can_view_reports:!1}}}[s.name],l=s.navigation_permissions||[],d=s.device_sub_permissions||[],f=l.length>0?l:n?n.navigation:[],h=d.length>0?d:n?n.device:[];console.log("权限配置详情:",{roleName:s.name,currentNav:l,currentDevice:d,finalNav:f,finalDevice:h});const y=n?n.management:{};Object.assign(a,{id:s.id,name:s.name,description:s.description,permissions:s.permissions||[],navigation_permissions:f,device_sub_permissions:h,level_scope:s.level_scope||1,can_manage_users:s.can_manage_users!==void 0?s.can_manage_users:y.can_manage_users,can_manage_devices:s.can_manage_devices!==void 0?s.can_manage_devices:y.can_manage_devices,can_view_reports:s.can_view_reports!==void 0?s.can_view_reports:y.can_view_reports}),console.log("编辑角色最终数据:",{roleName:s.name,template:n,finalForm:a}),O(),m.value=!1,R.value=!0},te=s=>{P.value=s,$.value=!0},z=()=>{var s;Object.assign(a,{name:"",description:"",level_scope:0,permissions:[],navigation_permissions:[],device_sub_permissions:[],can_manage_users:!1,can_manage_devices:!1,can_view_reports:!1}),(s=B.value)==null||s.resetFields(),O(),m.value=!1},le=()=>{m.value=!0,p.info("现在可以编辑权限配置")},re=()=>{m.value=!1,R.value=!1,z()},ce=async()=>{try{await _e(),m.value=!1}catch(s){console.error("保存角色失败:",s)}},O=()=>{V.navigation_permissions=[...a.navigation_permissions],V.device_sub_permissions=[...a.device_sub_permissions]},de=()=>{O(),p.success("权限配置已确认")},me=()=>{a.navigation_permissions=[...V.navigation_permissions],a.device_sub_permissions=[...V.device_sub_permissions],p.info("权限配置已重置")},_e=async()=>{if(B.value)try{await B.value.validate(),L.value=!0;const s={name:a.name,description:a.description,permissions:a.permissions||[],navigation_permissions:a.navigation_permissions||[],device_sub_permissions:a.device_sub_permissions||[],level_scope:a.level_scope||1,can_manage_users:a.can_manage_users||!1,can_manage_devices:a.can_manage_devices||!1,can_view_reports:a.can_view_reports||!1};if(console.log("保存角色数据:",s),C.value){const e=await os(a.id,s);console.log("角色更新响应:",e),p.success("角色更新成功");try{await ge(a.id,s),await new Promise(n=>setTimeout(n,100)),await fe(a.id),console.log("角色权限同步流程完成:",a.id)}catch(n){console.error("权限同步过程中出现错误:",n),p.warning("权限更新成功，但同步显示可能有延迟")}}else{const e=await is(s);console.log("角色创建响应:",e),p.success("角色创建成功")}await I(),R.value=!1}catch(s){p.error(C.value?"角色更新失败":"角色创建失败"),console.error("Save role error:",s)}finally{L.value=!1}},ue=async s=>{try{await ss.confirm(`确定要删除角色 "${s.name}" 吗？此操作不可恢复。`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await ts(s.id),p.success("角色删除成功"),await I()}catch(e){e!=="cancel"&&(p.error("角色删除失败"),console.error("Delete role error:",e))}},ve=s=>s.name==="全域管理员",pe=s=>["全域管理员","超级管理员","管理员","普通用户","新用户"].includes(s.name),ge=async(s,e)=>{try{typeof window<"u"&&window.dispatchEvent(new CustomEvent("rolePermissionUpdated",{detail:{roleId:s,roleName:e.name,permissions:{navigation:e.navigation_permissions||[],device:e.device_sub_permissions||[],management:{can_manage_users:e.can_manage_users,can_manage_devices:e.can_manage_devices,can_view_reports:e.can_view_reports}},timestamp:new Date().toISOString()}})),console.log("权限更新事件已发送:",s,e.name)}catch(n){console.error("发送权限更新事件失败:",n)}},fe=async s=>{try{if(!s)return;console.log("开始刷新角色权限:",s);const e=await ns(s);let n=null;if(e&&e.success&&e.data)n=e.data;else if(e&&e.id)n=e;else throw new Error("API响应格式异常");if(console.log("获取角色详情:",n),!n||!n.id)throw new Error("角色数据不完整");if(C.value&&a.id===s){const d={id:n.id,name:n.name||"",description:n.description||"",permissions:Array.isArray(n.permissions)?[...n.permissions]:[],navigation_permissions:Array.isArray(n.navigation_permissions)?[...n.navigation_permissions]:[],device_sub_permissions:Array.isArray(n.device_sub_permissions)?[...n.device_sub_permissions]:[],level_scope:n.level_scope||1,can_manage_users:!!n.can_manage_users,can_manage_devices:!!n.can_manage_devices,can_view_reports:!!n.can_view_reports};Object.keys(d).forEach(f=>{f in a&&(a[f]=d[f])}),V.navigation_permissions=[...d.navigation_permissions],V.device_sub_permissions=[...d.device_sub_permissions],console.log("表单数据已完全更新:",{roleId:s,roleName:a.name,navigationPermissions:a.navigation_permissions,devicePermissions:a.device_sub_permissions,managementPermissions:{can_manage_users:a.can_manage_users,can_manage_devices:a.can_manage_devices,can_view_reports:a.can_view_reports}})}const l=g.value.findIndex(d=>d.id===s);l!==-1&&(g.value[l]={...g.value[l],...n}),console.log("角色权限刷新完成:",s),p.success("权限数据已同步")}catch(e){console.error("刷新角色权限失败:",e),p.error(`刷新角色权限失败: ${e.message}`)}},be=()=>{typeof window<"u"&&(window.addEventListener("rolePermissionUpdated",s=>{const{roleId:e,roleName:n,permissions:l,timestamp:d}=s.detail;console.log("接收到权限更新事件:",{roleId:e,roleName:n,timestamp:d});const f=g.value.findIndex(h=>h.id===e);f!==-1&&(g.value[f]={...g.value[f],navigation_permissions:l.navigation,device_sub_permissions:l.device,...l.management}),C.value&&a.id===e&&Object.assign(a,{...a,navigation_permissions:l.navigation,device_sub_permissions:l.device,...l.management}),p.success(`角色"${n}"权限已同步更新`)}),window.addEventListener("devicePermissionChanged",s=>{const{userId:e,deviceId:n,action:l,timestamp:d}=s.detail;console.log("设备权限变更:",{userId:e,deviceId:n,action:l,timestamp:d}),I()}))},ye=()=>{typeof window<"u"&&(window.removeEventListener("rolePermissionUpdated",()=>{}),window.removeEventListener("devicePermissionChanged",()=>{}))};return Ce(()=>{I(),be()}),he(()=>{ye()}),(s,e)=>{const n=Re,l=Be,d=Ae,f=Ue,h=Te,y=$e,D=je,we=ze,U=qe,j=Je,J=He,ke=Le,X=De,F=es,Ee=Qe,Pe=Se;return c(),x("div",ls,[u("div",rs,[e[9]||(e[9]=u("div",{class:"header-content"},[u("h2",null,"角色管理"),u("p",null,"管理系统角色和权限分配，仅超级管理员可访问")],-1)),u("div",cs,[o(n,{type:"primary",onClick:ie,icon:N(xe)},{default:i(()=>e[8]||(e[8]=[r(" 创建角色 ",-1)])),_:1,__:[8]},8,["icon"])])]),u("div",ds,[Ne((c(),v(f,{data:g.value,style:{width:"100%"}},{default:i(()=>[o(d,{prop:"name",label:"角色名称",width:"150"},{default:i(({row:t})=>[u("div",ms,[t.is_system_role?(c(),v(l,{key:0,type:"danger",size:"small"},{default:i(()=>e[10]||(e[10]=[r("系统",-1)])),_:1,__:[10]})):b("",!0),r(" "+k(t.name),1)])]),_:1}),o(d,{prop:"description",label:"角色描述",width:"140"}),o(d,{label:"权限范围",width:"100"},{default:i(({row:t})=>[t.level_scope===0?(c(),v(l,{key:0,type:"warning"},{default:i(()=>e[11]||(e[11]=[r("无限制",-1)])),_:1,__:[11]})):(c(),v(l,{key:1,type:"info"},{default:i(()=>[r(k(t.level_scope)+"级",1)]),_:2},1024))]),_:1}),o(d,{label:"状态",width:"80"},{default:i(({row:t})=>[o(l,{type:t.is_active?"success":"danger"},{default:i(()=>[r(k(t.is_active?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),o(d,{label:"操作","min-width":"250",fixed:"right"},{default:i(({row:t})=>[o(n,{size:"small",onClick:W=>te(t),icon:N(Ie)},{default:i(()=>e[12]||(e[12]=[r(" 查看 ",-1)])),_:2,__:[12]},1032,["onClick","icon"]),ve(t)?b("",!0):(c(),v(n,{key:0,size:"small",type:"primary",onClick:W=>oe(t),icon:N(Oe)},{default:i(()=>e[13]||(e[13]=[r(" 编辑 ",-1)])),_:2,__:[13]},1032,["onClick","icon"])),pe(t)?b("",!0):(c(),v(n,{key:1,size:"small",type:"danger",onClick:W=>ue(t),icon:N(Fe)},{default:i(()=>e[14]||(e[14]=[r(" 删除 ",-1)])),_:2,__:[14]},1032,["onClick","icon"]))]),_:1})]),_:1},8,["data"])),[[Pe,S.value]])]),o(X,{modelValue:R.value,"onUpdate:modelValue":e[6]||(e[6]=t=>R.value=t),title:C.value?"编辑角色":"创建角色",width:"900px",onClose:z},{footer:i(()=>[u("span",fs,[m.value?b("",!0):(c(),v(n,{key:0,onClick:le},{default:i(()=>e[22]||(e[22]=[r("开始修改",-1)])),_:1,__:[22]})),m.value?(c(),v(n,{key:1,onClick:re},{default:i(()=>e[23]||(e[23]=[r("取消修改",-1)])),_:1,__:[23]})):b("",!0),m.value?(c(),v(n,{key:2,type:"primary",onClick:ce,loading:L.value},{default:i(()=>e[24]||(e[24]=[r(" 确认保存 ",-1)])),_:1,__:[24]},8,["loading"])):b("",!0)])]),default:i(()=>[o(ke,{model:a,rules:se,ref_key:"roleFormRef",ref:B,"label-width":"120px"},{default:i(()=>[o(y,{label:"角色名称",prop:"name"},{default:i(()=>[o(h,{modelValue:a.name,"onUpdate:modelValue":e[0]||(e[0]=t=>a.name=t),placeholder:"请输入角色名称"},null,8,["modelValue"])]),_:1}),o(y,{label:"角色描述",prop:"description"},{default:i(()=>[o(h,{modelValue:a.description,"onUpdate:modelValue":e[1]||(e[1]=t=>a.description=t),type:"textarea",rows:3,placeholder:"请输入角色描述"},null,8,["modelValue"])]),_:1}),o(y,{label:"权限层级"},{default:i(()=>[o(we,{modelValue:a.level_scope,"onUpdate:modelValue":e[2]||(e[2]=t=>a.level_scope=t),placeholder:"选择权限层级范围"},{default:i(()=>[o(D,{label:"无限制",value:0}),o(D,{label:"1级权限",value:1}),o(D,{label:"2级权限",value:2}),o(D,{label:"3级权限",value:3}),o(D,{label:"4级权限",value:4})]),_:1},8,["modelValue"])]),_:1}),o(y,{label:"管理权限"},{default:i(()=>[o(j,{modelValue:G.value,"onUpdate:modelValue":e[3]||(e[3]=t=>G.value=t)},{default:i(()=>[o(U,{label:"can_manage_users"},{default:i(()=>e[15]||(e[15]=[r("用户管理",-1)])),_:1,__:[15]}),o(U,{label:"can_manage_devices"},{default:i(()=>e[16]||(e[16]=[r("设备管理",-1)])),_:1,__:[16]}),o(U,{label:"can_view_reports"},{default:i(()=>e[17]||(e[17]=[r("报告查看",-1)])),_:1,__:[17]})]),_:1},8,["modelValue"])]),_:1}),o(y,{label:"功能权限"},{default:i(()=>[u("div",_s,[e[21]||(e[21]=u("h4",null,"一级权限（导航菜单）",-1)),o(j,{modelValue:a.navigation_permissions,"onUpdate:modelValue":e[4]||(e[4]=t=>a.navigation_permissions=t),class:"navigation-group-grid",disabled:_.value||!m.value},{default:i(()=>[(c(),x(Z,null,H(ae,t=>o(U,{key:t.key,label:t.key,disabled:_.value||!m.value,class:M(["nav-permission-item-grid",{"global-admin-item":_.value,"is-disabled":_.value}])},{default:i(()=>[u("div",us,[o(J,{class:M(["nav-icon",{"global-admin-icon":_.value}])},{default:i(()=>[(c(),v(Ke(t.icon)))]),_:2},1032,["class"]),u("span",null,k(t.name),1),_.value?(c(),v(J,{key:0,class:"admin-badge"},{default:i(()=>[o(N(ee))]),_:1})):b("",!0)])]),_:2},1032,["label","disabled","class"])),64))]),_:1},8,["modelValue","disabled"]),a.navigation_permissions.includes("device-center")||_.value?(c(),x("div",vs,[e[18]||(e[18]=u("h4",null,"设备管理中心 - 二级权限",-1)),o(j,{modelValue:a.device_sub_permissions,"onUpdate:modelValue":e[5]||(e[5]=t=>a.device_sub_permissions=t),class:"sub-permission-group",disabled:_.value||!m.value},{default:i(()=>[(c(),x(Z,null,H(ne,t=>o(U,{key:t.key,label:t.key,disabled:_.value||!m.value,class:M(["sub-permission-item",{"global-admin-item":_.value,"is-disabled":_.value||!m.value}])},{default:i(()=>[u("span",null,k(t.name),1),_.value?(c(),v(J,{key:0,class:"admin-badge"},{default:i(()=>[o(N(ee))]),_:1})):b("",!0)]),_:2},1032,["label","disabled","class"])),64))]),_:1},8,["modelValue","disabled"])])):b("",!0),!_.value&&m.value?(c(),x("div",ps,[o(n,{type:"success",onClick:de,disabled:!T.value,size:"small"},{default:i(()=>e[19]||(e[19]=[r(" 确定修改 ",-1)])),_:1,__:[19]},8,["disabled"]),o(n,{onClick:me,disabled:!T.value,size:"small"},{default:i(()=>e[20]||(e[20]=[r(" 放弃修改 ",-1)])),_:1,__:[20]},8,["disabled"]),T.value?(c(),x("span",gs," 权限配置已修改 ")):b("",!0)])):b("",!0)])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),o(X,{modelValue:$.value,"onUpdate:modelValue":e[7]||(e[7]=t=>$.value=t),title:"角色详情",width:"500px"},{default:i(()=>[P.value?(c(),x("div",bs,[o(Ee,{column:1,border:""},{default:i(()=>[o(F,{label:"角色名称"},{default:i(()=>[r(k(P.value.name),1)]),_:1}),o(F,{label:"角色描述"},{default:i(()=>[r(k(P.value.description),1)]),_:1}),o(F,{label:"系统角色"},{default:i(()=>[o(l,{type:P.value.is_system_role?"danger":"success"},{default:i(()=>[r(k(P.value.is_system_role?"是":"否"),1)]),_:1},8,["type"])]),_:1}),o(F,{label:"权限层级"},{default:i(()=>[P.value.level_scope===0?(c(),v(l,{key:0,type:"warning"},{default:i(()=>e[25]||(e[25]=[r("无限制",-1)])),_:1,__:[25]})):(c(),v(l,{key:1,type:"info"},{default:i(()=>[r(k(P.value.level_scope)+"级",1)]),_:1}))]),_:1})]),_:1})])):b("",!0)]),_:1},8,["modelValue"])])}}},Is=Ve(ys,[["__scopeId","data-v-53e5aad8"]]);export{Is as default};
