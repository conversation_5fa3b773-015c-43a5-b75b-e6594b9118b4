import{_ as o}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                 */import{c as i,d as e,e as t,w as a,cY as d,i as c,E as r,p as _,a0 as p}from"./index-BBw1jc_Y.js";const u={class:"data-dashboard"},m={class:"dashboard-content"},f={__name:"index",setup(h){return(v,s)=>{const l=r,n=d;return c(),i("div",u,[s[1]||(s[1]=e("div",{class:"page-header"},[e("div",{class:"header-content"},[e("h2",null,"数据大屏"),e("p",null,"系统数据可视化展示中心")])],-1)),e("div",m,[t(n,{"image-size":200,description:"数据大屏功能正在开发中，敬请期待"},{image:a(()=>[t(l,{size:"200",color:"#409eff"},{default:a(()=>[t(_(p))]),_:1})]),description:a(()=>s[0]||(s[0]=[e("div",{class:"empty-description"},[e("h3",null,"数据大屏功能正在开发中"),e("p",null,"即将为您提供丰富的数据可视化功能"),e("ul",{class:"feature-list"},[e("li",null,"实时设备状态监控"),e("li",null,"用户活动统计分析"),e("li",null,"系统性能指标展示"),e("li",null,"权限使用情况统计")])],-1)])),_:1})])])}}},b=o(f,[["__scopeId","data-v-9ff03b6d"]]);export{b as default};
