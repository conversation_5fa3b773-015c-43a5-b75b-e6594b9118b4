import{_ as $}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                     *//* empty css               *//* empty css                  */import{a as h}from"./users-mn6Jm0Wz.js";import{r as m,o as B,c as f,e,w as a,ai as z,s as i,i as _,ac as T,af as D,a5 as N,a6 as V,y as I,a7 as M,n,t as b,m as U,d as v}from"./index-Cp0qD91m.js";const A={class:"users-container"},F={class:"card-header"},L={__name:"index",setup(S){const p=m([]),c=m(!1),y=async()=>{c.value=!0;try{const o=await h();p.value=o.users||[]}catch{i.error("加载用户列表失败")}finally{c.value=!1}},w=()=>{i.info("添加用户功能开发中...")},g=o=>{i.info(`编辑用户: ${o.full_name}`)},k=o=>{i.info(`删除用户: ${o.full_name}`)};return B(()=>{y()}),(o,t)=>{const d=U,l=D,u=M,C=T,E=z;return _(),f("div",A,[e(E,null,{header:a(()=>[v("div",F,[t[1]||(t[1]=v("span",null,"用户管理",-1)),e(d,{type:"primary",onClick:w},{default:a(()=>t[0]||(t[0]=[n("添加用户",-1)])),_:1,__:[0]})])]),default:a(()=>[e(C,{data:p.value,style:{width:"100%"}},{default:a(()=>[e(l,{prop:"id",label:"用户ID",width:"80"}),e(l,{prop:"username",label:"用户名"}),e(l,{prop:"full_name",label:"姓名"}),e(l,{prop:"email",label:"邮箱"}),e(l,{prop:"organization",label:"所属组织"}),e(l,{prop:"roles",label:"角色"},{default:a(s=>[(_(!0),f(N,null,V(s.row.roles,(r,x)=>(_(),I(u,{key:`role-${x}-${r}`,size:"small"},{default:a(()=>[n(b(r),1)]),_:2},1024))),128))]),_:1}),e(l,{prop:"is_active",label:"状态"},{default:a(s=>[e(u,{type:s.row.is_active?"success":"danger"},{default:a(()=>[n(b(s.row.is_active?"激活":"禁用"),1)]),_:2},1032,["type"])]),_:1}),e(l,{label:"操作",width:"200"},{default:a(s=>[e(d,{size:"small",onClick:r=>g(s.row)},{default:a(()=>t[2]||(t[2]=[n("编辑",-1)])),_:2,__:[2]},1032,["onClick"]),e(d,{size:"small",type:"danger",onClick:r=>k(s.row)},{default:a(()=>t[3]||(t[3]=[n("删除",-1)])),_:2,__:[3]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})])}}},W=$(L,[["__scopeId","data-v-04002dd8"]]);export{W as default};
