import{_ as Ce}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                   *//* empty css                             *//* empty css                   *//* empty css                     *//* empty css                          *//* empty css                    *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  */import{ds as B,r as k,a as Z,a9 as <PERSON>,o as he,bN as xe,c as N,d as u,e as i,w as n,p as S,cR as Re,m as Ne,ab as Ae,ac as Se,y as v,x as De,s as g,ah as Ue,i as c,n as r,ak as Be,z as b,a7 as Ie,t as E,cW as Oe,cV as Fe,cX as Le,f as $e,j as Te,k as ze,ai as je,aj as Je,cN as qe,cO as Me,a5 as H,a6 as K,X as Ge,Y as Xe,Z as We,_ as Q,L as ee,$ as Ye,a0 as Ze,R as He,a2 as G,E as Ke,b_ as Qe,dl as se,af as es,ae as ss,a4 as as}from"./index-BALd70Fs.js";function ns(){return B.get("/api/v2/roles/")}function ae(P){return B.get(`/api/v2/roles/${P}`)}function is(P){return B.post("/api/v2/roles/",P)}function os(P,D){return B.put(`/api/v2/roles/${P}`,D)}function ts(P){return B.delete(`/api/v2/roles/${P}`)}const ls={class:"role-management"},rs={class:"page-header"},cs={class:"header-actions"},ds={class:"role-list"},ms={class:"role-name"},_s={class:"navigation-permissions"},us={class:"nav-permission-content"},vs={key:0,class:"sub-permissions"},ps={key:1,class:"permission-actions"},gs={key:0,class:"change-indicator"},fs={class:"dialog-footer"},bs={key:0,class:"role-detail"},ys={__name:"index",setup(P){const D=k(!1),$=k(!1),A=k(!1),T=k(!1),x=k(!1),m=k(!1),f=k([]),V=k(null),I=k(),a=Z({name:"",description:"",level_scope:0,permissions:[],navigation_permissions:[],device_sub_permissions:[],can_manage_users:!1,can_manage_devices:!1,can_view_reports:!1}),C=Z({navigation_permissions:[],device_sub_permissions:[]}),X=M({get(){const s=[];return a.can_manage_users&&s.push("can_manage_users"),a.can_manage_devices&&s.push("can_manage_devices"),a.can_view_reports&&s.push("can_view_reports"),s},set(s){a.can_manage_users=s.includes("can_manage_users"),a.can_manage_devices=s.includes("can_manage_devices"),a.can_view_reports=s.includes("can_view_reports")}}),_=M(()=>a.name==="全域管理员"),z=M(()=>{if(_.value)return!1;const s=JSON.stringify(a.navigation_permissions.sort())!==JSON.stringify(C.navigation_permissions.sort()),e=JSON.stringify(a.device_sub_permissions.sort())!==JSON.stringify(C.device_sub_permissions.sort());return s||e}),ne={name:[{required:!0,message:"请输入角色名称",trigger:"blur"},{min:2,max:50,message:"角色名称长度在 2 到 50 个字符",trigger:"blur"}],description:[{required:!0,message:"请输入角色描述",trigger:"blur"}]},ie=[{key:"dashboard",name:"工作台",icon:Ge},{key:"applications",name:"处理事项",icon:Xe},{key:"org-users",name:"组织与用户管理",icon:We},{key:"user-registration",name:"新用户审核",icon:Q},{key:"device-center",name:"设备管理中心",icon:ee},{key:"client-management",name:"设备绑定中心",icon:ee},{key:"role-management",name:"角色管理",icon:Q},{key:"system-settings",name:"系统设置",icon:Ye},{key:"data-dashboard",name:"数据大屏",icon:Ze},{key:"profile-management",name:"个人资料管理",icon:He}],oe=[{key:"usb-devices",name:"USB设备管理"},{key:"slave-servers",name:"分布式节点管理"},{key:"device-groups",name:"资源调度分组"},{key:"permission-assignment",name:"授权范围管理"}],O=async()=>{D.value=!0;try{const s=await ns();let e=s;s&&s.success&&s.data&&(console.log("🔧 loadRoles - 检测到API中间件包装格式，提取data字段"),e=s.data),console.log("loadRoles - 处理后的角色数据:",e),e&&e.roles?(f.value=Array.isArray(e.roles)?e.roles:[],console.log("加载角色列表成功:",f.value.length,"个角色"),e.filtered_by_permission&&console.log("权限过滤已生效")):Array.isArray(e)?(f.value=e,console.log("加载角色列表成功（数组格式）:",f.value.length,"个角色")):(f.value=[],console.log("角色数据格式异常，设置为空数组"))}catch(s){g.error("加载角色列表失败"),console.error("Load roles error:",s)}finally{D.value=!1}},te=()=>{x.value=!1,j(),F(),A.value=!0},le=async s=>{x.value=!0,console.log("编辑角色原始数据:",s);let e=s;try{console.log("正在获取角色完整权限数据...");const y=await ae(s.id);if(y&&y.success&&y.data)e=y.data,console.log("成功获取完整角色数据:",e);else if(y&&y.id)e=y,console.log("成功获取完整角色数据:",e);else throw new Error("API响应格式异常")}catch(y){console.warn("获取角色详情失败，使用角色列表数据:",y.message),e=s}const l={全域管理员:{navigation:["dashboard","applications","org-users","user-registration","device-center","client-management","role-management","system-settings","data-dashboard"],device:["usb-devices","slave-servers","device-groups","permission-assignment"],management:{can_manage_users:!0,can_manage_devices:!0,can_view_reports:!0}},超级管理员:{navigation:["dashboard","applications","org-users","user-registration","device-center","client-management","role-management","system-settings"],device:["usb-devices","slave-servers","device-groups","permission-assignment"],management:{can_manage_users:!0,can_manage_devices:!0,can_view_reports:!0}},管理员:{navigation:["dashboard","applications","org-users","user-registration","device-center","client-management","role-management"],device:["usb-devices","device-groups"],management:{can_manage_users:!0,can_manage_devices:!0,can_view_reports:!0}},普通用户:{navigation:["dashboard","applications"],device:[],management:{can_manage_users:!1,can_manage_devices:!1,can_view_reports:!1}},新用户:{navigation:["dashboard","applications"],device:[],management:{can_manage_users:!1,can_manage_devices:!1,can_view_reports:!1}}}[e.name],d=Array.isArray(e.navigation_permissions)?e.navigation_permissions:[],p=Array.isArray(e.device_sub_permissions)?e.device_sub_permissions:[],R=d.length>0?d:l?l.navigation:[],h=p.length>0?p:l?l.device:[];console.log("权限配置详情:",{roleName:e.name,currentNav:d,currentDevice:p,finalNav:R,finalDevice:h,template:l});const w=l?l.management:{};Object.assign(a,{id:e.id,name:e.name,description:e.description,permissions:e.permissions||[],navigation_permissions:R,device_sub_permissions:h,level_scope:e.level_scope||1,can_manage_users:e.can_manage_users!==void 0?e.can_manage_users:w.can_manage_users,can_manage_devices:e.can_manage_devices!==void 0?e.can_manage_devices:w.can_manage_devices,can_view_reports:e.can_view_reports!==void 0?e.can_view_reports:w.can_view_reports}),console.log("编辑角色最终数据:",{roleName:e.name,template:l,finalForm:a}),F(),m.value=!1,A.value=!0},re=s=>{V.value=s,T.value=!0},j=()=>{var s;Object.assign(a,{name:"",description:"",level_scope:0,permissions:[],navigation_permissions:[],device_sub_permissions:[],can_manage_users:!1,can_manage_devices:!1,can_view_reports:!1}),(s=I.value)==null||s.resetFields(),F(),m.value=!1},ce=()=>{m.value=!0,g.info("现在可以编辑权限配置")},de=()=>{m.value=!1,A.value=!1,j()},me=async()=>{try{await ve(),m.value=!1}catch(s){console.error("保存角色失败:",s)}},F=()=>{C.navigation_permissions=[...a.navigation_permissions],C.device_sub_permissions=[...a.device_sub_permissions]},_e=()=>{F(),g.success("权限配置已确认")},ue=()=>{a.navigation_permissions=[...C.navigation_permissions],a.device_sub_permissions=[...C.device_sub_permissions],g.info("权限配置已重置")},ve=async()=>{if(I.value)try{await I.value.validate(),$.value=!0;const s={name:a.name,description:a.description,permissions:a.permissions||[],navigation_permissions:a.navigation_permissions||[],device_sub_permissions:a.device_sub_permissions||[],level_scope:a.level_scope||1,can_manage_users:a.can_manage_users||!1,can_manage_devices:a.can_manage_devices||!1,can_view_reports:a.can_view_reports||!1};if(console.log("保存角色数据:",s),x.value){const e=await os(a.id,s);console.log("角色更新响应:",e),g.success("角色更新成功");try{await be(a.id,s),await new Promise(o=>setTimeout(o,100)),await ye(a.id),console.log("角色权限同步流程完成:",a.id)}catch(o){console.error("权限同步过程中出现错误:",o),g.warning("权限更新成功，但同步显示可能有延迟")}}else{const e=await is(s);console.log("角色创建响应:",e),g.success("角色创建成功")}await O(),A.value=!1}catch(s){g.error(x.value?"角色更新失败":"角色创建失败"),console.error("Save role error:",s)}finally{$.value=!1}},pe=async s=>{try{await as.confirm(`确定要删除角色 "${s.name}" 吗？此操作不可恢复。`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await ts(s.id),g.success("角色删除成功"),await O()}catch(e){e!=="cancel"&&(g.error("角色删除失败"),console.error("Delete role error:",e))}},ge=s=>s.name==="全域管理员",fe=s=>["全域管理员","超级管理员","管理员","普通用户","新用户"].includes(s.name),be=async(s,e)=>{try{typeof window<"u"&&window.dispatchEvent(new CustomEvent("rolePermissionUpdated",{detail:{roleId:s,roleName:e.name,permissions:{navigation:e.navigation_permissions||[],device:e.device_sub_permissions||[],management:{can_manage_users:e.can_manage_users,can_manage_devices:e.can_manage_devices,can_view_reports:e.can_view_reports}},timestamp:new Date().toISOString()}})),console.log("权限更新事件已发送:",s,e.name)}catch(o){console.error("发送权限更新事件失败:",o)}},ye=async s=>{try{if(!s)return;console.log("开始刷新角色权限:",s);const e=await ae(s);let o=null;if(e&&e.success&&e.data)o=e.data;else if(e&&e.id)o=e;else throw new Error("API响应格式异常");if(console.log("获取角色详情:",o),!o||!o.id)throw new Error("角色数据不完整");if(x.value&&a.id===s){const d={id:o.id,name:o.name||"",description:o.description||"",permissions:Array.isArray(o.permissions)?[...o.permissions]:[],navigation_permissions:Array.isArray(o.navigation_permissions)?[...o.navigation_permissions]:[],device_sub_permissions:Array.isArray(o.device_sub_permissions)?[...o.device_sub_permissions]:[],level_scope:o.level_scope||1,can_manage_users:!!o.can_manage_users,can_manage_devices:!!o.can_manage_devices,can_view_reports:!!o.can_view_reports};Object.keys(d).forEach(p=>{p in a&&(a[p]=d[p])}),C.navigation_permissions=[...d.navigation_permissions],C.device_sub_permissions=[...d.device_sub_permissions],console.log("表单数据已完全更新:",{roleId:s,roleName:a.name,navigationPermissions:a.navigation_permissions,devicePermissions:a.device_sub_permissions,managementPermissions:{can_manage_users:a.can_manage_users,can_manage_devices:a.can_manage_devices,can_view_reports:a.can_view_reports}})}const l=f.value.findIndex(d=>d.id===s);l!==-1&&(f.value[l]={...f.value[l],...o}),console.log("角色权限刷新完成:",s),g.success("权限数据已同步")}catch(e){console.error("刷新角色权限失败:",e),g.error(`刷新角色权限失败: ${e.message}`)}},we=()=>{typeof window<"u"&&(window.addEventListener("rolePermissionUpdated",s=>{const{roleId:e,roleName:o,permissions:l,timestamp:d}=s.detail;console.log("接收到权限更新事件:",{roleId:e,roleName:o,timestamp:d});const p=f.value.findIndex(R=>R.id===e);p!==-1&&(f.value[p]={...f.value[p],navigation_permissions:l.navigation,device_sub_permissions:l.device,...l.management}),x.value&&a.id===e&&Object.assign(a,{...a,navigation_permissions:l.navigation,device_sub_permissions:l.device,...l.management}),g.success(`角色"${o}"权限已同步更新`)}),window.addEventListener("devicePermissionChanged",s=>{const{userId:e,deviceId:o,action:l,timestamp:d}=s.detail;console.log("设备权限变更:",{userId:e,deviceId:o,action:l,timestamp:d}),O()}))},ke=()=>{typeof window<"u"&&(window.removeEventListener("rolePermissionUpdated",()=>{}),window.removeEventListener("devicePermissionChanged",()=>{}))};return he(()=>{O(),we()}),xe(()=>{ke()}),(s,e)=>{const o=Ne,l=Ie,d=Be,p=Ue,R=ze,h=Te,w=Je,y=je,U=Me,J=qe,q=Ke,Ee=$e,W=De,L=ss,Pe=es,Ve=Se;return c(),N("div",ls,[u("div",rs,[e[9]||(e[9]=u("div",{class:"header-content"},[u("h2",null,"角色管理"),u("p",null,"管理系统角色和权限分配，仅超级管理员可访问")],-1)),u("div",cs,[i(o,{type:"primary",onClick:te,icon:S(Re)},{default:n(()=>e[8]||(e[8]=[r(" 创建角色 ",-1)])),_:1,__:[8]},8,["icon"])])]),u("div",ds,[Ae((c(),v(p,{data:f.value,style:{width:"100%"}},{default:n(()=>[i(d,{prop:"name",label:"角色名称",width:"150"},{default:n(({row:t})=>[u("div",ms,[t.is_system_role?(c(),v(l,{key:0,type:"danger",size:"small"},{default:n(()=>e[10]||(e[10]=[r("系统",-1)])),_:1,__:[10]})):b("",!0),r(" "+E(t.name),1)])]),_:1}),i(d,{prop:"description",label:"角色描述",width:"140"}),i(d,{label:"权限范围",width:"100"},{default:n(({row:t})=>[t.level_scope===0?(c(),v(l,{key:0,type:"warning"},{default:n(()=>e[11]||(e[11]=[r("无限制",-1)])),_:1,__:[11]})):(c(),v(l,{key:1,type:"info"},{default:n(()=>[r(E(t.level_scope)+"级",1)]),_:2},1024))]),_:1}),i(d,{label:"状态",width:"80"},{default:n(({row:t})=>[i(l,{type:t.is_active?"success":"danger"},{default:n(()=>[r(E(t.is_active?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),i(d,{label:"操作","min-width":"250",fixed:"right"},{default:n(({row:t})=>[i(o,{size:"small",onClick:Y=>re(t),icon:S(Oe)},{default:n(()=>e[12]||(e[12]=[r(" 查看 ",-1)])),_:2,__:[12]},1032,["onClick","icon"]),ge(t)?b("",!0):(c(),v(o,{key:0,size:"small",type:"primary",onClick:Y=>le(t),icon:S(Fe)},{default:n(()=>e[13]||(e[13]=[r(" 编辑 ",-1)])),_:2,__:[13]},1032,["onClick","icon"])),fe(t)?b("",!0):(c(),v(o,{key:1,size:"small",type:"danger",onClick:Y=>pe(t),icon:S(Le)},{default:n(()=>e[14]||(e[14]=[r(" 删除 ",-1)])),_:2,__:[14]},1032,["onClick","icon"]))]),_:1})]),_:1},8,["data"])),[[Ve,D.value]])]),i(W,{modelValue:A.value,"onUpdate:modelValue":e[6]||(e[6]=t=>A.value=t),title:x.value?"编辑角色":"创建角色",width:"900px",onClose:j},{footer:n(()=>[u("span",fs,[m.value?b("",!0):(c(),v(o,{key:0,onClick:ce},{default:n(()=>e[22]||(e[22]=[r("开始修改",-1)])),_:1,__:[22]})),m.value?(c(),v(o,{key:1,onClick:de},{default:n(()=>e[23]||(e[23]=[r("取消修改",-1)])),_:1,__:[23]})):b("",!0),m.value?(c(),v(o,{key:2,type:"primary",onClick:me,loading:$.value},{default:n(()=>e[24]||(e[24]=[r(" 确认保存 ",-1)])),_:1,__:[24]},8,["loading"])):b("",!0)])]),default:n(()=>[i(Ee,{model:a,rules:ne,ref_key:"roleFormRef",ref:I,"label-width":"120px"},{default:n(()=>[i(h,{label:"角色名称",prop:"name"},{default:n(()=>[i(R,{modelValue:a.name,"onUpdate:modelValue":e[0]||(e[0]=t=>a.name=t),placeholder:"请输入角色名称"},null,8,["modelValue"])]),_:1}),i(h,{label:"角色描述",prop:"description"},{default:n(()=>[i(R,{modelValue:a.description,"onUpdate:modelValue":e[1]||(e[1]=t=>a.description=t),type:"textarea",rows:3,placeholder:"请输入角色描述"},null,8,["modelValue"])]),_:1}),i(h,{label:"权限层级"},{default:n(()=>[i(y,{modelValue:a.level_scope,"onUpdate:modelValue":e[2]||(e[2]=t=>a.level_scope=t),placeholder:"选择权限层级范围"},{default:n(()=>[i(w,{label:"无限制",value:0}),i(w,{label:"1级权限",value:1}),i(w,{label:"2级权限",value:2}),i(w,{label:"3级权限",value:3}),i(w,{label:"4级权限",value:4})]),_:1},8,["modelValue"])]),_:1}),i(h,{label:"管理权限"},{default:n(()=>[i(J,{modelValue:X.value,"onUpdate:modelValue":e[3]||(e[3]=t=>X.value=t)},{default:n(()=>[i(U,{label:"can_manage_users"},{default:n(()=>e[15]||(e[15]=[r("用户管理",-1)])),_:1,__:[15]}),i(U,{label:"can_manage_devices"},{default:n(()=>e[16]||(e[16]=[r("设备管理",-1)])),_:1,__:[16]}),i(U,{label:"can_view_reports"},{default:n(()=>e[17]||(e[17]=[r("报告查看",-1)])),_:1,__:[17]})]),_:1},8,["modelValue"])]),_:1}),i(h,{label:"功能权限"},{default:n(()=>[u("div",_s,[e[21]||(e[21]=u("h4",null,"一级权限（导航菜单）",-1)),i(J,{modelValue:a.navigation_permissions,"onUpdate:modelValue":e[4]||(e[4]=t=>a.navigation_permissions=t),class:"navigation-group-grid",disabled:_.value||!m.value},{default:n(()=>[(c(),N(H,null,K(ie,t=>i(U,{key:t.key,label:t.key,disabled:_.value||!m.value,class:G(["nav-permission-item-grid",{"global-admin-item":_.value,"is-disabled":_.value}])},{default:n(()=>[u("div",us,[i(q,{class:G(["nav-icon",{"global-admin-icon":_.value}])},{default:n(()=>[(c(),v(Qe(t.icon)))]),_:2},1032,["class"]),u("span",null,E(t.name),1),_.value?(c(),v(q,{key:0,class:"admin-badge"},{default:n(()=>[i(S(se))]),_:1})):b("",!0)])]),_:2},1032,["label","disabled","class"])),64))]),_:1},8,["modelValue","disabled"]),a.navigation_permissions.includes("device-center")||_.value?(c(),N("div",vs,[e[18]||(e[18]=u("h4",null,"设备管理中心 - 二级权限",-1)),i(J,{modelValue:a.device_sub_permissions,"onUpdate:modelValue":e[5]||(e[5]=t=>a.device_sub_permissions=t),class:"sub-permission-group",disabled:_.value||!m.value},{default:n(()=>[(c(),N(H,null,K(oe,t=>i(U,{key:t.key,label:t.key,disabled:_.value||!m.value,class:G(["sub-permission-item",{"global-admin-item":_.value,"is-disabled":_.value||!m.value}])},{default:n(()=>[u("span",null,E(t.name),1),_.value?(c(),v(q,{key:0,class:"admin-badge"},{default:n(()=>[i(S(se))]),_:1})):b("",!0)]),_:2},1032,["label","disabled","class"])),64))]),_:1},8,["modelValue","disabled"])])):b("",!0),!_.value&&m.value?(c(),N("div",ps,[i(o,{type:"success",onClick:_e,disabled:!z.value,size:"small"},{default:n(()=>e[19]||(e[19]=[r(" 确定修改 ",-1)])),_:1,__:[19]},8,["disabled"]),i(o,{onClick:ue,disabled:!z.value,size:"small"},{default:n(()=>e[20]||(e[20]=[r(" 放弃修改 ",-1)])),_:1,__:[20]},8,["disabled"]),z.value?(c(),N("span",gs," 权限配置已修改 ")):b("",!0)])):b("",!0)])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),i(W,{modelValue:T.value,"onUpdate:modelValue":e[7]||(e[7]=t=>T.value=t),title:"角色详情",width:"500px"},{default:n(()=>[V.value?(c(),N("div",bs,[i(Pe,{column:1,border:""},{default:n(()=>[i(L,{label:"角色名称"},{default:n(()=>[r(E(V.value.name),1)]),_:1}),i(L,{label:"角色描述"},{default:n(()=>[r(E(V.value.description),1)]),_:1}),i(L,{label:"系统角色"},{default:n(()=>[i(l,{type:V.value.is_system_role?"danger":"success"},{default:n(()=>[r(E(V.value.is_system_role?"是":"否"),1)]),_:1},8,["type"])]),_:1}),i(L,{label:"权限层级"},{default:n(()=>[V.value.level_scope===0?(c(),v(l,{key:0,type:"warning"},{default:n(()=>e[25]||(e[25]=[r("无限制",-1)])),_:1,__:[25]})):(c(),v(l,{key:1,type:"info"},{default:n(()=>[r(E(V.value.level_scope)+"级",1)]),_:1}))]),_:1})]),_:1})])):b("",!0)]),_:1},8,["modelValue"])])}}},Is=Ce(ys,[["__scopeId","data-v-edfbb2c1"]]);export{Is as default};
