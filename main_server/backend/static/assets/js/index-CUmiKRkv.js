import{_ as ae}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                   *//* empty css                   *//* empty css                 *//* empty css                       *//* empty css                 *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                     *//* empty css                  */import{u as le,r as v,a9 as ee,aa as ge,y as D,w as a,ab as oe,ac as se,c as R,z as C,ad as ye,d,e as t,ae as be,n as r,t as u,a7 as ne,af as Ve,m as ie,p as U,x as re,a8 as pe,i as g,s as I,a as J,o as we,f as xe,ag as Te,ah as he,b as ke,h as De,E as Ce,j as Ee,ai as ze,aj as <PERSON>,ak as je,v as K,al as Pe,am as $e,k as qe}from"./index-BALd70Fs.js";import{b as Ae,a as te}from"./applicationRequests-D6lO1yYh.js";/* empty css                *//* empty css                             */const Se={class:"application-detail"},Re={class:"info-section"},Ue={class:"info-section"},Ie={class:"content-box"},Ye={key:0,class:"info-section"},Fe={key:0,class:"response-section"},Be={class:"content-box"},Oe={class:"dialog-footer"},He={__name:"ApplicationDetailDialog",props:{modelValue:{type:Boolean,default:!1},application:{type:Object,default:()=>null}},emits:["update:modelValue","refresh","process"],setup(Q,{emit:j}){const x=Q,V=j,f=le(),p=v(!1),T=v(null),h=ee({get:()=>x.modelValue,set:s=>V("update:modelValue",s)});ge(()=>x.application,async s=>{s&&s.id&&(s.content||s.description?T.value=s:await k(s.id))},{immediate:!0});const k=async s=>{if(s)try{p.value=!0;const i=await Ae.getApplication(s);T.value=i.data||i}catch(i){console.error("加载申请详情失败:",i),I.error("加载申请详情失败")}finally{p.value=!1}},o=ee(()=>{const s=T.value,i=x.application;return s&&Object.keys(s).length>0?s:i||{}}),P=()=>{V("process",o.value),h.value=!1},$=s=>s?pe(s).format("YYYY-MM-DD HH:mm:ss"):"-",q=s=>({pending:"待处理",approved:"已批准",rejected:"已拒绝",cancelled:"已取消"})[s]||s,_=s=>({pending:"warning",approved:"success",rejected:"danger",cancelled:"info"})[s]||"info",Y=s=>({device_access:"设备访问申请",permission_change:"权限变更申请",organization_transfer:"组织转移申请",other:"其他申请"})[s]||s,y=s=>({device_access:"primary",permission_change:"warning",organization_transfer:"success",other:"info"})[s]||"info",F=s=>({low:"低",normal:"普通",high:"高",urgent:"紧急"})[s]||s,B=s=>({low:"info",normal:"primary",high:"warning",urgent:"danger"})[s]||"primary";return(s,i)=>{const c=be,E=ne,A=Ve,O=ye,S=ie,H=re,N=se;return g(),D(H,{modelValue:h.value,"onUpdate:modelValue":i[1]||(i[1]=l=>h.value=l),title:"申请详情",width:"800px","close-on-click-modal":!1,"destroy-on-close":""},{footer:a(()=>[d("div",Oe,[t(S,{onClick:i[0]||(i[0]=l=>h.value=!1)},{default:a(()=>i[6]||(i[6]=[r("关闭",-1)])),_:1,__:[6]}),o.value&&o.value.status==="pending"&&U(f).hasPermission("application.process")?(g(),D(S,{key:0,type:"primary",onClick:P},{default:a(()=>i[7]||(i[7]=[r(" 处理申请 ",-1)])),_:1,__:[7]})):C("",!0)])]),default:a(()=>[oe((g(),R("div",Se,[o.value?(g(),D(O,{key:0,class:"detail-card"},{default:a(()=>[d("div",Re,[i[2]||(i[2]=d("h3",null,"基本信息",-1)),t(A,{column:2,border:""},{default:a(()=>[t(c,{label:"申请ID"},{default:a(()=>[r(u(o.value.id),1)]),_:1}),t(c,{label:"申请标题"},{default:a(()=>[r(u(o.value.title),1)]),_:1}),t(c,{label:"申请类型"},{default:a(()=>[t(E,{type:y(o.value.type||o.value.application_type)},{default:a(()=>[r(u(Y(o.value.type||o.value.application_type)),1)]),_:1},8,["type"])]),_:1}),t(c,{label:"状态"},{default:a(()=>[t(E,{type:_(o.value.status)},{default:a(()=>[r(u(q(o.value.status)),1)]),_:1},8,["type"])]),_:1}),t(c,{label:"优先级"},{default:a(()=>[t(E,{type:B(o.value.priority)},{default:a(()=>[r(u(F(o.value.priority)),1)]),_:1},8,["type"])]),_:1}),t(c,{label:"申请人"},{default:a(()=>{var l,e;return[r(u(o.value.applicant_name||((l=o.value.applicant)==null?void 0:l.full_name)||((e=o.value.applicant)==null?void 0:e.username)),1)]}),_:1}),t(c,{label:"所属组织"},{default:a(()=>[r(u(o.value.organization_name||o.value.applicant_organization||"未指定"),1)]),_:1}),t(c,{label:"创建时间"},{default:a(()=>[r(u($(o.value.created_at)),1)]),_:1})]),_:1})]),d("div",Ue,[i[3]||(i[3]=d("h3",null,"申请内容",-1)),d("div",Ie,u(o.value.content||o.value.description||"暂无内容"),1)]),o.value.status!=="pending"?(g(),R("div",Ye,[i[5]||(i[5]=d("h3",null,"处理信息",-1)),t(A,{column:2,border:""},{default:a(()=>[t(c,{label:"处理人"},{default:a(()=>{var l,e;return[r(u(o.value.processor_name||((l=o.value.processor)==null?void 0:l.full_name)||((e=o.value.processor)==null?void 0:e.username)||"未知"),1)]}),_:1}),t(c,{label:"处理时间"},{default:a(()=>[r(u($(o.value.processed_at||o.value.updated_at)),1)]),_:1}),t(c,{label:"处理结果",span:"2"},{default:a(()=>[t(E,{type:_(o.value.status)},{default:a(()=>[r(u(q(o.value.status)),1)]),_:1},8,["type"])]),_:1})]),_:1}),o.value.response_content||o.value.process_comment?(g(),R("div",Fe,[i[4]||(i[4]=d("h4",null,"处理意见",-1)),d("div",Be,u(o.value.response_content||o.value.process_comment||"暂无处理意见"),1)])):C("",!0)])):C("",!0)]),_:1})):C("",!0)])),[[N,p.value]])]),_:1},8,["modelValue"])}}},Ne=ae(He,[["__scopeId","data-v-bb4cb208"]]),Le={class:"applications-container"},Ge={class:"page-header"},Je={class:"filter-container"},Ke={class:"table-container"},Qe={class:"pagination-container"},We={__name:"index",setup(Q){ke();const j=le(),x=v(!1),V=v([]),f=J({status:"",application_type:"",priority:""}),p=J({page:1,size:20,total:0}),T=v(!1),h=v(null),k=v(!1),o=v(""),P=v(!1),$=v(),q=v(null),_=J({status:"",response_content:""}),Y={status:[{required:!0,message:"请选择处理结果",trigger:"change"}],response_content:[{required:!0,message:"请输入处理意见",trigger:"blur"}]},y=async()=>{try{x.value=!0;const l={page:p.page,size:p.size,...f};Object.keys(l).forEach(z=>{(l[z]===""||l[z]===null||l[z]===void 0)&&delete l[z]});const e=await te.getApplicationRequests(l);e.data&&Array.isArray(e.data)?(V.value=e.data,p.total=e.total||0):Array.isArray(e)?(V.value=e,p.total=e.length):(V.value=e.data||[],p.total=e.total||0),console.log("申请列表加载成功:",{count:V.value.length,total:p.total,page:p.page})}catch(l){console.error("加载申请列表失败:",l),I.error("加载申请列表失败")}finally{x.value=!1}},F=()=>{Object.assign(f,{status:"",application_type:"",priority:""}),p.page=1,y()},B=l=>{h.value=l,T.value=!0},s=(l,e)=>{q.value=l,_.status=e,_.response_content="",o.value=e==="approved"?"批准申请":"拒绝申请",k.value=!0},i=async()=>{try{await $.value.validate(),P.value=!0,await te.processApplicationRequest(q.value,{status:_.status,process_comment:_.response_content}),I.success("处理成功"),k.value=!1,y()}catch{I.error("处理申请失败")}finally{P.value=!1}},c=l=>pe(l).format("YYYY-MM-DD HH:mm"),E=l=>({pending:"warning",approved:"success",rejected:"danger",cancelled:"info"})[l]||"info",A=l=>({pending:"待处理",approved:"已批准",rejected:"已拒绝",cancelled:"已取消"})[l]||l,O=l=>({permission_request:"primary",role_change:"success",device_request:"warning",other:"info"})[l]||"info",S=l=>({permission_request:"权限申请",role_change:"角色变更",device_request:"设备申请",other:"其他"})[l]||l,H=l=>({low:"info",normal:"primary",high:"warning",urgent:"danger"})[l]||"primary",N=l=>({low:"低",normal:"普通",high:"高",urgent:"紧急"})[l]||l;return we(()=>{y()}),(l,e)=>{const z=De("Plus"),ue=Ce,w=ie,m=Me,L=ze,M=Ee,W=xe,b=je,G=ne,de=he,ce=Te,X=$e,_e=Pe,me=qe,fe=re,ve=se;return g(),R("div",Le,[d("div",Ge,[e[12]||(e[12]=d("h2",{class:"page-title"},"处理事项",-1)),U(j).hasPermission("application.submit")?(g(),D(w,{key:0,type:"primary",onClick:e[0]||(e[0]=n=>l.$router.push("/applications/create"))},{default:a(()=>[t(ue,null,{default:a(()=>[t(z)]),_:1}),e[11]||(e[11]=r(" 创建申请 ",-1))]),_:1,__:[11]})):C("",!0)]),d("div",Je,[t(W,{model:f,inline:""},{default:a(()=>[t(M,{label:"状态"},{default:a(()=>[t(L,{modelValue:f.status,"onUpdate:modelValue":e[1]||(e[1]=n=>f.status=n),placeholder:"全部状态",clearable:""},{default:a(()=>[t(m,{label:"待处理",value:"pending"}),t(m,{label:"已批准",value:"approved"}),t(m,{label:"已拒绝",value:"rejected"}),t(m,{label:"已取消",value:"cancelled"})]),_:1},8,["modelValue"])]),_:1}),t(M,{label:"类型"},{default:a(()=>[t(L,{modelValue:f.application_type,"onUpdate:modelValue":e[2]||(e[2]=n=>f.application_type=n),placeholder:"全部类型",clearable:""},{default:a(()=>[t(m,{label:"权限申请",value:"permission_request"}),t(m,{label:"角色变更",value:"role_change"}),t(m,{label:"设备申请",value:"device_request"}),t(m,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1}),t(M,{label:"优先级"},{default:a(()=>[t(L,{modelValue:f.priority,"onUpdate:modelValue":e[3]||(e[3]=n=>f.priority=n),placeholder:"全部优先级",clearable:""},{default:a(()=>[t(m,{label:"低",value:"low"}),t(m,{label:"普通",value:"normal"}),t(m,{label:"高",value:"high"}),t(m,{label:"紧急",value:"urgent"})]),_:1},8,["modelValue"])]),_:1}),t(M,null,{default:a(()=>[t(w,{type:"primary",onClick:y},{default:a(()=>e[13]||(e[13]=[r("查询",-1)])),_:1,__:[13]}),t(w,{onClick:F},{default:a(()=>e[14]||(e[14]=[r("重置",-1)])),_:1,__:[14]})]),_:1})]),_:1},8,["model"])]),d("div",Ke,[oe((g(),D(de,{data:V.value,stripe:"",style:{width:"100%"}},{default:a(()=>[t(b,{prop:"id",label:"ID",width:"80"}),t(b,{prop:"title",label:"标题","min-width":"200","show-overflow-tooltip":""}),t(b,{prop:"application_type",label:"类型",width:"120"},{default:a(({row:n})=>[t(G,{type:O(n.application_type)},{default:a(()=>[r(u(S(n.application_type)),1)]),_:2},1032,["type"])]),_:1}),t(b,{prop:"status",label:"状态",width:"100"},{default:a(({row:n})=>[t(G,{type:E(n.status)},{default:a(()=>[r(u(A(n.status)),1)]),_:2},1032,["type"])]),_:1}),t(b,{prop:"priority",label:"优先级",width:"100"},{default:a(({row:n})=>[t(G,{type:H(n.priority)},{default:a(()=>[r(u(N(n.priority)),1)]),_:2},1032,["type"])]),_:1}),t(b,{prop:"applicant_name",label:"申请人",width:"120"}),t(b,{prop:"organization_name",label:"所属组织",width:"150","show-overflow-tooltip":""}),t(b,{prop:"created_at",label:"创建时间",width:"160"},{default:a(({row:n})=>[r(u(c(n.created_at)),1)]),_:1}),t(b,{label:"操作",width:"200",fixed:"right"},{default:a(({row:n})=>[t(w,{type:"primary",size:"small",onClick:K(Z=>B(n),["stop"])},{default:a(()=>e[15]||(e[15]=[r(" 查看 ",-1)])),_:2,__:[15]},1032,["onClick"]),n.status==="pending"&&U(j).hasPermission("application.process")?(g(),D(w,{key:0,type:"success",size:"small",onClick:K(Z=>s(n.id,"approved"),["stop"])},{default:a(()=>e[16]||(e[16]=[r(" 批准 ",-1)])),_:2,__:[16]},1032,["onClick"])):C("",!0),n.status==="pending"&&U(j).hasPermission("application.process")?(g(),D(w,{key:1,type:"danger",size:"small",onClick:K(Z=>s(n.id,"rejected"),["stop"])},{default:a(()=>e[17]||(e[17]=[r(" 拒绝 ",-1)])),_:2,__:[17]},1032,["onClick"])):C("",!0)]),_:1})]),_:1},8,["data"])),[[ve,x.value]]),d("div",Qe,[t(ce,{"current-page":p.page,"onUpdate:currentPage":e[4]||(e[4]=n=>p.page=n),"page-size":p.size,"onUpdate:pageSize":e[5]||(e[5]=n=>p.size=n),total:p.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:y,onCurrentChange:y},null,8,["current-page","page-size","total"])])]),t(fe,{modelValue:k.value,"onUpdate:modelValue":e[9]||(e[9]=n=>k.value=n),title:o.value,width:"500px","close-on-click-modal":!1},{footer:a(()=>[t(w,{onClick:e[8]||(e[8]=n=>k.value=!1)},{default:a(()=>e[20]||(e[20]=[r("取消",-1)])),_:1,__:[20]}),t(w,{type:"primary",onClick:i,loading:P.value},{default:a(()=>e[21]||(e[21]=[r(" 确定 ",-1)])),_:1,__:[21]},8,["loading"])]),default:a(()=>[t(W,{ref_key:"processFormRef",ref:$,model:_,rules:Y,"label-width":"80px"},{default:a(()=>[t(M,{label:"处理结果",prop:"status"},{default:a(()=>[t(_e,{modelValue:_.status,"onUpdate:modelValue":e[6]||(e[6]=n=>_.status=n)},{default:a(()=>[t(X,{label:"approved"},{default:a(()=>e[18]||(e[18]=[r("批准",-1)])),_:1,__:[18]}),t(X,{label:"rejected"},{default:a(()=>e[19]||(e[19]=[r("拒绝",-1)])),_:1,__:[19]})]),_:1},8,["modelValue"])]),_:1}),t(M,{label:"处理意见",prop:"response_content"},{default:a(()=>[t(me,{modelValue:_.response_content,"onUpdate:modelValue":e[7]||(e[7]=n=>_.response_content=n),type:"textarea",rows:4,placeholder:"请输入处理意见"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),t(Ne,{modelValue:T.value,"onUpdate:modelValue":e[10]||(e[10]=n=>T.value=n),application:h.value,onRefresh:y},null,8,["modelValue","application"])])}}},gt=ae(We,[["__scopeId","data-v-cb197bfe"]]);export{gt as default};
