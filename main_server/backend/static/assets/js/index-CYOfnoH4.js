import{_ as ke}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                   *//* empty css                             *//* empty css                   *//* empty css                     *//* empty css                          *//* empty css                    *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  */import{ds as T,r as g,a as H,aN as q,o as we,c as C,d as c,e as n,w as a,p as S,cR as Ve,m as Ce,a9 as Pe,aa as Ee,y as m,x as Re,s as p,ac as xe,i as t,n as o,af as De,z as _,a7 as Se,t as f,cW as he,cV as Ne,cX as ze,f as Be,j as Oe,k as Ue,ad as Fe,ae as Te,cN as $e,cO as Ae,a5 as K,a6 as Q,X as Ie,Y as je,Z as Je,_ as ee,L as se,$ as Le,a0 as qe,R as Me,a2 as M,E as Ge,bZ as Xe,dl as ae,al as Ze,am as We,a4 as Ye}from"./index-ClQFnb0n.js";function He(){return T.get("/api/v2/roles/")}function Ke(b){return T.post("/api/v2/roles/",b)}function Qe(b,V){return T.put(`/api/v2/roles/${b}`,V)}function es(b){return T.delete(`/api/v2/roles/${b}`)}function ss(){return{"user.view":"查看用户","user.create":"创建用户","user.edit":"编辑用户","user.delete":"删除用户","user.manage_role":"管理用户角色","org.view":"查看组织","org.create":"创建组织","org.edit":"编辑组织","org.delete":"删除组织","device.view":"查看设备","device.manage":"管理设备","device.connect":"连接设备","application.view":"查看申请","application.submit":"提交申请","application.process":"处理申请","profile.view":"查看个人资料","profile.edit":"编辑个人资料","system.config":"系统配置","system.monitor":"系统监控","system.audit":"审计日志"}}function as(b){const V=Object.keys(ss()),P=b.filter(y=>!V.includes(y));return{isValid:P.length===0,invalidPermissions:P}}const ns={class:"role-management"},is={class:"page-header"},ls={class:"header-actions"},os={class:"role-list"},ts={class:"role-name"},rs={class:"navigation-permissions"},ds={class:"nav-permission-content"},us={key:0,class:"sub-permissions"},cs={key:1,class:"permission-actions"},ms={key:0,class:"change-indicator"},_s={class:"dialog-footer"},vs={key:0,class:"role-detail"},ps={__name:"index",setup(b){const V=g(!1),P=g(!1),y=g(!1),$=g(!1),h=g(!1),d=g(!1),E=g([]),k=g(null),O=g(),l=H({name:"",description:"",level_scope:0,permissions:[],navigation_permissions:[],device_sub_permissions:[],can_manage_users:!1,can_manage_devices:!1,can_view_reports:!1}),R=H({navigation_permissions:[],device_sub_permissions:[]}),G=q({get(){const s=[];return l.can_manage_users&&s.push("can_manage_users"),l.can_manage_devices&&s.push("can_manage_devices"),l.can_view_reports&&s.push("can_view_reports"),s},set(s){l.can_manage_users=s.includes("can_manage_users"),l.can_manage_devices=s.includes("can_manage_devices"),l.can_view_reports=s.includes("can_view_reports")}}),u=q(()=>l.name==="全域管理员"),A=q(()=>{if(u.value)return!1;const s=JSON.stringify(l.navigation_permissions.sort())!==JSON.stringify(R.navigation_permissions.sort()),e=JSON.stringify(l.device_sub_permissions.sort())!==JSON.stringify(R.device_sub_permissions.sort());return s||e}),ne={name:[{required:!0,message:"请输入角色名称",trigger:"blur"},{min:2,max:50,message:"角色名称长度在 2 到 50 个字符",trigger:"blur"}],description:[{required:!0,message:"请输入角色描述",trigger:"blur"}]},X=[{key:"dashboard",name:"工作台",icon:Ie},{key:"applications",name:"处理事项",icon:je},{key:"org-users",name:"组织与用户管理",icon:Je},{key:"user-registration",name:"新用户审核",icon:ee},{key:"device-center",name:"设备管理中心",icon:se},{key:"client-management",name:"设备绑定中心",icon:se},{key:"role-management",name:"角色管理",icon:ee},{key:"system-settings",name:"系统设置",icon:Le},{key:"data-dashboard",name:"数据大屏",icon:qe},{key:"profile-management",name:"个人资料管理",icon:Me}],Z=[{key:"usb-devices",name:"USB设备管理"},{key:"slave-servers",name:"分布式节点管理"},{key:"device-groups",name:"资源调度分组"},{key:"permission-assignment",name:"授权范围管理"}],I=async()=>{V.value=!0;try{const s=await He();let e=s;s&&s.success&&s.data&&(console.log("🔧 loadRoles - 检测到API中间件包装格式，提取data字段"),e=s.data),console.log("loadRoles - 处理后的角色数据:",e),e&&e.roles?(E.value=Array.isArray(e.roles)?e.roles:[],console.log("加载角色列表成功:",E.value.length,"个角色"),e.filtered_by_permission&&console.log("权限过滤已生效")):Array.isArray(e)?(E.value=e,console.log("加载角色列表成功（数组格式）:",E.value.length,"个角色")):(E.value=[],console.log("角色数据格式异常，设置为空数组"))}catch(s){p.error("加载角色列表失败"),console.error("Load roles error:",s)}finally{V.value=!1}},ie=()=>{h.value=!1,j(),U(),y.value=!0},le=s=>{h.value=!0;const r={全域管理员:{navigation:X.map(D=>D.key),device:Z.map(D=>D.key),management:["can_manage_users","can_manage_devices","can_view_reports"]},超级管理员:{navigation:["dashboard","users","devices","organizations","roles","system-settings","reports"],device:["device.view","device.connect","device.manage","device.assign"],management:["can_manage_users","can_manage_devices","can_view_reports"]},管理员:{navigation:["dashboard","users","devices","organizations","reports"],device:["device.view","device.connect","device.assign"],management:["can_manage_users","can_view_reports"]},普通用户:{navigation:["dashboard","devices"],device:["device.view","device.connect"],management:[]},新用户:{navigation:["dashboard"],device:[],management:[]}}[s.name],v=r?r.navigation:s.navigation_permissions||s.permissions||[],w=r?r.device:s.device_sub_permissions||s.device_permissions||[],x=r?r.management:[];Object.assign(l,{...s,navigation_permissions:v,device_sub_permissions:w,can_manage_users:x.includes("can_manage_users"),can_manage_devices:x.includes("can_manage_devices"),can_view_reports:x.includes("can_view_reports")}),console.log("编辑角色权限数据:",{roleName:s.name,template:r,navigationPermissions:v,deviceSubPermissions:w,managementPermissions:x}),U(),d.value=!1,y.value=!0},oe=s=>{k.value=s,$.value=!0},j=()=>{var s;Object.assign(l,{name:"",description:"",level_scope:0,permissions:[],navigation_permissions:[],device_sub_permissions:[],can_manage_users:!1,can_manage_devices:!1,can_view_reports:!1}),(s=O.value)==null||s.resetFields(),U(),d.value=!1},te=()=>{d.value=!0,p.info("现在可以编辑权限配置")},re=()=>{d.value=!1,y.value=!1,j()},de=async()=>{try{await me(),d.value=!1}catch(s){console.error("保存角色失败:",s)}},U=()=>{R.navigation_permissions=[...l.navigation_permissions],R.device_sub_permissions=[...l.device_sub_permissions]},ue=()=>{U(),p.success("权限配置已确认")},ce=()=>{l.navigation_permissions=[...R.navigation_permissions],l.device_sub_permissions=[...R.device_sub_permissions],p.info("权限配置已重置")},me=async()=>{if(O.value)try{await O.value.validate(),P.value=!0;const s={...l},e=as(s.permissions);if(!e.isValid){p.error(`无效的权限配置: ${e.invalidPermissions.join(", ")}`);return}h.value?(await Qe(l.id,s),p.success("角色更新成功")):(await Ke(s),p.success("角色创建成功")),y.value=!1,await I()}catch(s){p.error(h.value?"角色更新失败":"角色创建失败"),console.error("Save role error:",s)}finally{P.value=!1}},_e=async s=>{try{await Ye.confirm(`确定要删除角色 "${s.name}" 吗？此操作不可恢复。`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await es(s.id),p.success("角色删除成功"),await I()}catch(e){e!=="cancel"&&(p.error("角色删除失败"),console.error("Delete role error:",e))}},ve=s=>s.name==="全域管理员",pe=s=>["全域管理员","超级管理员","管理员","普通用户","新用户"].includes(s.name);return we(()=>{I()}),(s,e)=>{const r=Ce,v=Se,w=De,x=xe,D=Ue,N=Oe,z=Te,ge=Fe,B=Ae,J=$e,L=Ge,fe=Be,W=Re,F=We,be=Ze,ye=Ee;return t(),C("div",ns,[c("div",is,[e[9]||(e[9]=c("div",{class:"header-content"},[c("h2",null,"角色管理"),c("p",null,"管理系统角色和权限分配，仅超级管理员可访问")],-1)),c("div",ls,[n(r,{type:"primary",onClick:ie,icon:S(Ve)},{default:a(()=>e[8]||(e[8]=[o(" 创建角色 ",-1)])),_:1,__:[8]},8,["icon"])])]),c("div",os,[Pe((t(),m(x,{data:E.value,style:{width:"100%"}},{default:a(()=>[n(w,{prop:"name",label:"角色名称",width:"150"},{default:a(({row:i})=>[c("div",ts,[i.is_system_role?(t(),m(v,{key:0,type:"danger",size:"small"},{default:a(()=>e[10]||(e[10]=[o("系统",-1)])),_:1,__:[10]})):_("",!0),o(" "+f(i.name),1)])]),_:1}),n(w,{prop:"description",label:"角色描述",width:"140"}),n(w,{label:"权限范围",width:"100"},{default:a(({row:i})=>[i.level_scope===0?(t(),m(v,{key:0,type:"warning"},{default:a(()=>e[11]||(e[11]=[o("无限制",-1)])),_:1,__:[11]})):(t(),m(v,{key:1,type:"info"},{default:a(()=>[o(f(i.level_scope)+"级",1)]),_:2},1024))]),_:1}),n(w,{label:"状态",width:"80"},{default:a(({row:i})=>[n(v,{type:i.is_active?"success":"danger"},{default:a(()=>[o(f(i.is_active?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),n(w,{label:"操作","min-width":"250",fixed:"right"},{default:a(({row:i})=>[n(r,{size:"small",onClick:Y=>oe(i),icon:S(he)},{default:a(()=>e[12]||(e[12]=[o(" 查看 ",-1)])),_:2,__:[12]},1032,["onClick","icon"]),ve(i)?_("",!0):(t(),m(r,{key:0,size:"small",type:"primary",onClick:Y=>le(i),icon:S(Ne)},{default:a(()=>e[13]||(e[13]=[o(" 编辑 ",-1)])),_:2,__:[13]},1032,["onClick","icon"])),pe(i)?_("",!0):(t(),m(r,{key:1,size:"small",type:"danger",onClick:Y=>_e(i),icon:S(ze)},{default:a(()=>e[14]||(e[14]=[o(" 删除 ",-1)])),_:2,__:[14]},1032,["onClick","icon"]))]),_:1})]),_:1},8,["data"])),[[ye,V.value]])]),n(W,{modelValue:y.value,"onUpdate:modelValue":e[6]||(e[6]=i=>y.value=i),title:h.value?"编辑角色":"创建角色",width:"900px",onClose:j},{footer:a(()=>[c("span",_s,[d.value?_("",!0):(t(),m(r,{key:0,onClick:te},{default:a(()=>e[22]||(e[22]=[o("开始修改",-1)])),_:1,__:[22]})),d.value?(t(),m(r,{key:1,onClick:re},{default:a(()=>e[23]||(e[23]=[o("取消修改",-1)])),_:1,__:[23]})):_("",!0),d.value?(t(),m(r,{key:2,type:"primary",onClick:de,loading:P.value},{default:a(()=>e[24]||(e[24]=[o(" 确认保存 ",-1)])),_:1,__:[24]},8,["loading"])):_("",!0)])]),default:a(()=>[n(fe,{model:l,rules:ne,ref_key:"roleFormRef",ref:O,"label-width":"120px"},{default:a(()=>[n(N,{label:"角色名称",prop:"name"},{default:a(()=>[n(D,{modelValue:l.name,"onUpdate:modelValue":e[0]||(e[0]=i=>l.name=i),placeholder:"请输入角色名称"},null,8,["modelValue"])]),_:1}),n(N,{label:"角色描述",prop:"description"},{default:a(()=>[n(D,{modelValue:l.description,"onUpdate:modelValue":e[1]||(e[1]=i=>l.description=i),type:"textarea",rows:3,placeholder:"请输入角色描述"},null,8,["modelValue"])]),_:1}),n(N,{label:"权限层级"},{default:a(()=>[n(ge,{modelValue:l.level_scope,"onUpdate:modelValue":e[2]||(e[2]=i=>l.level_scope=i),placeholder:"选择权限层级范围"},{default:a(()=>[n(z,{label:"无限制",value:0}),n(z,{label:"1级权限",value:1}),n(z,{label:"2级权限",value:2}),n(z,{label:"3级权限",value:3}),n(z,{label:"4级权限",value:4})]),_:1},8,["modelValue"])]),_:1}),n(N,{label:"管理权限"},{default:a(()=>[n(J,{modelValue:G.value,"onUpdate:modelValue":e[3]||(e[3]=i=>G.value=i)},{default:a(()=>[n(B,{label:"can_manage_users"},{default:a(()=>e[15]||(e[15]=[o("用户管理",-1)])),_:1,__:[15]}),n(B,{label:"can_manage_devices"},{default:a(()=>e[16]||(e[16]=[o("设备管理",-1)])),_:1,__:[16]}),n(B,{label:"can_view_reports"},{default:a(()=>e[17]||(e[17]=[o("报告查看",-1)])),_:1,__:[17]})]),_:1},8,["modelValue"])]),_:1}),n(N,{label:"功能权限"},{default:a(()=>[c("div",rs,[e[21]||(e[21]=c("h4",null,"一级权限（导航菜单）",-1)),n(J,{modelValue:l.navigation_permissions,"onUpdate:modelValue":e[4]||(e[4]=i=>l.navigation_permissions=i),class:"navigation-group-grid",disabled:u.value||!d.value},{default:a(()=>[(t(),C(K,null,Q(X,i=>n(B,{key:i.key,label:i.key,disabled:u.value||!d.value,class:M(["nav-permission-item-grid",{"global-admin-item":u.value,"is-disabled":u.value}])},{default:a(()=>[c("div",ds,[n(L,{class:M(["nav-icon",{"global-admin-icon":u.value}])},{default:a(()=>[(t(),m(Xe(i.icon)))]),_:2},1032,["class"]),c("span",null,f(i.name),1),u.value?(t(),m(L,{key:0,class:"admin-badge"},{default:a(()=>[n(S(ae))]),_:1})):_("",!0)])]),_:2},1032,["label","disabled","class"])),64))]),_:1},8,["modelValue","disabled"]),l.navigation_permissions.includes("device-center")||u.value?(t(),C("div",us,[e[18]||(e[18]=c("h4",null,"设备管理中心 - 二级权限",-1)),n(J,{modelValue:l.device_sub_permissions,"onUpdate:modelValue":e[5]||(e[5]=i=>l.device_sub_permissions=i),class:"sub-permission-group",disabled:u.value||!d.value},{default:a(()=>[(t(),C(K,null,Q(Z,i=>n(B,{key:i.key,label:i.key,disabled:u.value||!d.value,class:M(["sub-permission-item",{"global-admin-item":u.value,"is-disabled":u.value||!d.value}])},{default:a(()=>[c("span",null,f(i.name),1),u.value?(t(),m(L,{key:0,class:"admin-badge"},{default:a(()=>[n(S(ae))]),_:1})):_("",!0)]),_:2},1032,["label","disabled","class"])),64))]),_:1},8,["modelValue","disabled"])])):_("",!0),!u.value&&d.value?(t(),C("div",cs,[n(r,{type:"success",onClick:ue,disabled:!A.value,size:"small"},{default:a(()=>e[19]||(e[19]=[o(" 确定修改 ",-1)])),_:1,__:[19]},8,["disabled"]),n(r,{onClick:ce,disabled:!A.value,size:"small"},{default:a(()=>e[20]||(e[20]=[o(" 放弃修改 ",-1)])),_:1,__:[20]},8,["disabled"]),A.value?(t(),C("span",ms," 权限配置已修改 ")):_("",!0)])):_("",!0)])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),n(W,{modelValue:$.value,"onUpdate:modelValue":e[7]||(e[7]=i=>$.value=i),title:"角色详情",width:"500px"},{default:a(()=>[k.value?(t(),C("div",vs,[n(be,{column:1,border:""},{default:a(()=>[n(F,{label:"角色名称"},{default:a(()=>[o(f(k.value.name),1)]),_:1}),n(F,{label:"角色描述"},{default:a(()=>[o(f(k.value.description),1)]),_:1}),n(F,{label:"系统角色"},{default:a(()=>[n(v,{type:k.value.is_system_role?"danger":"success"},{default:a(()=>[o(f(k.value.is_system_role?"是":"否"),1)]),_:1},8,["type"])]),_:1}),n(F,{label:"权限层级"},{default:a(()=>[k.value.level_scope===0?(t(),m(v,{key:0,type:"warning"},{default:a(()=>e[25]||(e[25]=[o("无限制",-1)])),_:1,__:[25]})):(t(),m(v,{key:1,type:"info"},{default:a(()=>[o(f(k.value.level_scope)+"级",1)]),_:1}))]),_:1})]),_:1})])):_("",!0)]),_:1},8,["modelValue"])])}}},Ns=ke(ps,[["__scopeId","data-v-0eb459f6"]]);export{Ns as default};
