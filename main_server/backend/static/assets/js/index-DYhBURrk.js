import{_ as Ce}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                   *//* empty css                             *//* empty css                   *//* empty css                     *//* empty css                          *//* empty css                    *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  */import{ds as $,r as b,a as H,aN as M,o as Ee,bM as Re,c as x,d as u,e as n,w as a,p as N,cR as xe,m as he,a9 as Se,aa as Ne,y as _,x as Ue,s as g,ac as De,i as r,n as l,af as Oe,z as p,a7 as ze,t as w,cW as Be,cV as Ie,cX as Le,f as $e,j as Fe,k as Te,ad as je,ae as Ae,cN as Je,cO as Me,a5 as K,a6 as Q,X as qe,Y as Ge,Z as Xe,_ as ee,L as se,$ as Ze,a0 as We,R as Ye,a2 as q,E as He,bZ as Ke,dl as ae,al as Qe,am as es,a4 as ss}from"./index-BBw1jc_Y.js";function as(){return $.get("/api/v2/roles/")}function ns(k){return $.post("/api/v2/roles/",k)}function is(k,E){return $.put(`/api/v2/roles/${k}`,E)}function os(k){return $.delete(`/api/v2/roles/${k}`)}function ts(){return{"user.view":"查看用户","user.create":"创建用户","user.edit":"编辑用户","user.delete":"删除用户","user.manage_role":"管理用户角色","org.view":"查看组织","org.create":"创建组织","org.edit":"编辑组织","org.delete":"删除组织","device.view":"查看设备","device.manage":"管理设备","device.connect":"连接设备","application.view":"查看申请","application.submit":"提交申请","application.process":"处理申请","profile.view":"查看个人资料","profile.edit":"编辑个人资料","system.config":"系统配置","system.monitor":"系统监控","system.audit":"审计日志"}}function ls(k){const E=Object.keys(ts()),h=k.filter(P=>!E.includes(P));return{isValid:h.length===0,invalidPermissions:h}}const rs={class:"role-management"},ds={class:"page-header"},cs={class:"header-actions"},ms={class:"role-list"},us={class:"role-name"},_s={class:"navigation-permissions"},vs={class:"nav-permission-content"},ps={key:0,class:"sub-permissions"},gs={key:1,class:"permission-actions"},fs={key:0,class:"change-indicator"},ys={class:"dialog-footer"},bs={key:0,class:"role-detail"},ws={__name:"index",setup(k){const E=b(!1),h=b(!1),P=b(!1),F=b(!1),R=b(!1),c=b(!1),y=b([]),V=b(null),z=b(),o=H({name:"",description:"",level_scope:0,permissions:[],navigation_permissions:[],device_sub_permissions:[],can_manage_users:!1,can_manage_devices:!1,can_view_reports:!1}),S=H({navigation_permissions:[],device_sub_permissions:[]}),G=M({get(){const s=[];return o.can_manage_users&&s.push("can_manage_users"),o.can_manage_devices&&s.push("can_manage_devices"),o.can_view_reports&&s.push("can_view_reports"),s},set(s){o.can_manage_users=s.includes("can_manage_users"),o.can_manage_devices=s.includes("can_manage_devices"),o.can_view_reports=s.includes("can_view_reports")}}),m=M(()=>o.name==="全域管理员"),T=M(()=>{if(m.value)return!1;const s=JSON.stringify(o.navigation_permissions.sort())!==JSON.stringify(S.navigation_permissions.sort()),e=JSON.stringify(o.device_sub_permissions.sort())!==JSON.stringify(S.device_sub_permissions.sort());return s||e}),ne={name:[{required:!0,message:"请输入角色名称",trigger:"blur"},{min:2,max:50,message:"角色名称长度在 2 到 50 个字符",trigger:"blur"}],description:[{required:!0,message:"请输入角色描述",trigger:"blur"}]},X=[{key:"dashboard",name:"工作台",icon:qe},{key:"applications",name:"处理事项",icon:Ge},{key:"org-users",name:"组织与用户管理",icon:Xe},{key:"user-registration",name:"新用户审核",icon:ee},{key:"device-center",name:"设备管理中心",icon:se},{key:"client-management",name:"设备绑定中心",icon:se},{key:"role-management",name:"角色管理",icon:ee},{key:"system-settings",name:"系统设置",icon:Ze},{key:"data-dashboard",name:"数据大屏",icon:We},{key:"profile-management",name:"个人资料管理",icon:Ye}],Z=[{key:"usb-devices",name:"USB设备管理"},{key:"slave-servers",name:"分布式节点管理"},{key:"device-groups",name:"资源调度分组"},{key:"permission-assignment",name:"授权范围管理"}],B=async()=>{E.value=!0;try{const s=await as();let e=s;s&&s.success&&s.data&&(console.log("🔧 loadRoles - 检测到API中间件包装格式，提取data字段"),e=s.data),console.log("loadRoles - 处理后的角色数据:",e),e&&e.roles?(y.value=Array.isArray(e.roles)?e.roles:[],console.log("加载角色列表成功:",y.value.length,"个角色"),e.filtered_by_permission&&console.log("权限过滤已生效")):Array.isArray(e)?(y.value=e,console.log("加载角色列表成功（数组格式）:",y.value.length,"个角色")):(y.value=[],console.log("角色数据格式异常，设置为空数组"))}catch(s){g.error("加载角色列表失败"),console.error("Load roles error:",s)}finally{E.value=!1}},ie=()=>{R.value=!1,j(),I(),P.value=!0},oe=s=>{R.value=!0;const t={全域管理员:{navigation:X.map(C=>C.key),device:Z.map(C=>C.key),management:["can_manage_users","can_manage_devices","can_view_reports"]},超级管理员:{navigation:["dashboard","user-management","device-management","organization-management","role-management","system-settings","reports"],device:["device.view","device.connect","device.manage","device.assign","device.force_disconnect"],management:["can_manage_users","can_manage_devices","can_view_reports"]},管理员:{navigation:["dashboard","user-management","device-management","organization-management","reports"],device:["device.view","device.connect","device.assign","device.force_disconnect"],management:["can_manage_users","can_view_reports"]},普通用户:{navigation:["dashboard","device-management"],device:["device.view","device.connect"],management:[]},新用户:{navigation:["dashboard"],device:[],management:[]}}[s.name],d=t?t.navigation:s.navigation_permissions||s.permissions||[],v=t?t.device:s.device_sub_permissions||s.device_permissions||[],f=t?t.management:[];Object.assign(o,{...s,navigation_permissions:d,device_sub_permissions:v,can_manage_users:f.includes("can_manage_users"),can_manage_devices:f.includes("can_manage_devices"),can_view_reports:f.includes("can_view_reports")}),console.log("编辑角色权限数据:",{roleName:s.name,template:t,navigationPermissions:d,deviceSubPermissions:v,managementPermissions:f}),I(),c.value=!1,P.value=!0},te=s=>{V.value=s,F.value=!0},j=()=>{var s;Object.assign(o,{name:"",description:"",level_scope:0,permissions:[],navigation_permissions:[],device_sub_permissions:[],can_manage_users:!1,can_manage_devices:!1,can_view_reports:!1}),(s=z.value)==null||s.resetFields(),I(),c.value=!1},le=()=>{c.value=!0,g.info("现在可以编辑权限配置")},re=()=>{c.value=!1,P.value=!1,j()},de=async()=>{try{await ue(),c.value=!1}catch(s){console.error("保存角色失败:",s)}},I=()=>{S.navigation_permissions=[...o.navigation_permissions],S.device_sub_permissions=[...o.device_sub_permissions]},ce=()=>{I(),g.success("权限配置已确认")},me=()=>{o.navigation_permissions=[...S.navigation_permissions],o.device_sub_permissions=[...S.device_sub_permissions],g.info("权限配置已重置")},ue=async()=>{if(z.value)try{await z.value.validate(),h.value=!0;const s={...o},e=ls(s.permissions);if(!e.isValid){g.error(`无效的权限配置: ${e.invalidPermissions.join(", ")}`);return}R.value?(await is(o.id,s),g.success("角色更新成功"),await ge(o.id,s)):(await ns(s),g.success("角色创建成功")),P.value=!1,await B(),await fe(o.id)}catch(s){g.error(R.value?"角色更新失败":"角色创建失败"),console.error("Save role error:",s)}finally{h.value=!1}},_e=async s=>{try{await ss.confirm(`确定要删除角色 "${s.name}" 吗？此操作不可恢复。`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await os(s.id),g.success("角色删除成功"),await B()}catch(e){e!=="cancel"&&(g.error("角色删除失败"),console.error("Delete role error:",e))}},ve=s=>s.name==="全域管理员",pe=s=>["全域管理员","超级管理员","管理员","普通用户","新用户"].includes(s.name),ge=async(s,e)=>{try{typeof window<"u"&&window.dispatchEvent(new CustomEvent("rolePermissionUpdated",{detail:{roleId:s,roleName:e.name,permissions:{navigation:e.navigation_permissions||[],device:e.device_sub_permissions||[],management:{can_manage_users:e.can_manage_users,can_manage_devices:e.can_manage_devices,can_view_reports:e.can_view_reports}},timestamp:new Date().toISOString()}})),console.log("权限更新事件已发送:",s,e.name)}catch(t){console.error("发送权限更新事件失败:",t)}},fe=async s=>{try{if(!s)return;const e=await getRoleById(s);R.value&&o.id===s&&Object.assign(o,{...e,navigation_permissions:e.navigation_permissions||[],device_sub_permissions:e.device_sub_permissions||[]}),console.log("角色权限已刷新:",s)}catch(e){console.error("刷新角色权限失败:",e)}},ye=()=>{typeof window<"u"&&(window.addEventListener("rolePermissionUpdated",s=>{const{roleId:e,roleName:t,permissions:d,timestamp:v}=s.detail;console.log("接收到权限更新事件:",{roleId:e,roleName:t,timestamp:v});const f=y.value.findIndex(C=>C.id===e);f!==-1&&(y.value[f]={...y.value[f],navigation_permissions:d.navigation,device_sub_permissions:d.device,...d.management}),R.value&&o.id===e&&Object.assign(o,{...o,navigation_permissions:d.navigation,device_sub_permissions:d.device,...d.management}),g.success(`角色"${t}"权限已同步更新`)}),window.addEventListener("devicePermissionChanged",s=>{const{userId:e,deviceId:t,action:d,timestamp:v}=s.detail;console.log("设备权限变更:",{userId:e,deviceId:t,action:d,timestamp:v}),B()}))},be=()=>{typeof window<"u"&&(window.removeEventListener("rolePermissionUpdated",()=>{}),window.removeEventListener("devicePermissionChanged",()=>{}))};return Ee(()=>{B(),ye()}),Re(()=>{be()}),(s,e)=>{const t=he,d=ze,v=Oe,f=De,C=Te,U=Fe,D=Ae,we=je,O=Me,A=Je,J=He,ke=$e,W=Ue,L=es,Pe=Qe,Ve=Ne;return r(),x("div",rs,[u("div",ds,[e[9]||(e[9]=u("div",{class:"header-content"},[u("h2",null,"角色管理"),u("p",null,"管理系统角色和权限分配，仅超级管理员可访问")],-1)),u("div",cs,[n(t,{type:"primary",onClick:ie,icon:N(xe)},{default:a(()=>e[8]||(e[8]=[l(" 创建角色 ",-1)])),_:1,__:[8]},8,["icon"])])]),u("div",ms,[Se((r(),_(f,{data:y.value,style:{width:"100%"}},{default:a(()=>[n(v,{prop:"name",label:"角色名称",width:"150"},{default:a(({row:i})=>[u("div",us,[i.is_system_role?(r(),_(d,{key:0,type:"danger",size:"small"},{default:a(()=>e[10]||(e[10]=[l("系统",-1)])),_:1,__:[10]})):p("",!0),l(" "+w(i.name),1)])]),_:1}),n(v,{prop:"description",label:"角色描述",width:"140"}),n(v,{label:"权限范围",width:"100"},{default:a(({row:i})=>[i.level_scope===0?(r(),_(d,{key:0,type:"warning"},{default:a(()=>e[11]||(e[11]=[l("无限制",-1)])),_:1,__:[11]})):(r(),_(d,{key:1,type:"info"},{default:a(()=>[l(w(i.level_scope)+"级",1)]),_:2},1024))]),_:1}),n(v,{label:"状态",width:"80"},{default:a(({row:i})=>[n(d,{type:i.is_active?"success":"danger"},{default:a(()=>[l(w(i.is_active?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),n(v,{label:"操作","min-width":"250",fixed:"right"},{default:a(({row:i})=>[n(t,{size:"small",onClick:Y=>te(i),icon:N(Be)},{default:a(()=>e[12]||(e[12]=[l(" 查看 ",-1)])),_:2,__:[12]},1032,["onClick","icon"]),ve(i)?p("",!0):(r(),_(t,{key:0,size:"small",type:"primary",onClick:Y=>oe(i),icon:N(Ie)},{default:a(()=>e[13]||(e[13]=[l(" 编辑 ",-1)])),_:2,__:[13]},1032,["onClick","icon"])),pe(i)?p("",!0):(r(),_(t,{key:1,size:"small",type:"danger",onClick:Y=>_e(i),icon:N(Le)},{default:a(()=>e[14]||(e[14]=[l(" 删除 ",-1)])),_:2,__:[14]},1032,["onClick","icon"]))]),_:1})]),_:1},8,["data"])),[[Ve,E.value]])]),n(W,{modelValue:P.value,"onUpdate:modelValue":e[6]||(e[6]=i=>P.value=i),title:R.value?"编辑角色":"创建角色",width:"900px",onClose:j},{footer:a(()=>[u("span",ys,[c.value?p("",!0):(r(),_(t,{key:0,onClick:le},{default:a(()=>e[22]||(e[22]=[l("开始修改",-1)])),_:1,__:[22]})),c.value?(r(),_(t,{key:1,onClick:re},{default:a(()=>e[23]||(e[23]=[l("取消修改",-1)])),_:1,__:[23]})):p("",!0),c.value?(r(),_(t,{key:2,type:"primary",onClick:de,loading:h.value},{default:a(()=>e[24]||(e[24]=[l(" 确认保存 ",-1)])),_:1,__:[24]},8,["loading"])):p("",!0)])]),default:a(()=>[n(ke,{model:o,rules:ne,ref_key:"roleFormRef",ref:z,"label-width":"120px"},{default:a(()=>[n(U,{label:"角色名称",prop:"name"},{default:a(()=>[n(C,{modelValue:o.name,"onUpdate:modelValue":e[0]||(e[0]=i=>o.name=i),placeholder:"请输入角色名称"},null,8,["modelValue"])]),_:1}),n(U,{label:"角色描述",prop:"description"},{default:a(()=>[n(C,{modelValue:o.description,"onUpdate:modelValue":e[1]||(e[1]=i=>o.description=i),type:"textarea",rows:3,placeholder:"请输入角色描述"},null,8,["modelValue"])]),_:1}),n(U,{label:"权限层级"},{default:a(()=>[n(we,{modelValue:o.level_scope,"onUpdate:modelValue":e[2]||(e[2]=i=>o.level_scope=i),placeholder:"选择权限层级范围"},{default:a(()=>[n(D,{label:"无限制",value:0}),n(D,{label:"1级权限",value:1}),n(D,{label:"2级权限",value:2}),n(D,{label:"3级权限",value:3}),n(D,{label:"4级权限",value:4})]),_:1},8,["modelValue"])]),_:1}),n(U,{label:"管理权限"},{default:a(()=>[n(A,{modelValue:G.value,"onUpdate:modelValue":e[3]||(e[3]=i=>G.value=i)},{default:a(()=>[n(O,{label:"can_manage_users"},{default:a(()=>e[15]||(e[15]=[l("用户管理",-1)])),_:1,__:[15]}),n(O,{label:"can_manage_devices"},{default:a(()=>e[16]||(e[16]=[l("设备管理",-1)])),_:1,__:[16]}),n(O,{label:"can_view_reports"},{default:a(()=>e[17]||(e[17]=[l("报告查看",-1)])),_:1,__:[17]})]),_:1},8,["modelValue"])]),_:1}),n(U,{label:"功能权限"},{default:a(()=>[u("div",_s,[e[21]||(e[21]=u("h4",null,"一级权限（导航菜单）",-1)),n(A,{modelValue:o.navigation_permissions,"onUpdate:modelValue":e[4]||(e[4]=i=>o.navigation_permissions=i),class:"navigation-group-grid",disabled:m.value||!c.value},{default:a(()=>[(r(),x(K,null,Q(X,i=>n(O,{key:i.key,label:i.key,disabled:m.value||!c.value,class:q(["nav-permission-item-grid",{"global-admin-item":m.value,"is-disabled":m.value}])},{default:a(()=>[u("div",vs,[n(J,{class:q(["nav-icon",{"global-admin-icon":m.value}])},{default:a(()=>[(r(),_(Ke(i.icon)))]),_:2},1032,["class"]),u("span",null,w(i.name),1),m.value?(r(),_(J,{key:0,class:"admin-badge"},{default:a(()=>[n(N(ae))]),_:1})):p("",!0)])]),_:2},1032,["label","disabled","class"])),64))]),_:1},8,["modelValue","disabled"]),o.navigation_permissions.includes("device-center")||m.value?(r(),x("div",ps,[e[18]||(e[18]=u("h4",null,"设备管理中心 - 二级权限",-1)),n(A,{modelValue:o.device_sub_permissions,"onUpdate:modelValue":e[5]||(e[5]=i=>o.device_sub_permissions=i),class:"sub-permission-group",disabled:m.value||!c.value},{default:a(()=>[(r(),x(K,null,Q(Z,i=>n(O,{key:i.key,label:i.key,disabled:m.value||!c.value,class:q(["sub-permission-item",{"global-admin-item":m.value,"is-disabled":m.value||!c.value}])},{default:a(()=>[u("span",null,w(i.name),1),m.value?(r(),_(J,{key:0,class:"admin-badge"},{default:a(()=>[n(N(ae))]),_:1})):p("",!0)]),_:2},1032,["label","disabled","class"])),64))]),_:1},8,["modelValue","disabled"])])):p("",!0),!m.value&&c.value?(r(),x("div",gs,[n(t,{type:"success",onClick:ce,disabled:!T.value,size:"small"},{default:a(()=>e[19]||(e[19]=[l(" 确定修改 ",-1)])),_:1,__:[19]},8,["disabled"]),n(t,{onClick:me,disabled:!T.value,size:"small"},{default:a(()=>e[20]||(e[20]=[l(" 放弃修改 ",-1)])),_:1,__:[20]},8,["disabled"]),T.value?(r(),x("span",fs," 权限配置已修改 ")):p("",!0)])):p("",!0)])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),n(W,{modelValue:F.value,"onUpdate:modelValue":e[7]||(e[7]=i=>F.value=i),title:"角色详情",width:"500px"},{default:a(()=>[V.value?(r(),x("div",bs,[n(Pe,{column:1,border:""},{default:a(()=>[n(L,{label:"角色名称"},{default:a(()=>[l(w(V.value.name),1)]),_:1}),n(L,{label:"角色描述"},{default:a(()=>[l(w(V.value.description),1)]),_:1}),n(L,{label:"系统角色"},{default:a(()=>[n(d,{type:V.value.is_system_role?"danger":"success"},{default:a(()=>[l(w(V.value.is_system_role?"是":"否"),1)]),_:1},8,["type"])]),_:1}),n(L,{label:"权限层级"},{default:a(()=>[V.value.level_scope===0?(r(),_(d,{key:0,type:"warning"},{default:a(()=>e[25]||(e[25]=[l("无限制",-1)])),_:1,__:[25]})):(r(),_(d,{key:1,type:"info"},{default:a(()=>[l(w(V.value.level_scope)+"级",1)]),_:1}))]),_:1})]),_:1})])):p("",!0)]),_:1},8,["modelValue"])])}}},Is=Ce(ws,[["__scopeId","data-v-d7e0cc1e"]]);export{Is as default};
