import{_ as Ve}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                   *//* empty css                             *//* empty css                   *//* empty css                     *//* empty css                          *//* empty css                    *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  */import{ds as I,r as k,a as H,a9 as q,o as Ce,bN as xe,c as R,d as u,e as i,w as n,p as S,cR as Re,m as he,ab as Ne,ac as Se,y as v,x as De,s as f,ah as Ue,i as c,n as r,ak as Oe,z as b,a7 as ze,t as P,cW as Be,cV as Fe,cX as Ie,f as Le,j as Te,k as $e,ai as je,aj as Ae,cN as Je,cO as qe,a5 as K,a6 as Q,X as Me,Y as Ge,Z as Xe,_ as ee,L as se,$ as We,a0 as Ye,R as Ze,a2 as M,E as He,b_ as Ke,dl as ae,af as Qe,ae as es,a4 as ss}from"./index-BzawHLSs.js";function as(){return I.get("/api/v2/roles/")}function ns(h){return I.post("/api/v2/roles/",h)}function is(h,D){return I.put(`/api/v2/roles/${h}`,D)}function os(h){return I.delete(`/api/v2/roles/${h}`)}const ts={class:"role-management"},ls={class:"page-header"},rs={class:"header-actions"},cs={class:"role-list"},ds={class:"role-name"},ms={class:"navigation-permissions"},_s={class:"nav-permission-content"},us={key:0,class:"sub-permissions"},vs={key:1,class:"permission-actions"},ps={key:0,class:"change-indicator"},gs={class:"dialog-footer"},fs={key:0,class:"role-detail"},bs={__name:"index",setup(h){const D=k(!1),L=k(!1),N=k(!1),T=k(!1),C=k(!1),m=k(!1),p=k([]),E=k(null),O=k(),a=H({name:"",description:"",level_scope:0,permissions:[],navigation_permissions:[],device_sub_permissions:[],can_manage_users:!1,can_manage_devices:!1,can_view_reports:!1}),V=H({navigation_permissions:[],device_sub_permissions:[]}),G=q({get(){const s=[];return a.can_manage_users&&s.push("can_manage_users"),a.can_manage_devices&&s.push("can_manage_devices"),a.can_view_reports&&s.push("can_view_reports"),s},set(s){a.can_manage_users=s.includes("can_manage_users"),a.can_manage_devices=s.includes("can_manage_devices"),a.can_view_reports=s.includes("can_view_reports")}}),_=q(()=>a.name==="全域管理员"),$=q(()=>{if(_.value)return!1;const s=JSON.stringify(a.navigation_permissions.sort())!==JSON.stringify(V.navigation_permissions.sort()),e=JSON.stringify(a.device_sub_permissions.sort())!==JSON.stringify(V.device_sub_permissions.sort());return s||e}),ne={name:[{required:!0,message:"请输入角色名称",trigger:"blur"},{min:2,max:50,message:"角色名称长度在 2 到 50 个字符",trigger:"blur"}],description:[{required:!0,message:"请输入角色描述",trigger:"blur"}]},X=[{key:"dashboard",name:"工作台",icon:Me},{key:"applications",name:"处理事项",icon:Ge},{key:"org-users",name:"组织与用户管理",icon:Xe},{key:"user-registration",name:"新用户审核",icon:ee},{key:"device-center",name:"设备管理中心",icon:se},{key:"client-management",name:"设备绑定中心",icon:se},{key:"role-management",name:"角色管理",icon:ee},{key:"system-settings",name:"系统设置",icon:We},{key:"data-dashboard",name:"数据大屏",icon:Ye},{key:"profile-management",name:"个人资料管理",icon:Ze}],W=[{key:"usb-devices",name:"USB设备管理"},{key:"slave-servers",name:"分布式节点管理"},{key:"device-groups",name:"资源调度分组"},{key:"permission-assignment",name:"授权范围管理"}],z=async()=>{D.value=!0;try{const s=await as();let e=s;s&&s.success&&s.data&&(console.log("🔧 loadRoles - 检测到API中间件包装格式，提取data字段"),e=s.data),console.log("loadRoles - 处理后的角色数据:",e),e&&e.roles?(p.value=Array.isArray(e.roles)?e.roles:[],console.log("加载角色列表成功:",p.value.length,"个角色"),e.filtered_by_permission&&console.log("权限过滤已生效")):Array.isArray(e)?(p.value=e,console.log("加载角色列表成功（数组格式）:",p.value.length,"个角色")):(p.value=[],console.log("角色数据格式异常，设置为空数组"))}catch(s){f.error("加载角色列表失败"),console.error("Load roles error:",s)}finally{D.value=!1}},ie=()=>{C.value=!1,j(),B(),N.value=!0},oe=s=>{C.value=!0,console.log("编辑角色原始数据:",s);const o={全域管理员:{navigation:X.map(w=>w.key),device:W.map(w=>w.key),management:{can_manage_users:!0,can_manage_devices:!0,can_view_reports:!0}},超级管理员:{navigation:["dashboard","user-management","device-management","organization-management","role-management","system-settings","reports"],device:["device.view","device.connect","device.manage","device.assign","device.force_disconnect"],management:{can_manage_users:!0,can_manage_devices:!0,can_view_reports:!0}},管理员:{navigation:["dashboard","user-management","device-management","organization-management","reports"],device:["device.view","device.connect","device.assign","device.force_disconnect"],management:{can_manage_users:!0,can_manage_devices:!1,can_view_reports:!0}},普通用户:{navigation:["dashboard","device-management"],device:["device.view","device.connect"],management:{can_manage_users:!1,can_manage_devices:!1,can_view_reports:!1}},新用户:{navigation:["dashboard"],device:[],management:{can_manage_users:!1,can_manage_devices:!1,can_view_reports:!1}}}[s.name],l=s.navigation_permissions||[],d=s.device_sub_permissions||[],g=l.length>0?l:o?o.navigation:[],x=d.length>0?d:o?o.device:[];console.log("权限配置详情:",{roleName:s.name,currentNav:l,currentDevice:d,finalNav:g,finalDevice:x});const y=o?o.management:{};Object.assign(a,{id:s.id,name:s.name,description:s.description,permissions:s.permissions||[],navigation_permissions:g,device_sub_permissions:x,level_scope:s.level_scope||1,can_manage_users:s.can_manage_users!==void 0?s.can_manage_users:y.can_manage_users,can_manage_devices:s.can_manage_devices!==void 0?s.can_manage_devices:y.can_manage_devices,can_view_reports:s.can_view_reports!==void 0?s.can_view_reports:y.can_view_reports}),console.log("编辑角色最终数据:",{roleName:s.name,template:o,finalForm:a}),B(),m.value=!1,N.value=!0},te=s=>{E.value=s,T.value=!0},j=()=>{var s;Object.assign(a,{name:"",description:"",level_scope:0,permissions:[],navigation_permissions:[],device_sub_permissions:[],can_manage_users:!1,can_manage_devices:!1,can_view_reports:!1}),(s=O.value)==null||s.resetFields(),B(),m.value=!1},le=()=>{m.value=!0,f.info("现在可以编辑权限配置")},re=()=>{m.value=!1,N.value=!1,j()},ce=async()=>{try{await _e(),m.value=!1}catch(s){console.error("保存角色失败:",s)}},B=()=>{V.navigation_permissions=[...a.navigation_permissions],V.device_sub_permissions=[...a.device_sub_permissions]},de=()=>{B(),f.success("权限配置已确认")},me=()=>{a.navigation_permissions=[...V.navigation_permissions],a.device_sub_permissions=[...V.device_sub_permissions],f.info("权限配置已重置")},_e=async()=>{if(O.value)try{await O.value.validate(),L.value=!0;const s={name:a.name,description:a.description,permissions:a.permissions||[],navigation_permissions:a.navigation_permissions||[],device_sub_permissions:a.device_sub_permissions||[],level_scope:a.level_scope||1,can_manage_users:a.can_manage_users||!1,can_manage_devices:a.can_manage_devices||!1,can_view_reports:a.can_view_reports||!1};if(console.log("保存角色数据:",s),C.value){const e=await is(a.id,s);console.log("角色更新响应:",e),f.success("角色更新成功");try{await ge(a.id,s),await new Promise(o=>setTimeout(o,100)),await fe(a.id),console.log("角色权限同步流程完成:",a.id)}catch(o){console.error("权限同步过程中出现错误:",o),f.warning("权限更新成功，但同步显示可能有延迟")}}else{const e=await ns(s);console.log("角色创建响应:",e),f.success("角色创建成功")}await z(),N.value=!1}catch(s){f.error(C.value?"角色更新失败":"角色创建失败"),console.error("Save role error:",s)}finally{L.value=!1}},ue=async s=>{try{await ss.confirm(`确定要删除角色 "${s.name}" 吗？此操作不可恢复。`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await os(s.id),f.success("角色删除成功"),await z()}catch(e){e!=="cancel"&&(f.error("角色删除失败"),console.error("Delete role error:",e))}},ve=s=>s.name==="全域管理员",pe=s=>["全域管理员","超级管理员","管理员","普通用户","新用户"].includes(s.name),ge=async(s,e)=>{try{typeof window<"u"&&window.dispatchEvent(new CustomEvent("rolePermissionUpdated",{detail:{roleId:s,roleName:e.name,permissions:{navigation:e.navigation_permissions||[],device:e.device_sub_permissions||[],management:{can_manage_users:e.can_manage_users,can_manage_devices:e.can_manage_devices,can_view_reports:e.can_view_reports}},timestamp:new Date().toISOString()}})),console.log("权限更新事件已发送:",s,e.name)}catch(o){console.error("发送权限更新事件失败:",o)}},fe=async s=>{try{if(!s)return;console.log("开始刷新角色权限:",s);const e=await getRoleById(s),o=e.data||e;if(console.log("获取角色详情:",o),C.value&&a.id===s){const d=JSON.parse(JSON.stringify({id:o.id,name:o.name,description:o.description,permissions:o.permissions||[],navigation_permissions:o.navigation_permissions||[],device_sub_permissions:o.device_sub_permissions||[],level_scope:o.level_scope||1,can_manage_users:o.can_manage_users||!1,can_manage_devices:o.can_manage_devices||!1,can_view_reports:o.can_view_reports||!1}));Object.keys(a).forEach(g=>{g in d&&(a[g]=d[g])}),V.navigation_permissions=[...d.navigation_permissions],V.device_sub_permissions=[...d.device_sub_permissions],console.log("表单数据已完全更新:",{roleId:s,navigationPermissions:a.navigation_permissions,devicePermissions:a.device_sub_permissions,managementPermissions:{can_manage_users:a.can_manage_users,can_manage_devices:a.can_manage_devices,can_view_reports:a.can_view_reports}})}const l=p.value.findIndex(d=>d.id===s);l!==-1&&(p.value[l]={...p.value[l],...o}),console.log("角色权限刷新完成:",s)}catch(e){console.error("刷新角色权限失败:",e),f.error("刷新角色权限失败")}},be=()=>{typeof window<"u"&&(window.addEventListener("rolePermissionUpdated",s=>{const{roleId:e,roleName:o,permissions:l,timestamp:d}=s.detail;console.log("接收到权限更新事件:",{roleId:e,roleName:o,timestamp:d});const g=p.value.findIndex(x=>x.id===e);g!==-1&&(p.value[g]={...p.value[g],navigation_permissions:l.navigation,device_sub_permissions:l.device,...l.management}),C.value&&a.id===e&&Object.assign(a,{...a,navigation_permissions:l.navigation,device_sub_permissions:l.device,...l.management}),f.success(`角色"${o}"权限已同步更新`)}),window.addEventListener("devicePermissionChanged",s=>{const{userId:e,deviceId:o,action:l,timestamp:d}=s.detail;console.log("设备权限变更:",{userId:e,deviceId:o,action:l,timestamp:d}),z()}))},ye=()=>{typeof window<"u"&&(window.removeEventListener("rolePermissionUpdated",()=>{}),window.removeEventListener("devicePermissionChanged",()=>{}))};return Ce(()=>{z(),be()}),xe(()=>{ye()}),(s,e)=>{const o=he,l=ze,d=Oe,g=Ue,x=$e,y=Te,w=Ae,we=je,U=qe,A=Je,J=He,ke=Le,Y=De,F=es,Pe=Qe,Ee=Se;return c(),R("div",ts,[u("div",ls,[e[9]||(e[9]=u("div",{class:"header-content"},[u("h2",null,"角色管理"),u("p",null,"管理系统角色和权限分配，仅超级管理员可访问")],-1)),u("div",rs,[i(o,{type:"primary",onClick:ie,icon:S(Re)},{default:n(()=>e[8]||(e[8]=[r(" 创建角色 ",-1)])),_:1,__:[8]},8,["icon"])])]),u("div",cs,[Ne((c(),v(g,{data:p.value,style:{width:"100%"}},{default:n(()=>[i(d,{prop:"name",label:"角色名称",width:"150"},{default:n(({row:t})=>[u("div",ds,[t.is_system_role?(c(),v(l,{key:0,type:"danger",size:"small"},{default:n(()=>e[10]||(e[10]=[r("系统",-1)])),_:1,__:[10]})):b("",!0),r(" "+P(t.name),1)])]),_:1}),i(d,{prop:"description",label:"角色描述",width:"140"}),i(d,{label:"权限范围",width:"100"},{default:n(({row:t})=>[t.level_scope===0?(c(),v(l,{key:0,type:"warning"},{default:n(()=>e[11]||(e[11]=[r("无限制",-1)])),_:1,__:[11]})):(c(),v(l,{key:1,type:"info"},{default:n(()=>[r(P(t.level_scope)+"级",1)]),_:2},1024))]),_:1}),i(d,{label:"状态",width:"80"},{default:n(({row:t})=>[i(l,{type:t.is_active?"success":"danger"},{default:n(()=>[r(P(t.is_active?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),i(d,{label:"操作","min-width":"250",fixed:"right"},{default:n(({row:t})=>[i(o,{size:"small",onClick:Z=>te(t),icon:S(Be)},{default:n(()=>e[12]||(e[12]=[r(" 查看 ",-1)])),_:2,__:[12]},1032,["onClick","icon"]),ve(t)?b("",!0):(c(),v(o,{key:0,size:"small",type:"primary",onClick:Z=>oe(t),icon:S(Fe)},{default:n(()=>e[13]||(e[13]=[r(" 编辑 ",-1)])),_:2,__:[13]},1032,["onClick","icon"])),pe(t)?b("",!0):(c(),v(o,{key:1,size:"small",type:"danger",onClick:Z=>ue(t),icon:S(Ie)},{default:n(()=>e[14]||(e[14]=[r(" 删除 ",-1)])),_:2,__:[14]},1032,["onClick","icon"]))]),_:1})]),_:1},8,["data"])),[[Ee,D.value]])]),i(Y,{modelValue:N.value,"onUpdate:modelValue":e[6]||(e[6]=t=>N.value=t),title:C.value?"编辑角色":"创建角色",width:"900px",onClose:j},{footer:n(()=>[u("span",gs,[m.value?b("",!0):(c(),v(o,{key:0,onClick:le},{default:n(()=>e[22]||(e[22]=[r("开始修改",-1)])),_:1,__:[22]})),m.value?(c(),v(o,{key:1,onClick:re},{default:n(()=>e[23]||(e[23]=[r("取消修改",-1)])),_:1,__:[23]})):b("",!0),m.value?(c(),v(o,{key:2,type:"primary",onClick:ce,loading:L.value},{default:n(()=>e[24]||(e[24]=[r(" 确认保存 ",-1)])),_:1,__:[24]},8,["loading"])):b("",!0)])]),default:n(()=>[i(ke,{model:a,rules:ne,ref_key:"roleFormRef",ref:O,"label-width":"120px"},{default:n(()=>[i(y,{label:"角色名称",prop:"name"},{default:n(()=>[i(x,{modelValue:a.name,"onUpdate:modelValue":e[0]||(e[0]=t=>a.name=t),placeholder:"请输入角色名称"},null,8,["modelValue"])]),_:1}),i(y,{label:"角色描述",prop:"description"},{default:n(()=>[i(x,{modelValue:a.description,"onUpdate:modelValue":e[1]||(e[1]=t=>a.description=t),type:"textarea",rows:3,placeholder:"请输入角色描述"},null,8,["modelValue"])]),_:1}),i(y,{label:"权限层级"},{default:n(()=>[i(we,{modelValue:a.level_scope,"onUpdate:modelValue":e[2]||(e[2]=t=>a.level_scope=t),placeholder:"选择权限层级范围"},{default:n(()=>[i(w,{label:"无限制",value:0}),i(w,{label:"1级权限",value:1}),i(w,{label:"2级权限",value:2}),i(w,{label:"3级权限",value:3}),i(w,{label:"4级权限",value:4})]),_:1},8,["modelValue"])]),_:1}),i(y,{label:"管理权限"},{default:n(()=>[i(A,{modelValue:G.value,"onUpdate:modelValue":e[3]||(e[3]=t=>G.value=t)},{default:n(()=>[i(U,{label:"can_manage_users"},{default:n(()=>e[15]||(e[15]=[r("用户管理",-1)])),_:1,__:[15]}),i(U,{label:"can_manage_devices"},{default:n(()=>e[16]||(e[16]=[r("设备管理",-1)])),_:1,__:[16]}),i(U,{label:"can_view_reports"},{default:n(()=>e[17]||(e[17]=[r("报告查看",-1)])),_:1,__:[17]})]),_:1},8,["modelValue"])]),_:1}),i(y,{label:"功能权限"},{default:n(()=>[u("div",ms,[e[21]||(e[21]=u("h4",null,"一级权限（导航菜单）",-1)),i(A,{modelValue:a.navigation_permissions,"onUpdate:modelValue":e[4]||(e[4]=t=>a.navigation_permissions=t),class:"navigation-group-grid",disabled:_.value||!m.value},{default:n(()=>[(c(),R(K,null,Q(X,t=>i(U,{key:t.key,label:t.key,disabled:_.value||!m.value,class:M(["nav-permission-item-grid",{"global-admin-item":_.value,"is-disabled":_.value}])},{default:n(()=>[u("div",_s,[i(J,{class:M(["nav-icon",{"global-admin-icon":_.value}])},{default:n(()=>[(c(),v(Ke(t.icon)))]),_:2},1032,["class"]),u("span",null,P(t.name),1),_.value?(c(),v(J,{key:0,class:"admin-badge"},{default:n(()=>[i(S(ae))]),_:1})):b("",!0)])]),_:2},1032,["label","disabled","class"])),64))]),_:1},8,["modelValue","disabled"]),a.navigation_permissions.includes("device-center")||_.value?(c(),R("div",us,[e[18]||(e[18]=u("h4",null,"设备管理中心 - 二级权限",-1)),i(A,{modelValue:a.device_sub_permissions,"onUpdate:modelValue":e[5]||(e[5]=t=>a.device_sub_permissions=t),class:"sub-permission-group",disabled:_.value||!m.value},{default:n(()=>[(c(),R(K,null,Q(W,t=>i(U,{key:t.key,label:t.key,disabled:_.value||!m.value,class:M(["sub-permission-item",{"global-admin-item":_.value,"is-disabled":_.value||!m.value}])},{default:n(()=>[u("span",null,P(t.name),1),_.value?(c(),v(J,{key:0,class:"admin-badge"},{default:n(()=>[i(S(ae))]),_:1})):b("",!0)]),_:2},1032,["label","disabled","class"])),64))]),_:1},8,["modelValue","disabled"])])):b("",!0),!_.value&&m.value?(c(),R("div",vs,[i(o,{type:"success",onClick:de,disabled:!$.value,size:"small"},{default:n(()=>e[19]||(e[19]=[r(" 确定修改 ",-1)])),_:1,__:[19]},8,["disabled"]),i(o,{onClick:me,disabled:!$.value,size:"small"},{default:n(()=>e[20]||(e[20]=[r(" 放弃修改 ",-1)])),_:1,__:[20]},8,["disabled"]),$.value?(c(),R("span",ps," 权限配置已修改 ")):b("",!0)])):b("",!0)])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),i(Y,{modelValue:T.value,"onUpdate:modelValue":e[7]||(e[7]=t=>T.value=t),title:"角色详情",width:"500px"},{default:n(()=>[E.value?(c(),R("div",fs,[i(Pe,{column:1,border:""},{default:n(()=>[i(F,{label:"角色名称"},{default:n(()=>[r(P(E.value.name),1)]),_:1}),i(F,{label:"角色描述"},{default:n(()=>[r(P(E.value.description),1)]),_:1}),i(F,{label:"系统角色"},{default:n(()=>[i(l,{type:E.value.is_system_role?"danger":"success"},{default:n(()=>[r(P(E.value.is_system_role?"是":"否"),1)]),_:1},8,["type"])]),_:1}),i(F,{label:"权限层级"},{default:n(()=>[E.value.level_scope===0?(c(),v(l,{key:0,type:"warning"},{default:n(()=>e[25]||(e[25]=[r("无限制",-1)])),_:1,__:[25]})):(c(),v(l,{key:1,type:"info"},{default:n(()=>[r(P(E.value.level_scope)+"级",1)]),_:1}))]),_:1})]),_:1})])):b("",!0)]),_:1},8,["modelValue"])])}}},zs=Ve(bs,[["__scopeId","data-v-f1d33f32"]]);export{zs as default};
