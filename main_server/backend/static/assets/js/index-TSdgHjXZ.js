import{_ as ne}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                   *//* empty css                   *//* empty css                 *//* empty css                       *//* empty css                 *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                     *//* empty css                  */import{u as re,r as c,a as T,o as ie,c as pe,d as y,e as t,y as w,z,p as q,w as l,f as ue,a9 as de,aa as ce,ab as me,x as _e,s as A,m as ge,ac as fe,b as ve,h as ye,i as b,n as s,E as be,j as we,ad as Ve,ae as xe,af as ke,a7 as Ce,t as V,v as R,ag as Ee,ah as he,k as Te,a8 as ze}from"./index-BBw1jc_Y.js";import{a as B}from"./applicationRequests-wSECGe-Z.js";const qe={class:"applications-container"},Ae={class:"page-header"},Re={class:"filter-container"},je={class:"table-container"},Pe={class:"pagination-container"},De={__name:"index",setup(Se){const M=ve(),x=re(),k=c(!1),f=c([]),i=T({status:"",application_type:"",priority:""}),n=T({page:1,size:20,total:0}),v=c(!1),j=c(""),C=c(!1),P=c(),D=c(null),p=T({status:"",response_content:""}),N={status:[{required:!0,message:"请选择处理结果",trigger:"change"}],response_content:[{required:!0,message:"请输入处理意见",trigger:"blur"}]},m=async()=>{try{k.value=!0;const a={page:n.page,size:n.size,...i};Object.keys(a).forEach(_=>{(a[_]===""||a[_]===null||a[_]===void 0)&&delete a[_]});const e=await B.getApplicationRequests(a);e.data&&Array.isArray(e.data)?(f.value=e.data,n.total=e.total||0):Array.isArray(e)?(f.value=e,n.total=e.length):(f.value=e.data||[],n.total=e.total||0),console.log("申请列表加载成功:",{count:f.value.length,total:n.total,page:n.page})}catch(a){console.error("加载申请列表失败:",a),A.error("加载申请列表失败")}finally{k.value=!1}},Y=()=>{Object.assign(i,{status:"",application_type:"",priority:""}),n.page=1,m()},S=a=>{M.push(`/applications/${a}`)},O=a=>{S(a.id)},U=(a,e)=>{D.value=a,p.status=e,p.response_content="",j.value=e==="approved"?"批准申请":"拒绝申请",v.value=!0},H=async()=>{try{await P.value.validate(),C.value=!0,await B.processApplicationRequest(D.value,{status:p.status,process_comment:p.response_content}),A.success("处理成功"),v.value=!1,m()}catch{A.error("处理申请失败")}finally{C.value=!1}},L=a=>ze(a).format("YYYY-MM-DD HH:mm"),G=a=>({pending:"warning",approved:"success",rejected:"danger",cancelled:"info"})[a]||"info",J=a=>({pending:"待处理",approved:"已批准",rejected:"已拒绝",cancelled:"已取消"})[a]||a,K=a=>({permission_request:"primary",role_change:"success",device_request:"warning",other:"info"})[a]||"info",Q=a=>({permission_request:"权限申请",role_change:"角色变更",device_request:"设备申请",other:"其他"})[a]||a,W=a=>({low:"info",normal:"primary",high:"warning",urgent:"danger"})[a]||"primary",X=a=>({low:"低",normal:"普通",high:"高",urgent:"紧急"})[a]||a;return ie(()=>{m()}),(a,e)=>{const _=ye("Plus"),Z=be,d=ge,r=xe,E=Ve,g=we,F=ue,u=ke,h=Ce,ee=fe,te=me,$=he,ae=Ee,oe=Te,le=_e,se=ce;return b(),pe("div",qe,[y("div",Ae,[e[11]||(e[11]=y("h2",{class:"page-title"},"处理事项",-1)),q(x).hasPermission("application.submit")?(b(),w(d,{key:0,type:"primary",onClick:e[0]||(e[0]=o=>a.$router.push("/applications/create"))},{default:l(()=>[t(Z,null,{default:l(()=>[t(_)]),_:1}),e[10]||(e[10]=s(" 创建申请 ",-1))]),_:1,__:[10]})):z("",!0)]),y("div",Re,[t(F,{model:i,inline:""},{default:l(()=>[t(g,{label:"状态"},{default:l(()=>[t(E,{modelValue:i.status,"onUpdate:modelValue":e[1]||(e[1]=o=>i.status=o),placeholder:"全部状态",clearable:""},{default:l(()=>[t(r,{label:"待处理",value:"pending"}),t(r,{label:"已批准",value:"approved"}),t(r,{label:"已拒绝",value:"rejected"}),t(r,{label:"已取消",value:"cancelled"})]),_:1},8,["modelValue"])]),_:1}),t(g,{label:"类型"},{default:l(()=>[t(E,{modelValue:i.application_type,"onUpdate:modelValue":e[2]||(e[2]=o=>i.application_type=o),placeholder:"全部类型",clearable:""},{default:l(()=>[t(r,{label:"权限申请",value:"permission_request"}),t(r,{label:"角色变更",value:"role_change"}),t(r,{label:"设备申请",value:"device_request"}),t(r,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1}),t(g,{label:"优先级"},{default:l(()=>[t(E,{modelValue:i.priority,"onUpdate:modelValue":e[3]||(e[3]=o=>i.priority=o),placeholder:"全部优先级",clearable:""},{default:l(()=>[t(r,{label:"低",value:"low"}),t(r,{label:"普通",value:"normal"}),t(r,{label:"高",value:"high"}),t(r,{label:"紧急",value:"urgent"})]),_:1},8,["modelValue"])]),_:1}),t(g,null,{default:l(()=>[t(d,{type:"primary",onClick:m},{default:l(()=>e[12]||(e[12]=[s("查询",-1)])),_:1,__:[12]}),t(d,{onClick:Y},{default:l(()=>e[13]||(e[13]=[s("重置",-1)])),_:1,__:[13]})]),_:1})]),_:1},8,["model"])]),y("div",je,[de((b(),w(ee,{data:f.value,stripe:"",style:{width:"100%"},onRowClick:O},{default:l(()=>[t(u,{prop:"id",label:"ID",width:"80"}),t(u,{prop:"title",label:"标题","min-width":"200","show-overflow-tooltip":""}),t(u,{prop:"application_type",label:"类型",width:"120"},{default:l(({row:o})=>[t(h,{type:K(o.application_type)},{default:l(()=>[s(V(Q(o.application_type)),1)]),_:2},1032,["type"])]),_:1}),t(u,{prop:"status",label:"状态",width:"100"},{default:l(({row:o})=>[t(h,{type:G(o.status)},{default:l(()=>[s(V(J(o.status)),1)]),_:2},1032,["type"])]),_:1}),t(u,{prop:"priority",label:"优先级",width:"100"},{default:l(({row:o})=>[t(h,{type:W(o.priority)},{default:l(()=>[s(V(X(o.priority)),1)]),_:2},1032,["type"])]),_:1}),t(u,{prop:"applicant_name",label:"申请人",width:"120"}),t(u,{prop:"organization_name",label:"所属组织",width:"150","show-overflow-tooltip":""}),t(u,{prop:"created_at",label:"创建时间",width:"160"},{default:l(({row:o})=>[s(V(L(o.created_at)),1)]),_:1}),t(u,{label:"操作",width:"200",fixed:"right"},{default:l(({row:o})=>[t(d,{type:"primary",size:"small",onClick:R(I=>S(o.id),["stop"])},{default:l(()=>e[14]||(e[14]=[s(" 查看 ",-1)])),_:2,__:[14]},1032,["onClick"]),o.status==="pending"&&q(x).hasPermission("application.process")?(b(),w(d,{key:0,type:"success",size:"small",onClick:R(I=>U(o.id,"approved"),["stop"])},{default:l(()=>e[15]||(e[15]=[s(" 批准 ",-1)])),_:2,__:[15]},1032,["onClick"])):z("",!0),o.status==="pending"&&q(x).hasPermission("application.process")?(b(),w(d,{key:1,type:"danger",size:"small",onClick:R(I=>U(o.id,"rejected"),["stop"])},{default:l(()=>e[16]||(e[16]=[s(" 拒绝 ",-1)])),_:2,__:[16]},1032,["onClick"])):z("",!0)]),_:1})]),_:1},8,["data"])),[[se,k.value]]),y("div",Pe,[t(te,{"current-page":n.page,"onUpdate:currentPage":e[4]||(e[4]=o=>n.page=o),"page-size":n.size,"onUpdate:pageSize":e[5]||(e[5]=o=>n.size=o),total:n.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:m,onCurrentChange:m},null,8,["current-page","page-size","total"])])]),t(le,{modelValue:v.value,"onUpdate:modelValue":e[9]||(e[9]=o=>v.value=o),title:j.value,width:"500px","close-on-click-modal":!1},{footer:l(()=>[t(d,{onClick:e[8]||(e[8]=o=>v.value=!1)},{default:l(()=>e[19]||(e[19]=[s("取消",-1)])),_:1,__:[19]}),t(d,{type:"primary",onClick:H,loading:C.value},{default:l(()=>e[20]||(e[20]=[s(" 确定 ",-1)])),_:1,__:[20]},8,["loading"])]),default:l(()=>[t(F,{ref_key:"processFormRef",ref:P,model:p,rules:N,"label-width":"80px"},{default:l(()=>[t(g,{label:"处理结果",prop:"status"},{default:l(()=>[t(ae,{modelValue:p.status,"onUpdate:modelValue":e[6]||(e[6]=o=>p.status=o)},{default:l(()=>[t($,{label:"approved"},{default:l(()=>e[17]||(e[17]=[s("批准",-1)])),_:1,__:[17]}),t($,{label:"rejected"},{default:l(()=>e[18]||(e[18]=[s("拒绝",-1)])),_:1,__:[18]})]),_:1},8,["modelValue"])]),_:1}),t(g,{label:"处理意见",prop:"response_content"},{default:l(()=>[t(oe,{modelValue:p.response_content,"onUpdate:modelValue":e[7]||(e[7]=o=>p.response_content=o),type:"textarea",rows:4,placeholder:"请输入处理意见"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},Ze=ne(De,[["__scopeId","data-v-3ed8e868"]]);export{Ze as default};
