import{_ as ae}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                   *//* empty css                   *//* empty css                     *//* empty css                  *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css               *//* empty css                *//* empty css                  */import{u as oe,r as _,aN as se,a as I,o as re,c as M,d as s,e as t,y as E,z as R,w as l,m as ne,p as c,cK as ie,ai as ue,x as de,s as f,b as pe,i as y,n as u,E as ce,c$ as me,cR as _e,cL as fe,t as b,d8 as ve,L as O,d6 as ge,a9 as ye,ac as be,af as we,a7 as Ve,aa as xe,f as ke,j as Ee,k as Ce,ad as Te,ae as $e,cM as he,a4 as De}from"./index-Cp0qD91m.js";import{u as Ue}from"./permission-assignment-CURw7bdX.js";import"./index-BmmbDJZi.js";const Be={class:"device-groups-container"},Fe={class:"toolbar"},Le={class:"stats-cards"},Re={class:"stat-content"},Se={class:"stat-number"},je={class:"stat-content"},Ge={class:"stat-number"},Pe={class:"stat-content"},ze={class:"stat-number"},Ne={class:"stat-content"},Ie={class:"stat-number"},Me={key:1},Oe={__name:"index",setup(He){const H=pe(),D=oe(),C=_(!1),U=_(!1),B=_(!1),V=_(!1),x=_(!1),v=_([]),T=se(()=>{const o=v.value.length,e=v.value.filter(p=>p.group_type==="server").length,i=v.value.filter(p=>p.group_type==="mixed").length,d=v.value.filter(p=>p.group_type==="single").length;return{total:o,server:e,mixed:i,single:d}}),F=_(),r=I({name:"",group_type:"",description:"",auto_virtual:!0}),L=_(),n=I({id:null,name:"",group_type:"",description:""}),S={name:[{required:!0,message:"请输入分组名称",trigger:"blur"}],group_type:[{required:!0,message:"请选择分组类型",trigger:"change"}]},k=async()=>{C.value=!0;try{const o=await fetch("/api/v1/device-groups/");if(o.ok){const e=await o.json();v.value=e||[],f.success(`成功加载 ${e.length} 个设备分组`)}else throw new Error(`HTTP ${o.status}: ${o.statusText}`)}catch(o){f.error("获取设备分组列表失败"),console.error("获取设备分组列表失败:",o),v.value=[]}finally{C.value=!1}},q=o=>{switch(o){case"server":return"success";case"mixed":return"warning";case"single":return"info";default:return""}},J=o=>{switch(o){case"server":return"服务器分组";case"mixed":return"混合分组";case"single":return"单设备分组";default:return"未知"}},K=o=>new Date(o).toLocaleString(),A=o=>{H.push(`/device-groups/${o.id}`)},Q=o=>{n.id=o.id,n.name=o.name,n.group_type=o.group_type,n.description=o.description,x.value=!0},W=async o=>{try{await De.confirm(`确定要删除设备分组 "${o.name}" 吗？此操作将同时删除分组中的所有虚拟设备占位符。`,"确认删除",{type:"warning",confirmButtonText:"确定删除",cancelButtonText:"取消",confirmButtonClass:"el-button--danger"});const e=await fetch(`/api/v1/device-groups/${o.id}`,{method:"DELETE"});if(e.ok)f.success(`设备分组 "${o.name}" 删除成功`),k();else{const i=await e.json().catch(()=>({}));throw new Error(i.detail||`HTTP ${e.status}`)}}catch(e){e!=="cancel"&&(f.error(`删除失败: ${e.message}`),console.error("删除设备分组失败:",e))}},X=async()=>{if(!(!F.value||!await F.value.validate().catch(()=>!1))){U.value=!0;try{const e=await fetch("/api/v1/device-groups/",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:r.name,group_type:r.group_type,description:r.description,auto_virtual:r.auto_virtual})});if(e.ok){const i=await e.json();f.success(`设备分组 "${r.name}" 创建成功`),V.value=!1,k(),Object.assign(r,{name:"",group_type:"",description:"",auto_virtual:!0})}else{const i=await e.json().catch(()=>({}));throw new Error(i.detail||`HTTP ${e.status}`)}}catch(e){f.error(`创建失败: ${e.message}`),console.error("创建设备分组失败:",e)}finally{U.value=!1}}},Y=async()=>{if(!(!L.value||!await L.value.validate().catch(()=>!1))){B.value=!0;try{await Ue(n.id,n),f.success("更新成功"),x.value=!1,k()}catch(e){f.error("更新失败"),console.error("更新设备分组失败:",e)}finally{B.value=!1}}};return re(()=>{k()}),(o,e)=>{const i=ce,d=ne,p=ue,$=fe,Z=ie,m=we,j=Ve,ee=be,h=Ce,g=Ee,w=$e,G=Te,te=he,P=ke,z=de,le=xe;return y(),M("div",Be,[e[28]||(e[28]=s("div",{class:"page-header"},[s("h2",null,"设备分组管理"),s("p",null,"管理设备分组和用户权限分配")],-1)),s("div",Fe,[t(d,{type:"primary",onClick:k,loading:C.value},{default:l(()=>[t(i,null,{default:l(()=>[t(c(me))]),_:1}),e[12]||(e[12]=u(" 刷新列表 ",-1))]),_:1,__:[12]},8,["loading"]),c(D).hasPermission("device.group")?(y(),E(d,{key:0,type:"success",onClick:e[0]||(e[0]=a=>V.value=!0)},{default:l(()=>[t(i,null,{default:l(()=>[t(c(_e))]),_:1}),e[13]||(e[13]=u(" 创建分组 ",-1))]),_:1,__:[13]})):R("",!0)]),s("div",Le,[t(Z,{gutter:20},{default:l(()=>[t($,{span:6},{default:l(()=>[t(p,{class:"stat-card"},{default:l(()=>[s("div",Re,[s("div",Se,b(T.value.total),1),e[14]||(e[14]=s("div",{class:"stat-label"},"总分组数",-1))]),t(i,{class:"stat-icon"},{default:l(()=>[t(c(ve))]),_:1})]),_:1})]),_:1}),t($,{span:6},{default:l(()=>[t(p,{class:"stat-card server"},{default:l(()=>[s("div",je,[s("div",Ge,b(T.value.server),1),e[15]||(e[15]=s("div",{class:"stat-label"},"服务器分组",-1))]),t(i,{class:"stat-icon"},{default:l(()=>[t(c(O))]),_:1})]),_:1})]),_:1}),t($,{span:6},{default:l(()=>[t(p,{class:"stat-card mixed"},{default:l(()=>[s("div",Pe,[s("div",ze,b(T.value.mixed),1),e[16]||(e[16]=s("div",{class:"stat-label"},"混合分组",-1))]),t(i,{class:"stat-icon"},{default:l(()=>[t(c(ge))]),_:1})]),_:1})]),_:1}),t($,{span:6},{default:l(()=>[t(p,{class:"stat-card single"},{default:l(()=>[s("div",Ne,[s("div",Ie,b(T.value.single),1),e[17]||(e[17]=s("div",{class:"stat-label"},"单设备分组",-1))]),t(i,{class:"stat-icon"},{default:l(()=>[t(c(O))]),_:1})]),_:1})]),_:1})]),_:1})]),t(p,{class:"list-card"},{default:l(()=>[ye((y(),E(ee,{data:v.value,stripe:"",style:{width:"100%"}},{default:l(()=>[t(m,{prop:"id",label:"ID",width:"80"}),t(m,{prop:"name",label:"分组名称",width:"150"}),t(m,{label:"分组类型",width:"120"},{default:l(({row:a})=>[t(j,{type:q(a.group_type),size:"small"},{default:l(()=>[u(b(J(a.group_type)),1)]),_:2},1032,["type"])]),_:1}),t(m,{label:"设备数量",width:"100"},{default:l(({row:a})=>[s("span",null,b(a.device_count||0),1)]),_:1}),t(m,{label:"虚拟设备",width:"100"},{default:l(({row:a})=>[a.has_virtual_devices?(y(),E(j,{key:0,type:"warning",size:"small"},{default:l(()=>e[18]||(e[18]=[u(" 有占位 ",-1)])),_:1,__:[18]})):(y(),M("span",Me,"-"))]),_:1}),t(m,{prop:"description",label:"描述","min-width":"150"}),t(m,{label:"创建时间",width:"160"},{default:l(({row:a})=>[u(b(K(a.created_at)),1)]),_:1}),t(m,{label:"操作",width:"200",fixed:"right"},{default:l(({row:a})=>[t(d,{type:"primary",size:"small",onClick:N=>A(a)},{default:l(()=>e[19]||(e[19]=[u(" 详情 ",-1)])),_:2,__:[19]},1032,["onClick"]),c(D).hasPermission("device.group")?(y(),E(d,{key:0,type:"warning",size:"small",onClick:N=>Q(a)},{default:l(()=>e[20]||(e[20]=[u(" 编辑 ",-1)])),_:2,__:[20]},1032,["onClick"])):R("",!0),c(D).hasPermission("device.group")?(y(),E(d,{key:1,type:"danger",size:"small",onClick:N=>W(a)},{default:l(()=>e[21]||(e[21]=[u(" 删除 ",-1)])),_:2,__:[21]},1032,["onClick"])):R("",!0)]),_:1})]),_:1},8,["data"])),[[le,C.value]])]),_:1}),t(z,{modelValue:V.value,"onUpdate:modelValue":e[6]||(e[6]=a=>V.value=a),title:"创建设备分组",width:"600px","close-on-click-modal":!1},{footer:l(()=>[t(d,{onClick:e[5]||(e[5]=a=>V.value=!1)},{default:l(()=>e[23]||(e[23]=[u("取消",-1)])),_:1,__:[23]}),t(d,{type:"primary",onClick:X,loading:U.value},{default:l(()=>e[24]||(e[24]=[u(" 确定 ",-1)])),_:1,__:[24]},8,["loading"])]),default:l(()=>[t(P,{ref_key:"createFormRef",ref:F,model:r,rules:S,"label-width":"120px"},{default:l(()=>[t(g,{label:"分组名称",prop:"name"},{default:l(()=>[t(h,{modelValue:r.name,"onUpdate:modelValue":e[1]||(e[1]=a=>r.name=a),placeholder:"请输入分组名称"},null,8,["modelValue"])]),_:1}),t(g,{label:"分组类型",prop:"group_type"},{default:l(()=>[t(G,{modelValue:r.group_type,"onUpdate:modelValue":e[2]||(e[2]=a=>r.group_type=a),placeholder:"请选择分组类型"},{default:l(()=>[t(w,{label:"按服务器分组",value:"server"}),t(w,{label:"跨服务器混合分组",value:"mixed"}),t(w,{label:"单设备分组",value:"single"})]),_:1},8,["modelValue"])]),_:1}),t(g,{label:"描述",prop:"description"},{default:l(()=>[t(h,{modelValue:r.description,"onUpdate:modelValue":e[3]||(e[3]=a=>r.description=a),type:"textarea",placeholder:"请输入分组描述",rows:3},null,8,["modelValue"])]),_:1}),t(g,{label:"自动添加虚拟设备",prop:"auto_virtual"},{default:l(()=>[t(te,{modelValue:r.auto_virtual,"onUpdate:modelValue":e[4]||(e[4]=a=>r.auto_virtual=a),"active-text":"是","inactive-text":"否"},null,8,["modelValue"]),e[22]||(e[22]=s("div",{class:"form-tip"}," 开启后，创建分组时会自动添加虚拟设备占位，添加真实设备后自动删除 ",-1))]),_:1,__:[22]})]),_:1},8,["model"])]),_:1},8,["modelValue"]),t(z,{modelValue:x.value,"onUpdate:modelValue":e[11]||(e[11]=a=>x.value=a),title:"编辑设备分组",width:"600px","close-on-click-modal":!1},{footer:l(()=>[t(d,{onClick:e[10]||(e[10]=a=>x.value=!1)},{default:l(()=>e[26]||(e[26]=[u("取消",-1)])),_:1,__:[26]}),t(d,{type:"primary",onClick:Y,loading:B.value},{default:l(()=>e[27]||(e[27]=[u(" 确定 ",-1)])),_:1,__:[27]},8,["loading"])]),default:l(()=>[t(P,{ref_key:"editFormRef",ref:L,model:n,rules:S,"label-width":"120px"},{default:l(()=>[t(g,{label:"分组名称",prop:"name"},{default:l(()=>[t(h,{modelValue:n.name,"onUpdate:modelValue":e[7]||(e[7]=a=>n.name=a),placeholder:"请输入分组名称"},null,8,["modelValue"])]),_:1}),t(g,{label:"分组类型",prop:"group_type"},{default:l(()=>[t(G,{modelValue:n.group_type,"onUpdate:modelValue":e[8]||(e[8]=a=>n.group_type=a),placeholder:"请选择分组类型",disabled:""},{default:l(()=>[t(w,{label:"按服务器分组",value:"server"}),t(w,{label:"跨服务器混合分组",value:"mixed"}),t(w,{label:"单设备分组",value:"single"})]),_:1},8,["modelValue"]),e[25]||(e[25]=s("div",{class:"form-tip"},"分组类型创建后不可修改",-1))]),_:1,__:[25]}),t(g,{label:"描述",prop:"description"},{default:l(()=>[t(h,{modelValue:n.description,"onUpdate:modelValue":e[9]||(e[9]=a=>n.description=a),type:"textarea",placeholder:"请输入分组描述",rows:3},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},ut=ae(Oe,[["__scopeId","data-v-e62510d7"]]);export{ut as default};
