import{_ as O}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                   *//* empty css                  *//* empty css                     *//* empty css                 */import"./el-tooltip-l0sNRNKZ.js";/* empty css                     *//* empty css                         *//* empty css                  */import{u as $,r as b,a as j,c as W,e,w as s,J as H,K as J,x as K,b as Q,h as X,i as r,d as n,E as Y,p as a,L as k,M as Z,N as G,n as f,t as V,O as ee,P as se,y as u,z as i,Q as oe,R as ae,S as le,T as te,U as ne,V as re,W as de,X as ue,Y as ie,Z as _e,_ as h,$ as me,a0 as pe,a1 as fe,a2 as we,f as ce,j as ge,k as ve,m as Pe,a3 as ye,s as be,a4 as ke}from"./index-CwbwN4Sv.js";const xe={class:"layout-container"},Ee={class:"header-content"},Ve={class:"header-left"},he={class:"logo"},Ce={class:"header-right"},Ne={class:"user-info"},Me={class:"user-name"},Ue={__name:"index",setup(Be){const x=Q(),l=$(),w=b(!1),c=b(!1),g=b(),d=j({oldPassword:"",newPassword:"",confirmPassword:""}),C={oldPassword:[{required:!0,message:"请输入原密码",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认新密码",trigger:"blur"},{validator:(_,o,t)=>{o!==d.newPassword?t(new Error("两次输入的密码不一致")):t()},trigger:"blur"}]},N=_=>{switch(_){case"profile":x.push("/profile");break;case"changePassword":w.value=!0;break;case"logout":U();break}},M=async()=>{try{await g.value.validate(),c.value=!0,await ye({old_password:d.oldPassword,new_password:d.newPassword}),be.success("密码修改成功"),w.value=!1,g.value.resetFields(),Object.assign(d,{oldPassword:"",newPassword:"",confirmPassword:""})}catch(_){console.error("修改密码失败:",_)}finally{c.value=!1}},U=async()=>{try{await ke.confirm("确定要退出登录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await l.userLogout(),x.push("/login")}catch(_){_!=="cancel"&&console.error("登出失败:",_)}};return(_,o)=>{const t=Y,B=G,v=oe,L=se,A=Z,F=H,m=de,I=re,R=ne,S=X("router-view"),D=fe,T=J,P=ve,y=ge,q=ce,E=Pe,z=K;return r(),W("div",xe,[e(F,{class:"layout-header",height:"60px"},{default:s(()=>[n("div",Ee,[n("div",Ve,[n("div",he,[e(t,{class:"logo-icon"},{default:s(()=>[e(a(k))]),_:1}),o[5]||(o[5]=n("span",{class:"logo-text"},"OmniLink 全联通系统",-1))])]),n("div",Ce,[e(A,{onCommand:N},{dropdown:s(()=>[e(L,null,{default:s(()=>[e(v,{command:"profile"},{default:s(()=>[e(t,null,{default:s(()=>[e(a(ae))]),_:1}),o[6]||(o[6]=f(" 个人资料 ",-1))]),_:1,__:[6]}),a(l).isNewUser?i("",!0):(r(),u(v,{key:0,command:"changePassword"},{default:s(()=>[e(t,null,{default:s(()=>[e(a(le))]),_:1}),o[7]||(o[7]=f(" 修改密码 ",-1))]),_:1,__:[7]})),e(v,{divided:"",command:"logout"},{default:s(()=>[e(t,null,{default:s(()=>[e(a(te))]),_:1}),o[8]||(o[8]=f(" 退出登录 ",-1))]),_:1,__:[8]})]),_:1})]),default:s(()=>[n("div",Ne,[e(B,{size:32,class:"user-avatar"},{default:s(()=>[f(V(a(l).userName.charAt(0)),1)]),_:1}),n("span",Me,V(a(l).userName),1),e(t,{class:"dropdown-icon"},{default:s(()=>[e(a(ee))]),_:1})])]),_:1})])])]),_:1}),e(T,{class:"layout-main"},{default:s(()=>[a(l).isNewUser?i("",!0):(r(),u(R,{key:0,class:"layout-sidebar",width:"200px"},{default:s(()=>[e(I,{"default-active":_.$route.path,class:"sidebar-menu",router:"","unique-opened":""},{default:s(()=>[a(l).canAccessWorkspace?(r(),u(m,{key:0,index:"/dashboard"},{default:s(()=>[e(t,null,{default:s(()=>[e(a(ue))]),_:1}),o[9]||(o[9]=n("span",null,"工作台",-1))]),_:1,__:[9]})):i("",!0),a(l).hasPermission("application.view")?(r(),u(m,{key:1,index:"/applications"},{default:s(()=>[e(t,null,{default:s(()=>[e(a(ie))]),_:1}),o[10]||(o[10]=n("span",null,"处理事项",-1))]),_:1,__:[10]})):i("",!0),a(l).hasPermission("user.view")||a(l).hasPermission("org.view")?(r(),u(m,{key:2,index:"/org-users"},{default:s(()=>[e(t,null,{default:s(()=>[e(a(_e))]),_:1}),o[11]||(o[11]=n("span",null,"组织与用户管理",-1))]),_:1,__:[11]})):i("",!0),a(l).hasPermission("user.approve")?(r(),u(m,{key:3,index:"/user-registration"},{default:s(()=>[e(t,null,{default:s(()=>[e(a(h))]),_:1}),o[12]||(o[12]=n("span",null,"新用户审核",-1))]),_:1,__:[12]})):i("",!0),a(l).hasPermission("device.view")||a(l).hasPermission("slave.view")||a(l).hasPermission("device.group")?(r(),u(m,{key:4,index:"/device-center"},{default:s(()=>[e(t,null,{default:s(()=>[e(a(k))]),_:1}),o[13]||(o[13]=n("span",null,"设备管理中心",-1))]),_:1,__:[13]})):i("",!0),a(l).canAccessLoginManagementTab&&a(l).getPermissionLevel<=2?(r(),u(m,{key:5,index:"/client-management"},{default:s(()=>[e(t,null,{default:s(()=>[e(a(k))]),_:1}),o[14]||(o[14]=n("span",null,"设备绑定中心",-1))]),_:1,__:[14]})):i("",!0),a(l).canAccessRoleManagement?(r(),u(m,{key:6,index:"/role-management"},{default:s(()=>[e(t,null,{default:s(()=>[e(a(h))]),_:1}),o[15]||(o[15]=n("span",null,"角色管理",-1))]),_:1,__:[15]})):i("",!0),a(l).canAccessSystemSettings&&a(l).getPermissionLevel<=1?(r(),u(m,{key:7,index:"/system-settings"},{default:s(()=>[e(t,null,{default:s(()=>[e(a(me))]),_:1}),o[16]||(o[16]=n("span",null,"系统设置",-1))]),_:1,__:[16]})):i("",!0),a(l).hasPermission("dashboard.view")?(r(),u(m,{key:8,index:"/data-dashboard"},{default:s(()=>[e(t,null,{default:s(()=>[e(a(pe))]),_:1}),o[17]||(o[17]=n("span",null,"数据大屏",-1))]),_:1,__:[17]})):i("",!0)]),_:1},8,["default-active"])]),_:1})),e(D,{class:we(["layout-content",{"new-user-content":a(l).isNewUser}])},{default:s(()=>[e(S)]),_:1},8,["class"])]),_:1}),e(z,{modelValue:w.value,"onUpdate:modelValue":o[4]||(o[4]=p=>w.value=p),title:"修改密码",width:"400px","close-on-click-modal":!1},{footer:s(()=>[e(E,{onClick:o[3]||(o[3]=p=>w.value=!1)},{default:s(()=>o[18]||(o[18]=[f("取消",-1)])),_:1,__:[18]}),e(E,{type:"primary",onClick:M,loading:c.value},{default:s(()=>o[19]||(o[19]=[f(" 确定 ",-1)])),_:1,__:[19]},8,["loading"])]),default:s(()=>[e(q,{ref_key:"passwordFormRef",ref:g,model:d,rules:C,"label-width":"80px"},{default:s(()=>[e(y,{label:"原密码",prop:"oldPassword"},{default:s(()=>[e(P,{modelValue:d.oldPassword,"onUpdate:modelValue":o[0]||(o[0]=p=>d.oldPassword=p),type:"password",placeholder:"请输入原密码","show-password":""},null,8,["modelValue"])]),_:1}),e(y,{label:"新密码",prop:"newPassword"},{default:s(()=>[e(P,{modelValue:d.newPassword,"onUpdate:modelValue":o[1]||(o[1]=p=>d.newPassword=p),type:"password",placeholder:"请输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),e(y,{label:"确认密码",prop:"confirmPassword"},{default:s(()=>[e(P,{modelValue:d.confirmPassword,"onUpdate:modelValue":o[2]||(o[2]=p=>d.confirmPassword=p),type:"password",placeholder:"请确认新密码","show-password":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},Oe=O(Ue,[["__scopeId","data-v-2854fb7b"]]);export{Oe as default};
