import{an as e}from"./index-ClQFnb0n.js";function u(t=null){return e({url:"/api/v1/system/settings",method:"get",params:t?{category:t}:{}})}function r(t,n){return e({url:`/api/v1/system/settings/${t}`,method:"put",data:n})}function o(){return e({url:"/api/v1/system/tunnels",method:"get"})}function a(t){return e({url:"/api/v1/system/tunnels",method:"post",data:t})}function l(t,n){return e({url:`/api/v1/system/tunnels/${t}`,method:"put",data:n})}function i(t){return e({url:`/api/v1/system/tunnels/${t}`,method:"delete"})}function m(t){return e({url:`/api/v1/system/tunnels/${t}/start`,method:"post"})}function p(t){return e({url:`/api/v1/system/tunnels/${t}/stop`,method:"post"})}function d(){return e({url:"/api/v1/system/client-download-url",method:"get"})}export{u as a,o as b,p as c,i as d,a as e,r as f,d as g,m as s,l as u};
