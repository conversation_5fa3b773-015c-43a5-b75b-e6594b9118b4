import{b as H}from"./users-CjWk2F0r.js";import{u as Q}from"./useOrganizationTree-aYaBbMvP.js";import{r as o,aN as $,o as X,s as b,bz as z}from"./index-msvS5Uas.js";function re(I={}){const{autoLoad:E=!0,enableCache:K=!0}=I,{loading:M,searchText:d,expandedKeys:h,defaultExpandedKeys:P,canManageOrganization:V,getLevelName:W,collectExpandKeys:v,filterNodes:N,findNode:k,getNodePath:J}=Q({autoLoad:!1,enableCache:K,maxExpandLevel:3}),A=o(!1),t=o([]),D=o([]),f=o([]),R=o([]),m=o(null),S=$(()=>{console.log("🔍 useOrgUsersAdapter - computedTreeData computed"),console.log("🔍 useOrgUsersAdapter - organizationTreeData.value:",t.value);const r=p(t.value);return console.log("🔍 useOrgUsersAdapter - 处理后数据:",r),r}),j=$(()=>d.value?N(t.value,r=>{var e;return r.name.toLowerCase().includes(d.value.toLowerCase())||r.type==="user"&&((e=r.role_name)==null?void 0:e.toLowerCase().includes(d.value.toLowerCase()))}):t.value),O=r=>{const e=[],a=(u,c=null)=>{Array.isArray(u)&&u.forEach(l=>{e.push({...l,parentId:c}),l.children&&l.children.length>0&&a(l.children,l.id)})};let s=r;return r&&r.success&&r.data&&(s=r.data),a(Array.isArray(s)?s:[s]),e},U=r=>{if(!r||!Array.isArray(r))return[];const e=a=>{const s={id:a.id,name:a.name,type:a.type||"organization",level:a.level||0,children:[]};return a.type==="user"&&(s.role_name=a.role_name,s.username=a.username,s.email=a.email,s.permission_level=a.permission_level),a.type==="organization"&&(s.description=a.description,s.user_count=a.user_count||0,s.child_count=a.child_count||0,s.users=a.users||[]),a.children&&Array.isArray(a.children)&&(s.children=a.children.map(u=>e(u))),s};return r.map(a=>e(a))},_=async()=>{A.value=!0;try{const r=await H();console.log("🔍 原始API响应类型:",typeof r,"是否数组:",Array.isArray(r));let e=r;r&&r.success&&r.data&&(console.log("🔍 检测到API中间件包装格式，提取data字段:",r.data),e=r.data),f.value=e,D.value=O(e),w(),b.success("组织架构数据加载成功")}catch(r){console.error("加载组织架构失败:",r),b.error("加载组织架构失败"),t.value=[]}finally{A.value=!1}},p=r=>Array.isArray(r)?r.map(e=>{const a={...e};if(e.id&&typeof e.id=="number"){console.log(`🔍 处理组织: ${e.name}, ID: ${e.id}, user_count: ${e.user_count}, users数组长度: ${e.users?e.users.length:"N/A"}`),console.log(`🔍 ${e.name} 完整数据结构:`,JSON.stringify(e,null,2));const c=(n=>{const C=x=>{if(!Array.isArray(x))return[];for(const i of x){if(i.id===n&&i.users&&Array.isArray(i.users))return console.log(`🔍 找到组织 ${i.name} 的用户数据:`,i.users.length,"人"),i.users;if(i.children&&Array.isArray(i.children)){const L=C(i.children);if(L.length>0)return L}}return[]};return C(f.value||[])})(e.id)||e.users||[];console.log(`🔍 ${e.name} 最终用户数据:`,c.length,"人");const l=c.filter(n=>n.role_name&&n.role_name.includes("管理员")),y=c.filter(n=>!n.role_name||!n.role_name.includes("管理员"));console.log(`🔍 ${e.name} - 管理员: ${l.length}人, 普通用户: ${y.length}人`);const g=[];g.push({id:`${e.id}_admin_group`,name:"管理员",type:"admin_group",userCount:l.length,users:l,organization_id:e.id,children:l.map(n=>({...n,id:`${e.id}_admin_${n.id}`,type:"user"}))}),g.push({id:`${e.id}_user_group`,name:"普通用户",type:"normal_user_group",userCount:y.length,users:y,organization_id:e.id,children:y.map(n=>({...n,id:`${e.id}_user_${n.id}`,type:"user"}))}),console.log(`🔍 ${e.name} - 添加了 ${g.length} 个分类节点`);const T=e.children?p(e.children):[];a.children=[...T,...g],console.log(`🔍 ${e.name} - 最终子节点数量: ${a.children.length} (组织: ${T.length}, 分类: ${g.length})`)}else console.log(`🔍 跳过组织: ${e.name}, ID: ${e.id}, 原因: 无用户数据`),e.children&&(a.children=p(e.children));return a}):[],w=()=>{try{const r=f.value;if(!r){t.value=[];return}let e=Array.isArray(r)?r:[r];if(e.length===0){t.value=[];return}let a=U(e);z(()=>{t.value=a,t.value.length>0&&!m.value&&(m.value=t.value[0]),h.value=v(t.value,3)})}catch(r){console.error("构建树结构失败:",r),t.value=[]}},q=async()=>{await _()},B=r=>{m.value=r},F=async()=>{await z(),h.value=v(t.value)},G=async()=>{await z(),h.value=[]};return X(()=>{E&&_()}),{loading:$(()=>A.value||M.value),organizationTreeData:t,flatOrganizations:D,organizationsResponse:f,allUsers:R,selectedNode:m,searchText:d,expandedKeys:h,defaultExpandedKeys:P,computedTreeData:S,filteredTreeData:j,canManageOrganization:V,loadOrganizationsWithUsers:_,buildTreeData:w,refreshData:q,selectNode:B,expandAll:F,collapseAll:G,getLevelName:W,collectExpandKeys:v,filterNodes:N,findNode:k,getNodePath:J,flattenOrganizations:O,buildTree:U,addUserCategoryNodes:p}}export{re as u};
