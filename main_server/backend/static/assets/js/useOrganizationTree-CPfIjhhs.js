import{a as V}from"./permission-assignment-Bhi__0DG.js";import{u as q,r as u,a9 as h,o as Q,bN as W,s as S,bA as X}from"./index-BALd70Fs.js";const D="organization_tree_cache",T="organization_tree_timestamp",Z=5*60*1e3;let m=u([]),O=u(!1),P=u(null),N=u(null),I=new Set;class A{static setCache(t){try{localStorage.setItem(D,JSON.stringify(t)),localStorage.setItem(T,Date.now().toString())}catch(e){console.warn("缓存存储失败:",e)}}static getCache(){try{const t=localStorage.getItem(T);if(!t||Date.now()-parseInt(t)>Z)return null;const e=localStorage.getItem(D);return e?JSON.parse(e):null}catch(t){return console.warn("缓存读取失败:",t),null}}static clearCache(){try{localStorage.removeItem(D),localStorage.removeItem(T)}catch(t){console.warn("缓存清除失败:",t)}}}class L{static async fetchData(t=!1){if(O.value)return m.value;if(!t){const e=A.getCache();if(e)return m.value=e,N.value=new Date,this.notifySubscribers(),e}try{O.value=!0,P.value=null,console.log("🔍 useOrganizationTree - 开始获取组织架构数据");const e=await V();console.log("🔍 useOrganizationTree - API响应:",e);let a=e;e&&e.success&&e.data?(console.log("🔍 useOrganizationTree - 检测到API中间件包装格式，提取data字段"),a=e.data):e&&e.data&&(console.log("🔍 useOrganizationTree - 使用response.data字段"),a=e.data);const r=Array.isArray(a)?a:[];return console.log("🔍 useOrganizationTree - 处理后的数据:",r),console.log("🔍 useOrganizationTree - 数据长度:",r.length),m.value=r,N.value=new Date,A.setCache(r),this.notifySubscribers(),r}catch(e){throw P.value=e,console.error("获取组织架构失败:",e),S.error("获取组织架构失败"),e}finally{O.value=!1}}static notifySubscribers(){I.forEach(t=>{try{t(m.value)}catch(e){console.error("通知订阅者失败:",e)}}),this.triggerCrossModuleSync()}static subscribe(t){return I.add(t),()=>I.delete(t)}static triggerCrossModuleSync(){typeof window<"u"&&window.dispatchEvent(new CustomEvent("organizationDataUpdated",{detail:{data:m.value,timestamp:N.value,source:"OrganizationDataManager"}}))}static async refreshAllModules(){try{await this.fetchData(!0),S.success("所有模块数据已同步更新")}catch(t){console.error("刷新所有模块数据失败:",t),S.error("数据同步失败")}}}class g{static getLevelName(t){return["集团总部","大区","分公司","部门","小组"][t]||"未知"}static filterNewUserOrganizations(t,e=!0){return!e||!t?t:t.filter(a=>a.name==="新注册用户"?(console.log('🔧 OrganizationUtils - 过滤掉"新注册用户"组织:',a.name),!1):!0).map(a=>{const r={...a};return a.children&&a.children.length>0&&(r.children=this.filterNewUserOrganizations(a.children,e)),r})}static collectExpandKeys(t,e=3){const a=[],r=(o,s=0)=>{s>=e||o.forEach(i=>{i.children&&i.children.length>0&&(a.push(i.id),r(i.children,s+1))})};return r(t),a}static findNodeById(t,e){for(const a of t){if(a.id===e)return a;if(a.children){const r=this.findNodeById(a.children,e);if(r)return r}}return null}static findNodePath(t,e){const a=[],r=(o,s,i=[])=>{for(const c of o){const f=[...i,c];if(c.id===s)return a.push(...f),!0;if(c.children&&r(c.children,s,f))return!0}return!1};return r(t,e),a}static filterNodes(t,e){const a=[];for(const r of t)if(e(r)){const o={...r};r.children&&(o.children=this.filterNodes(r.children,e)),a.push(o)}else if(r.children){const o=this.filterNodes(r.children,e);o.length>0&&a.push({...r,children:o})}return a}}function ae(w={}){const{autoLoad:t=!0,enableCache:e=!0,maxExpandLevel:a=3,enableVirtualScroll:r=!1}=w,o=q(),s=u(!1),i=u(null),c=u([]),f=u(""),_=u(!1),p=h(()=>{let n=m.value;return n=g.filterNewUserOrganizations(n,!0),n=F(n),console.log("🔍 useOrganizationTree - organizationTree computed:",n),console.log("🔍 useOrganizationTree - 数据长度:",n.length),n}),M=h(()=>O.value||s.value),U=h(()=>P.value),K=h(()=>N.value),F=n=>{if(!n||n.length===0)return console.log("🔍 applyPermissionFilter - 数据为空，返回空数组"),[];console.log("🔍 applyPermissionFilter - 输入数据:",n),console.log("🔍 applyPermissionFilter - 用户权限等级:",o.getPermissionLevel),console.log("🔍 applyPermissionFilter - 用户组织ID:",o.organizationId);const v=d=>{const E={...d,type:"organization",children:[]};if(d.children&&d.children.length>0&&E.children.push(...d.children.map(v)),d.users&&d.users.length>0){const y={};d.users.forEach(l=>{const z=l.role_name||"普通用户";y[z]||(y[z]=[]),y[z].push({id:`user_${l.id}`,name:l.full_name||l.username,type:"user",role_name:l.role_name,permission_level:l.permission_level,organization_id:l.organization_id,username:l.username,full_name:l.full_name,children:[]})}),Object.keys(y).forEach(l=>{const z={id:`role_${d.id}_${l}`,name:l,type:l.includes("管理员")?"admin_group":"normal_user_group",children:y[l]};E.children.push(z)})}return E},x=n.map(v);return console.log("🔍 applyPermissionFilter - 输出数据:",x),x},B=h(()=>f.value?g.filterNodes(p.value,n=>{var v;return n.name.toLowerCase().includes(f.value.toLowerCase())||n.type==="user"&&((v=n.role_name)==null?void 0:v.toLowerCase().includes(f.value.toLowerCase()))}):p.value),b=h(()=>g.collectExpandKeys(p.value,a)),H=h(()=>o.hasPermission("organization.manage")||o.canAccessDeviceManagement),C=async(n=!1)=>{try{s.value=!0,await L.fetchData(n)}finally{s.value=!1}},$=()=>C(!0),k=()=>{A.clearCache(),m.value=[],N.value=null},J=n=>{i.value=n},R=async()=>{_.value=!_.value,await X(),c.value=_.value?b.value:[]},Y=n=>g.findNodeById(p.value,n),j=n=>g.findNodePath(p.value,n),G=L.subscribe(n=>{c.value.length===0&&(c.value=b.value)});return Q(()=>{t&&C()}),W(()=>{G()}),{organizationTree:p,filteredTree:B,loading:M,error:U,lastUpdate:K,selectedNode:i,expandedKeys:c,defaultExpandedKeys:b,searchText:f,allExpanded:_,canManageOrganization:H,loadData:C,refreshData:$,clearCache:k,selectNode:J,expandAll:R,findNode:Y,getNodePath:j,getLevelName:g.getLevelName,collectExpandKeys:g.collectExpandKeys,filterNodes:g.filterNodes}}export{ae as u};
