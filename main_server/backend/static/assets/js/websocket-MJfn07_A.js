import{s}from"./index-BALd70Fs.js";class o{constructor(){this.ws=null,this.url="",this.token="",this.isConnected=!1,this.isConnecting=!1,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectInterval=3e3,this.heartbeatInterval=3e4,this.heartbeatTimer=null,this.reconnectTimer=null,this.eventListeners=new Map,this.subscriptions=new Set,this.messageQueue=[],this.onConnectionChange=null}connect(e){if(!(this.isConnecting||this.isConnected)){this.token=e,this.url=this.getWebSocketUrl(),this.isConnecting=!0;try{this.ws=new WebSocket(this.url),this.setupEventHandlers()}catch(t){console.error("WebSocket连接失败:",t),this.handleConnectionError()}}}disconnect(){this.isConnecting=!1,this.clearTimers(),this.ws&&(this.ws.close(),this.ws=null),this.isConnected=!1,this.reconnectAttempts=0,this.notifyConnectionChange(!1)}send(e){if(this.isConnected&&this.ws)try{return this.ws.send(JSON.stringify(e)),!0}catch(t){return console.error("发送消息失败:",t),!1}else return this.messageQueue.push(e),!1}subscribe(e){this.subscriptions.add(e),this.isConnected&&this.send({type:"subscribe",subscription_type:e})}unsubscribe(e){this.subscriptions.delete(e),this.isConnected&&this.send({type:"unsubscribe",subscription_type:e})}addEventListener(e,t){this.eventListeners.has(e)||this.eventListeners.set(e,new Set),this.eventListeners.get(e).add(t)}removeEventListener(e,t){this.eventListeners.has(e)&&this.eventListeners.get(e).delete(t)}setConnectionChangeCallback(e){this.onConnectionChange=e}getWebSocketUrl(){const e=window.location.protocol==="https:"?"wss:":"ws:",t=window.location.host;return`${e}//${t}/ws/${this.token}`}setupEventHandlers(){this.ws.onopen=()=>{console.log("WebSocket连接已建立"),this.isConnected=!0,this.isConnecting=!1,this.reconnectAttempts=0,this.subscriptions.forEach(e=>{this.send({type:"subscribe",subscription_type:e})}),this.flushMessageQueue(),this.startHeartbeat(),this.notifyConnectionChange(!0),s.success("实时连接已建立")},this.ws.onmessage=e=>{try{const t=JSON.parse(e.data);this.handleMessage(t)}catch(t){console.error("解析WebSocket消息失败:",t)}},this.ws.onclose=e=>{console.log("WebSocket连接已关闭:",e.code,e.reason),this.isConnected=!1,this.isConnecting=!1,this.clearTimers(),this.notifyConnectionChange(!1),e.code!==1e3&&this.attemptReconnect()},this.ws.onerror=e=>{console.error("WebSocket错误:",e),this.handleConnectionError()}}handleMessage(e){const{type:t}=e;switch(this.eventListeners.has(t)&&this.eventListeners.get(t).forEach(n=>{try{n(e)}catch(i){console.error("事件监听器执行失败:",i)}}),t){case"pong":break;case"connection_established":console.log("WebSocket连接确认:",e.connection_id);break;case"subscription_confirmed":console.log("订阅确认:",e.subscription_type);break;case"error":console.error("服务器错误:",e.message),s.error(`服务器错误: ${e.message}`);break}}startHeartbeat(){this.clearHeartbeat(),this.heartbeatTimer=setInterval(()=>{this.isConnected&&this.send({type:"ping"})},this.heartbeatInterval)}clearHeartbeat(){this.heartbeatTimer&&(clearInterval(this.heartbeatTimer),this.heartbeatTimer=null)}attemptReconnect(){if(this.reconnectAttempts>=this.maxReconnectAttempts){console.error("WebSocket重连次数已达上限"),s.error("连接失败，请刷新页面重试");return}this.reconnectAttempts++,console.log(`WebSocket重连尝试 ${this.reconnectAttempts}/${this.maxReconnectAttempts}`),this.reconnectTimer=setTimeout(()=>{!this.isConnected&&!this.isConnecting&&this.connect(this.token)},this.reconnectInterval*this.reconnectAttempts)}handleConnectionError(){this.isConnected=!1,this.isConnecting=!1,this.clearTimers(),this.notifyConnectionChange(!1)}clearTimers(){this.clearHeartbeat(),this.reconnectTimer&&(clearTimeout(this.reconnectTimer),this.reconnectTimer=null)}flushMessageQueue(){for(;this.messageQueue.length>0;){const e=this.messageQueue.shift();this.send(e)}}notifyConnectionChange(e){this.onConnectionChange&&this.onConnectionChange(e)}getConnectionStatus(){return{isConnected:this.isConnected,isConnecting:this.isConnecting,reconnectAttempts:this.reconnectAttempts,subscriptions:Array.from(this.subscriptions)}}}const h=new o;export{h as default};
