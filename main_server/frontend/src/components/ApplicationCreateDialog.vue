<template>
  <el-dialog
    v-model="dialogVisible"
    title="提交申请"
    width="600px"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="申请标题" prop="title">
        <el-input
          v-model="form.title"
          placeholder="请输入申请标题"
        />
      </el-form-item>
      
      <el-form-item label="申请类型" prop="application_type">
        <el-select
          v-model="form.application_type"
          placeholder="请选择申请类型"
          style="width: 100%"
        >
          <el-option
            v-for="type in applicationTypes"
            :key="type.code"
            :label="type.name"
            :value="type.code"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="申请内容" prop="content">
        <el-input
          v-model="form.content"
          type="textarea"
          :rows="6"
          placeholder="请详细描述您的申请内容"
        />
      </el-form-item>
      
      <el-form-item label="期望完成时间">
        <el-date-picker
          v-model="form.expected_completion_date"
          type="date"
          placeholder="选择期望完成时间"
          style="width: 100%"
        />
      </el-form-item>
      
      <el-form-item label="补充说明">
        <el-input
          v-model="form.additional_notes"
          type="textarea"
          :rows="3"
          placeholder="其他需要说明的信息（可选）"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit"
          :loading="submitting"
        >
          提交申请
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { applicationRequestsAPI } from '@/api/applicationRequests'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'refresh'])

// Reactive data
const formRef = ref()
const submitting = ref(false)
const applicationTypes = ref([])

const form = reactive({
  title: '',
  application_type: '',
  content: '',
  expected_completion_date: '',
  additional_notes: ''
})

const rules = {
  title: [
    { required: true, message: '请输入申请标题', trigger: 'blur' }
  ],
  application_type: [
    { required: true, message: '请选择申请类型', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入申请内容', trigger: 'blur' }
  ]
}

// Computed
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// Watch
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    resetForm()
  }
})

// Methods
const resetForm = () => {
  form.title = ''
  form.application_type = ''
  form.content = ''
  form.expected_completion_date = ''
  form.additional_notes = ''
  formRef.value?.resetFields()
}

const loadApplicationTypes = async () => {
  try {
    const response = await applicationRequestsAPI.getTypes()
    applicationTypes.value = response.data || response || []
  } catch (error) {
    console.error('加载申请类型失败:', error)
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    const applicationData = {
      title: form.title,
      type: form.application_type,
      priority: 'normal',
      content: form.content,
      expected_completion_date: form.expected_completion_date,
      additional_notes: form.additional_notes || null
    }
    
    await applicationRequestsAPI.createApplication(applicationData)
    
    ElMessage.success('申请提交成功，请等待管理员审核')
    emit('refresh')
    dialogVisible.value = false
    
  } catch (error) {
    console.error('提交申请失败:', error)
    ElMessage.error('提交申请失败')
  } finally {
    submitting.value = false
  }
}

// Lifecycle
onMounted(() => {
  loadApplicationTypes()
})
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
