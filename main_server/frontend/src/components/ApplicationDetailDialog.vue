<template>
  <el-dialog
    v-model="dialogVisible"
    title="申请详情"
    width="800px"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div v-loading="loading" class="application-detail">
      <el-card v-if="application" class="detail-card">
        <!-- 申请基本信息 -->
        <div class="info-section">
          <h3>基本信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="申请ID">
              {{ application.id }}
            </el-descriptions-item>
            <el-descriptions-item label="申请标题">
              {{ application.title }}
            </el-descriptions-item>
            <el-descriptions-item label="申请类型">
              <el-tag :type="getTypeTagType(application.type || application.application_type)">
                {{ getTypeText(application.type || application.application_type) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="getStatusTagType(application.status)">
                {{ getStatusText(application.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="优先级">
              <el-tag :type="getPriorityTagType(application.priority)">
                {{ getPriorityText(application.priority) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="申请人">
              {{ application.applicant_name || application.applicant?.full_name || application.applicant?.username }}
            </el-descriptions-item>
            <el-descriptions-item label="所属组织">
              {{ application.organization_name || application.applicant_organization || '未指定' }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ formatDateTime(application.created_at) }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        
        <!-- 申请内容 -->
        <div class="info-section">
          <h3>申请内容</h3>
          <div class="content-box">
            {{ application.content || application.description || '暂无内容' }}
          </div>
        </div>
        
        <!-- 处理信息 -->
        <div v-if="application.status !== 'pending'" class="info-section">
          <h3>处理信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="处理人">
              {{ application.processor_name || application.processor?.full_name || application.processor?.username || '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="处理时间">
              {{ formatDateTime(application.processed_at || application.updated_at) }}
            </el-descriptions-item>
            <el-descriptions-item label="处理结果" span="2">
              <el-tag :type="getStatusTagType(application.status)">
                {{ getStatusText(application.status) }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
          
          <div v-if="application.response_content || application.process_comment" class="response-section">
            <h4>处理意见</h4>
            <div class="content-box">
              {{ application.response_content || application.process_comment || '暂无处理意见' }}
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button 
          v-if="application && application.status === 'pending' && userStore.hasPermission('application.process')"
          type="primary" 
          @click="handleProcess"
        >
          处理申请
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useUserStore } from '@/stores/user'
import { applicationRequestsAPI } from '@/api/applicationRequests'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  application: {
    type: Object,
    default: () => null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'refresh', 'process'])

// Store
const userStore = useUserStore()

// Reactive data
const loading = ref(false)
const applicationDetail = ref(null)

// Computed
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// Watch for application changes
watch(() => props.application, async (newApplication) => {
  if (newApplication && newApplication.id) {
    // 如果传入的application已经包含完整数据，直接使用
    if (newApplication.content || newApplication.description) {
      applicationDetail.value = newApplication
    } else {
      // 否则从API获取完整数据
      await loadApplicationDetail(newApplication.id)
    }
  }
}, { immediate: true })

// Methods
const loadApplicationDetail = async (applicationId) => {
  if (!applicationId) return

  try {
    loading.value = true
    const response = await applicationRequestsAPI.getApplication(applicationId)
    applicationDetail.value = response.data || response
  } catch (error) {
    console.error('加载申请详情失败:', error)
    ElMessage.error('加载申请详情失败')
  } finally {
    loading.value = false
  }
}

const application = computed(() => {
  // 优先使用详细数据，如果没有则使用传入的props数据
  const detailData = applicationDetail.value
  const propsData = props.application

  if (detailData && Object.keys(detailData).length > 0) {
    return detailData
  }

  return propsData || {}
})

const handleProcess = () => {
  emit('process', application.value)
  dialogVisible.value = false
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

// 状态相关方法
const getStatusText = (status) => {
  const statusMap = {
    pending: '待处理',
    approved: '已批准',
    rejected: '已拒绝',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status) => {
  const typeMap = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger',
    cancelled: 'info'
  }
  return typeMap[status] || 'info'
}

const getTypeText = (type) => {
  const typeMap = {
    device_access: '设备访问申请',
    permission_change: '权限变更申请',
    organization_transfer: '组织转移申请',
    other: '其他申请'
  }
  return typeMap[type] || type
}

const getTypeTagType = (type) => {
  const typeMap = {
    device_access: 'primary',
    permission_change: 'warning',
    organization_transfer: 'success',
    other: 'info'
  }
  return typeMap[type] || 'info'
}

const getPriorityText = (priority) => {
  const priorityMap = {
    low: '低',
    normal: '普通',
    high: '高',
    urgent: '紧急'
  }
  return priorityMap[priority] || priority
}

const getPriorityTagType = (priority) => {
  const typeMap = {
    low: 'info',
    normal: 'primary',
    high: 'warning',
    urgent: 'danger'
  }
  return typeMap[priority] || 'primary'
}
</script>

<style scoped>
.application-detail {
  max-height: 600px;
  overflow-y: auto;
}

.detail-card {
  border: none;
  box-shadow: none;
}

.info-section {
  margin-bottom: 24px;
}

.info-section h3 {
  margin-bottom: 16px;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.info-section h4 {
  margin: 16px 0 8px 0;
  color: #606266;
  font-size: 14px;
  font-weight: 500;
}

.content-box {
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  min-height: 60px;
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.6;
}

.response-section {
  margin-top: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
