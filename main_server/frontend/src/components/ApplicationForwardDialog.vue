<template>
  <el-dialog
    v-model="dialogVisible"
    title="转交申请"
    width="500px"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
    >
      <el-form-item label="申请信息">
        <div class="application-info">
          <p><strong>标题：</strong>{{ application?.title }}</p>
          <p><strong>申请人：</strong>{{ application?.applicant_name }}</p>
          <p><strong>类型：</strong>{{ getTypeText(application?.application_type) }}</p>
        </div>
      </el-form-item>
      
      <el-form-item label="转交给" prop="target_user_id">
        <el-select
          v-model="form.target_user_id"
          placeholder="请选择转交目标"
          style="width: 100%"
          :loading="loadingManagers"
        >
          <el-option
            v-for="manager in availableManagers"
            :key="manager.id"
            :label="`${manager.full_name} (${manager.role_name})`"
            :value="manager.id"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="转交说明" prop="forward_comment">
        <el-input
          v-model="form.forward_comment"
          type="textarea"
          :rows="4"
          placeholder="请输入转交说明"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit"
          :loading="submitting"
        >
          确认转交
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { applicationRequestsAPI } from '@/api/applicationRequests'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  application: {
    type: Object,
    default: () => null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'refresh'])

// Reactive data
const formRef = ref()
const submitting = ref(false)
const loadingManagers = ref(false)
const availableManagers = ref([])

const form = reactive({
  target_user_id: '',
  forward_comment: ''
})

const rules = {
  target_user_id: [
    { required: true, message: '请选择转交目标', trigger: 'change' }
  ],
  forward_comment: [
    { required: true, message: '请输入转交说明', trigger: 'blur' }
  ]
}

// Computed
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// Watch
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    resetForm()
    loadAvailableManagers()
  }
})

// Methods
const resetForm = () => {
  form.target_user_id = ''
  form.forward_comment = ''
  formRef.value?.resetFields()
}

const loadAvailableManagers = async () => {
  try {
    loadingManagers.value = true
    const response = await applicationRequestsAPI.getAvailableManagers()
    availableManagers.value = response.data || response || []
  } catch (error) {
    console.error('加载可用管理员失败:', error)
    ElMessage.error('加载可用管理员失败')
  } finally {
    loadingManagers.value = false
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    await applicationRequestsAPI.forwardApplication(props.application.id, {
      target_user_id: form.target_user_id,
      forward_comment: form.forward_comment
    })
    
    ElMessage.success('转交成功')
    emit('refresh')
    dialogVisible.value = false
    
  } catch (error) {
    console.error('转交申请失败:', error)
    ElMessage.error('转交申请失败')
  } finally {
    submitting.value = false
  }
}

const getTypeText = (type) => {
  const typeMap = {
    device_access: '设备访问申请',
    permission_change: '权限变更申请',
    organization_transfer: '组织转移申请',
    other: '其他申请'
  }
  return typeMap[type] || type
}
</script>

<style scoped>
.application-info {
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
}

.application-info p {
  margin: 4px 0;
  line-height: 1.6;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
