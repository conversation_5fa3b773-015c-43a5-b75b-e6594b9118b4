<template>
  <el-dialog
    v-model="dialogVisible"
    title="处理申请"
    width="500px"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
    >
      <el-form-item label="申请信息">
        <div class="application-info">
          <p><strong>标题：</strong>{{ application?.title }}</p>
          <p><strong>申请人：</strong>{{ application?.applicant_name }}</p>
          <p><strong>类型：</strong>{{ getTypeText(application?.application_type) }}</p>
        </div>
      </el-form-item>
      
      <el-form-item label="处理结果" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio label="approved">批准</el-radio>
          <el-radio label="rejected">拒绝</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="处理意见" prop="process_comment">
        <el-input
          v-model="form.process_comment"
          type="textarea"
          :rows="4"
          placeholder="请输入处理意见"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit"
          :loading="submitting"
        >
          确认处理
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { applicationRequestsAPI } from '@/api/applicationRequests'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  application: {
    type: Object,
    default: () => null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'refresh'])

// Reactive data
const formRef = ref()
const submitting = ref(false)

const form = reactive({
  status: '',
  process_comment: ''
})

const rules = {
  status: [
    { required: true, message: '请选择处理结果', trigger: 'change' }
  ],
  process_comment: [
    { required: true, message: '请输入处理意见', trigger: 'blur' }
  ]
}

// Computed
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// Watch
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    resetForm()
  }
})

// Methods
const resetForm = () => {
  form.status = ''
  form.process_comment = ''
  formRef.value?.resetFields()
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    await applicationRequestsAPI.processApplication(props.application.id, {
      status: form.status,
      process_comment: form.process_comment
    })
    
    ElMessage.success('处理成功')
    emit('refresh')
    dialogVisible.value = false
    
  } catch (error) {
    console.error('处理申请失败:', error)
    ElMessage.error('处理申请失败')
  } finally {
    submitting.value = false
  }
}

const getTypeText = (type) => {
  const typeMap = {
    device_access: '设备访问申请',
    permission_change: '权限变更申请',
    organization_transfer: '组织转移申请',
    other: '其他申请'
  }
  return typeMap[type] || type
}
</script>

<style scoped>
.application-info {
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
}

.application-info p {
  margin: 4px 0;
  line-height: 1.6;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
