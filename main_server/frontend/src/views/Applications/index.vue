<template>
  <div class="applications-container">
    <div class="page-header">
      <h2 class="page-title">处理事项</h2>
      <el-button 
        type="primary" 
        @click="$router.push('/applications/create')"
        v-if="userStore.hasPermission('application.submit')"
      >
        <el-icon><Plus /></el-icon>
        创建申请
      </el-button>
    </div>
    
    <!-- 筛选条件 -->
    <div class="filter-container">
      <el-form :model="filterForm" inline>
        <el-form-item label="状态">
          <el-select v-model="filterForm.status" placeholder="全部状态" clearable>
            <el-option label="待处理" value="pending" />
            <el-option label="已批准" value="approved" />
            <el-option label="已拒绝" value="rejected" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="类型">
          <el-select v-model="filterForm.application_type" placeholder="全部类型" clearable>
            <el-option label="权限申请" value="permission_request" />
            <el-option label="角色变更" value="role_change" />
            <el-option label="设备申请" value="device_request" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="优先级">
          <el-select v-model="filterForm.priority" placeholder="全部优先级" clearable>
            <el-option label="低" value="low" />
            <el-option label="普通" value="normal" />
            <el-option label="高" value="high" />
            <el-option label="紧急" value="urgent" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="loadApplications">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 申请列表 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="applications"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        
        <el-table-column prop="title" label="标题" min-width="200" show-overflow-tooltip />
        
        <el-table-column prop="application_type" label="类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.application_type)">
              {{ getTypeText(row.application_type) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="priority" label="优先级" width="100">
          <template #default="{ row }">
            <el-tag :type="getPriorityTagType(row.priority)">
              {{ getPriorityText(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="applicant_name" label="申请人" width="120" />
        
        <el-table-column prop="organization_name" label="所属组织" width="150" show-overflow-tooltip />
        
        <el-table-column prop="created_at" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click.stop="viewApplication(row)"
            >
              查看
            </el-button>
            
            <el-button 
              v-if="row.status === 'pending' && userStore.hasPermission('application.process')"
              type="success" 
              size="small" 
              @click.stop="processApplication(row.id, 'approved')"
            >
              批准
            </el-button>
            
            <el-button 
              v-if="row.status === 'pending' && userStore.hasPermission('application.process')"
              type="danger" 
              size="small" 
              @click.stop="processApplication(row.id, 'rejected')"
            >
              拒绝
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadApplications"
          @current-change="loadApplications"
        />
      </div>
    </div>
    
    <!-- 处理申请对话框 -->
    <el-dialog
      v-model="processDialogVisible"
      :title="processDialogTitle"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="processFormRef"
        :model="processForm"
        :rules="processRules"
        label-width="80px"
      >
        <el-form-item label="处理结果" prop="status">
          <el-radio-group v-model="processForm.status">
            <el-radio label="approved">批准</el-radio>
            <el-radio label="rejected">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="处理意见" prop="response_content">
          <el-input
            v-model="processForm.response_content"
            type="textarea"
            :rows="4"
            placeholder="请输入处理意见"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="processDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleProcessSubmit" :loading="processLoading">
          确定
        </el-button>
      </template>
    </el-dialog>

    <!-- 申请详情对话框 -->
    <ApplicationDetailDialog
      v-model="showDetailDialog"
      :application="selectedApplication"
      @refresh="loadApplications"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { applicationRequestApi } from '@/api/applicationRequests'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import ApplicationDetailDialog from '@/components/ApplicationDetailDialog.vue'

const router = useRouter()
const userStore = useUserStore()

const loading = ref(false)
const applications = ref([])

// 筛选条件
const filterForm = reactive({
  status: '',
  application_type: '',
  priority: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 申请详情对话框
const showDetailDialog = ref(false)
const selectedApplication = ref(null)

// 处理申请对话框
const processDialogVisible = ref(false)
const processDialogTitle = ref('')
const processLoading = ref(false)
const processFormRef = ref()
const currentApplicationId = ref(null)

const processForm = reactive({
  status: '',
  response_content: ''
})

const processRules = {
  status: [
    { required: true, message: '请选择处理结果', trigger: 'change' }
  ],
  response_content: [
    { required: true, message: '请输入处理意见', trigger: 'blur' }
  ]
}

// 加载申请列表
const loadApplications = async () => {
  try {
    loading.value = true
    
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...filterForm
    }
    
    // 移除空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })
    
    const response = await applicationRequestApi.getApplicationRequests(params)

    // 处理新的响应格式：{data: [], total: number, page: number, size: number}
    if (response.data && Array.isArray(response.data)) {
      // 新格式：包含分页信息
      applications.value = response.data
      pagination.total = response.total || 0
    } else if (Array.isArray(response)) {
      // 旧格式：直接返回数组
      applications.value = response
      pagination.total = response.length
    } else {
      // 其他格式
      applications.value = response.data || []
      pagination.total = response.total || 0
    }

    console.log('申请列表加载成功:', {
      count: applications.value.length,
      total: pagination.total,
      page: pagination.page
    })
    
  } catch (error) {
    console.error('加载申请列表失败:', error)
    ElMessage.error('加载申请列表失败')
  } finally {
    loading.value = false
  }
}

// 重置筛选条件
const resetFilter = () => {
  Object.assign(filterForm, {
    status: '',
    application_type: '',
    priority: ''
  })
  pagination.page = 1
  loadApplications()
}

// 查看申请详情
const viewApplication = (application) => {
  selectedApplication.value = application
  showDetailDialog.value = true
}

// 处理申请
const processApplication = (id, status) => {
  currentApplicationId.value = id
  processForm.status = status
  processForm.response_content = ''
  processDialogTitle.value = status === 'approved' ? '批准申请' : '拒绝申请'
  processDialogVisible.value = true
}

// 提交处理结果
const handleProcessSubmit = async () => {
  try {
    await processFormRef.value.validate()
    
    processLoading.value = true
    
    await applicationRequestApi.processApplicationRequest(currentApplicationId.value, {
      status: processForm.status,
      process_comment: processForm.response_content
    })
    
    ElMessage.success('处理成功')
    processDialogVisible.value = false
    loadApplications()
    
  } catch (error) {
    ElMessage.error('处理申请失败')
  } finally {
    processLoading.value = false
  }
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm')
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const types = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger',
    cancelled: 'info'
  }
  return types[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    pending: '待处理',
    approved: '已批准',
    rejected: '已拒绝',
    cancelled: '已取消'
  }
  return texts[status] || status
}

// 获取类型标签类型
const getTypeTagType = (type) => {
  const types = {
    permission_request: 'primary',
    role_change: 'success',
    device_request: 'warning',
    other: 'info'
  }
  return types[type] || 'info'
}

// 获取类型文本
const getTypeText = (type) => {
  const texts = {
    permission_request: '权限申请',
    role_change: '角色变更',
    device_request: '设备申请',
    other: '其他'
  }
  return texts[type] || type
}

// 获取优先级标签类型
const getPriorityTagType = (priority) => {
  const types = {
    low: 'info',
    normal: 'primary',
    high: 'warning',
    urgent: 'danger'
  }
  return types[priority] || 'primary'
}

// 获取优先级文本
const getPriorityText = (priority) => {
  const texts = {
    low: '低',
    normal: '普通',
    high: '高',
    urgent: '紧急'
  }
  return texts[priority] || priority
}

onMounted(() => {
  loadApplications()
})
</script>

<style scoped>
.applications-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.filter-container {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-container {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

:deep(.el-table__row) {
  cursor: pointer;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}
</style>
