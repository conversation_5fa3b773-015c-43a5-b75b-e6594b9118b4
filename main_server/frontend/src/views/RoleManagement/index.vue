<template>
  <div class="role-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2>角色管理</h2>
        <p>管理系统角色和权限分配，仅超级管理员可访问</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog" :icon="Plus">
          创建角色
        </el-button>
      </div>
    </div>

    <!-- 角色列表 -->
    <div class="role-list">
      <el-table :data="roles" style="width: 100%" v-loading="loading">
        <el-table-column prop="name" label="角色名称" width="150">
          <template #default="{ row }">
            <div class="role-name">
              <el-tag v-if="row.is_system_role" type="danger" size="small">系统</el-tag>
              {{ row.name }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="角色描述" width="140" />
        <el-table-column label="权限范围" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.level_scope === 0" type="warning">无限制</el-tag>
            <el-tag v-else type="info">{{ row.level_scope }}级</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'">
              {{ row.is_active ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="250" fixed="right">
          <template #default="{ row }">
            <!-- 查看按钮 -->
            <el-button
              size="small"
              @click="viewRole(row)"
              :icon="View"
            >
              查看
            </el-button>

            <!-- 编辑按钮 -->
            <el-button
              v-if="!isProtectedRole(row)"
              size="small"
              type="primary"
              @click="editRole(row)"
              :icon="Edit"
            >
              编辑
            </el-button>

            <!-- 删除按钮 - 只有手动创建的角色才显示 -->
            <el-button
              v-if="!isSystemDefaultRole(row)"
              size="small"
              type="danger"
              @click="handleDeleteRole(row)"
              :icon="Delete"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 创建/编辑角色对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑角色' : '创建角色'"
      width="900px"
      @close="resetForm"
    >
      <el-form :model="roleForm" :rules="rules" ref="roleFormRef" label-width="120px">
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="roleForm.name" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="角色描述" prop="description">
          <el-input 
            v-model="roleForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入角色描述"
          />
        </el-form-item>
        <el-form-item label="权限层级">
          <el-select v-model="roleForm.level_scope" placeholder="选择权限层级范围">
            <el-option label="无限制" :value="0" />
            <el-option label="1级权限" :value="1" />
            <el-option label="2级权限" :value="2" />
            <el-option label="3级权限" :value="3" />
            <el-option label="4级权限" :value="4" />
          </el-select>
        </el-form-item>
        <el-form-item label="管理权限">
          <el-checkbox-group v-model="managementPermissions">
            <el-checkbox label="can_manage_users">用户管理</el-checkbox>
            <el-checkbox label="can_manage_devices">设备管理</el-checkbox>
            <el-checkbox label="can_view_reports">报告查看</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="功能权限">
          <div class="navigation-permissions">
            <!-- 移除全域管理员特殊提示，改为内部逻辑处理 -->

            <h4>一级权限（导航菜单）</h4>
            <el-checkbox-group
              v-model="roleForm.navigation_permissions"
              class="navigation-group-grid"
              :disabled="isGlobalAdmin || !isEditingPermissions"
            >
              <el-checkbox
                v-for="nav in navigationPermissions"
                :key="nav.key"
                :label="nav.key"
                :disabled="isGlobalAdmin || !isEditingPermissions"
                class="nav-permission-item-grid"
                :class="{
                  'global-admin-item': isGlobalAdmin,
                  'is-disabled': isGlobalAdmin
                }"
              >
                <div class="nav-permission-content">
                  <el-icon class="nav-icon" :class="{ 'global-admin-icon': isGlobalAdmin }">
                    <component :is="nav.icon" />
                  </el-icon>
                  <span>{{ nav.name }}</span>
                  <el-icon v-if="isGlobalAdmin" class="admin-badge">
                    <Star />
                  </el-icon>
                </div>
              </el-checkbox>
            </el-checkbox-group>

            <!-- 设备管理中心的二级权限 -->
            <div v-if="roleForm.navigation_permissions.includes('device-center') || isGlobalAdmin" class="sub-permissions">
              <h4>设备管理中心 - 二级权限</h4>
              <el-checkbox-group
                v-model="roleForm.device_sub_permissions"
                class="sub-permission-group"
                :disabled="isGlobalAdmin || !isEditingPermissions"
              >
                <el-checkbox
                  v-for="sub in deviceSubPermissions"
                  :key="sub.key"
                  :label="sub.key"
                  :disabled="isGlobalAdmin || !isEditingPermissions"
                  class="sub-permission-item"
                  :class="{
                    'global-admin-item': isGlobalAdmin,
                    'is-disabled': isGlobalAdmin || !isEditingPermissions
                  }"
                >
                  <span>{{ sub.name }}</span>
                  <el-icon v-if="isGlobalAdmin" class="admin-badge">
                    <Star />
                  </el-icon>
                </el-checkbox>
              </el-checkbox-group>
            </div>

            <!-- 权限配置操作按钮 -->
            <div v-if="!isGlobalAdmin && isEditingPermissions" class="permission-actions">
              <el-button
                type="success"
                @click="confirmPermissionChanges"
                :disabled="!hasPermissionChanges"
                size="small"
              >
                确定修改
              </el-button>
              <el-button
                @click="resetPermissionChanges"
                :disabled="!hasPermissionChanges"
                size="small"
              >
                放弃修改
              </el-button>
              <span v-if="hasPermissionChanges" class="change-indicator">
                权限配置已修改
              </span>
            </div>

            <!-- 移除全域管理员权限说明，改为内部逻辑处理 -->
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="startEditingPermissions" v-if="!isEditingPermissions">开始修改</el-button>
          <el-button @click="cancelEdit" v-if="isEditingPermissions">取消修改</el-button>
          <el-button type="primary" @click="confirmSaveRole" :loading="saving" v-if="isEditingPermissions">
            确认保存
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 角色详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="角色详情" width="500px">
      <div v-if="selectedRole" class="role-detail">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="角色名称">{{ selectedRole.name }}</el-descriptions-item>
          <el-descriptions-item label="角色描述">{{ selectedRole.description }}</el-descriptions-item>
          <el-descriptions-item label="系统角色">
            <el-tag :type="selectedRole.is_system_role ? 'danger' : 'success'">
              {{ selectedRole.is_system_role ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="权限层级">
            <el-tag v-if="selectedRole.level_scope === 0" type="warning">无限制</el-tag>
            <el-tag v-else type="info">{{ selectedRole.level_scope }}级</el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>


    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Edit, Delete, View, House, Document, OfficeBuilding,
  UserFilled, Monitor, Setting, DataAnalysis, Star, InfoFilled, User
} from '@element-plus/icons-vue'
import {
  getRoles,
  createRole,
  updateRole,
  deleteRole,
  getPermissionGroups,
  getPermissionDescriptions,
  validateRolePermissions
} from '@/api/roles'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const dialogVisible = ref(false)
const detailDialogVisible = ref(false)
const isEdit = ref(false)
const isEditingPermissions = ref(false) // 控制权限是否可编辑
const roles = ref([])
const selectedRole = ref(null)
const roleFormRef = ref()

// 表单数据
const roleForm = reactive({
  name: '',
  description: '',
  level_scope: 0,
  permissions: [],
  navigation_permissions: [],
  device_sub_permissions: [],
  can_manage_users: false,
  can_manage_devices: false,
  can_view_reports: false
})

// 原始权限数据备份（用于检测变更和重置）
const originalPermissions = reactive({
  navigation_permissions: [],
  device_sub_permissions: []
})

// 管理权限的计算属性
const managementPermissions = computed({
  get() {
    const perms = []
    if (roleForm.can_manage_users) perms.push('can_manage_users')
    if (roleForm.can_manage_devices) perms.push('can_manage_devices')
    if (roleForm.can_view_reports) perms.push('can_view_reports')
    return perms
  },
  set(value) {
    roleForm.can_manage_users = value.includes('can_manage_users')
    roleForm.can_manage_devices = value.includes('can_manage_devices')
    roleForm.can_view_reports = value.includes('can_view_reports')
  }
})

// 全域管理员检测 - 只检测被编辑角色的名称
const isGlobalAdmin = computed(() => {
  return roleForm.name === '全域管理员'
})

// 权限变更检测
const hasPermissionChanges = computed(() => {
  // 全域管理员权限不可修改
  if (isGlobalAdmin.value) return false

  const navChanged = JSON.stringify(roleForm.navigation_permissions.sort()) !==
                    JSON.stringify(originalPermissions.navigation_permissions.sort())
  const deviceChanged = JSON.stringify(roleForm.device_sub_permissions.sort()) !==
                       JSON.stringify(originalPermissions.device_sub_permissions.sort())
  return navChanged || deviceChanged
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 50, message: '角色名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入角色描述', trigger: 'blur' }
  ]
}

// 导航权限定义（与数据库定义完全一致）
const navigationPermissions = [
  { key: 'dashboard', name: '工作台', icon: House },
  { key: 'applications', name: '处理事项', icon: Document },
  { key: 'org-users', name: '组织与用户管理', icon: OfficeBuilding },
  { key: 'user-registration', name: '新用户审核', icon: UserFilled },
  { key: 'device-center', name: '设备管理中心', icon: Monitor },
  { key: 'client-management', name: '设备绑定中心', icon: Monitor },
  { key: 'role-management', name: '角色管理', icon: UserFilled },
  { key: 'system-settings', name: '系统设置', icon: Setting },
  { key: 'data-dashboard', name: '数据大屏', icon: DataAnalysis },
  { key: 'profile-management', name: '个人资料管理', icon: User }
]

// 设备管理中心的二级权限
const deviceSubPermissions = [
  { key: 'usb-devices', name: 'USB设备管理' },
  { key: 'slave-servers', name: '分布式节点管理' },
  { key: 'device-groups', name: '资源调度分组' },
  { key: 'permission-assignment', name: '授权范围管理' }
]

// 获取权限分组和描述
const permissionGroups = getPermissionGroups()
const permissionDescriptions = getPermissionDescriptions()

// 方法
const loadRoles = async () => {
  loading.value = true
  try {
    const response = await getRoles()

    // 🔧 修复：处理API中间件包装格式
    let roleData = response
    if (response && response.success && response.data) {
      console.log('🔧 loadRoles - 检测到API中间件包装格式，提取data字段')
      roleData = response.data
    }
    console.log('loadRoles - 处理后的角色数据:', roleData)

    // 处理角色数据格式
    if (roleData && roleData.roles) {
      roles.value = Array.isArray(roleData.roles) ? roleData.roles : []
      console.log('加载角色列表成功:', roles.value.length, '个角色')
      if (roleData.filtered_by_permission) {
        console.log('权限过滤已生效')
      }
    } else if (Array.isArray(roleData)) {
      // 兼容直接返回数组格式
      roles.value = roleData
      console.log('加载角色列表成功（数组格式）:', roles.value.length, '个角色')
    } else {
      // 兜底处理
      roles.value = []
      console.log('角色数据格式异常，设置为空数组')
    }
  } catch (error) {
    ElMessage.error('加载角色列表失败')
    console.error('Load roles error:', error)
  } finally {
    loading.value = false
  }
}

const showCreateDialog = () => {
  isEdit.value = false
  resetForm()
  backupPermissions()
  dialogVisible.value = true
}

const editRole = (role) => {
  isEdit.value = true

  console.log('编辑角色原始数据:', role)

  // 智能权限预设机制 - 根据数据库实际权限配置（与数据库完全一致）
  const rolePermissionTemplates = {
    '全域管理员': {
      navigation: ['dashboard', 'applications', 'org-users', 'user-registration', 'device-center', 'client-management', 'role-management', 'system-settings', 'data-dashboard'],
      device: ['usb-devices', 'slave-servers', 'device-groups', 'permission-assignment'],
      management: { can_manage_users: true, can_manage_devices: true, can_view_reports: true }
    },
    '超级管理员': {
      navigation: ['dashboard', 'applications', 'org-users', 'user-registration', 'device-center', 'client-management', 'role-management', 'system-settings'],
      device: ['usb-devices', 'slave-servers', 'device-groups', 'permission-assignment'],
      management: { can_manage_users: true, can_manage_devices: true, can_view_reports: true }
    },
    '管理员': {
      navigation: ['dashboard', 'applications', 'org-users', 'user-registration', 'device-center', 'client-management'],
      device: ['usb-devices', 'device-groups'],
      management: { can_manage_users: true, can_manage_devices: true, can_view_reports: true }
    },
    '普通用户': {
      navigation: ['dashboard', 'applications'],
      device: [],
      management: { can_manage_users: false, can_manage_devices: false, can_view_reports: false }
    },
    '新用户': {
      navigation: ['dashboard', 'applications'],
      device: [],
      management: { can_manage_users: false, can_manage_devices: false, can_view_reports: false }
    }
  }

  const template = rolePermissionTemplates[role.name]

  // 优先使用现有权限，如果没有则使用模板权限
  const currentNavigationPermissions = role.navigation_permissions || []
  const currentDeviceSubPermissions = role.device_sub_permissions || []

  // 如果角色已有权限配置，使用现有权限；否则使用模板预设
  const finalNavigationPermissions = currentNavigationPermissions.length > 0 ?
    currentNavigationPermissions :
    (template ? template.navigation : [])

  const finalDeviceSubPermissions = currentDeviceSubPermissions.length > 0 ?
    currentDeviceSubPermissions :
    (template ? template.device : [])

  console.log('权限配置详情:', {
    roleName: role.name,
    currentNav: currentNavigationPermissions,
    currentDevice: currentDeviceSubPermissions,
    finalNav: finalNavigationPermissions,
    finalDevice: finalDeviceSubPermissions
  })

  // 设置管理权限 - 优先使用现有值
  const managementPerms = template ? template.management : {}

  Object.assign(roleForm, {
    id: role.id,
    name: role.name,
    description: role.description,
    permissions: role.permissions || [],
    navigation_permissions: finalNavigationPermissions,
    device_sub_permissions: finalDeviceSubPermissions,
    level_scope: role.level_scope || 1,
    can_manage_users: role.can_manage_users !== undefined ? role.can_manage_users : managementPerms.can_manage_users,
    can_manage_devices: role.can_manage_devices !== undefined ? role.can_manage_devices : managementPerms.can_manage_devices,
    can_view_reports: role.can_view_reports !== undefined ? role.can_view_reports : managementPerms.can_view_reports
  })

  console.log('编辑角色最终数据:', {
    roleName: role.name,
    template: template,
    finalForm: roleForm
  })

  backupPermissions()
  isEditingPermissions.value = false // 重置编辑状态，需要点击"开始修改"才能编辑
  dialogVisible.value = true
}

const viewRole = (role) => {
  selectedRole.value = role
  detailDialogVisible.value = true
}

const resetForm = () => {
  Object.assign(roleForm, {
    name: '',
    description: '',
    level_scope: 0,
    permissions: [],
    navigation_permissions: [],
    device_sub_permissions: [],
    can_manage_users: false,
    can_manage_devices: false,
    can_view_reports: false
  })
  roleFormRef.value?.resetFields()
  // 重置权限备份
  backupPermissions()
  // 重置编辑状态
  isEditingPermissions.value = false
}

// 编辑流程控制方法
const startEditingPermissions = () => {
  isEditingPermissions.value = true
  ElMessage.info('现在可以编辑权限配置')
}

const cancelEdit = () => {
  isEditingPermissions.value = false
  dialogVisible.value = false
  resetForm()
}

const confirmSaveRole = async () => {
  try {
    await saveRole()
    isEditingPermissions.value = false
  } catch (error) {
    console.error('保存角色失败:', error)
  }
}

// 权限操作方法
const backupPermissions = () => {
  originalPermissions.navigation_permissions = [...roleForm.navigation_permissions]
  originalPermissions.device_sub_permissions = [...roleForm.device_sub_permissions]
}

const confirmPermissionChanges = () => {
  // 确认权限修改，更新备份
  backupPermissions()
  ElMessage.success('权限配置已确认')
}

const resetPermissionChanges = () => {
  // 重置权限到原始状态
  roleForm.navigation_permissions = [...originalPermissions.navigation_permissions]
  roleForm.device_sub_permissions = [...originalPermissions.device_sub_permissions]
  ElMessage.info('权限配置已重置')
}

const saveRole = async () => {
  if (!roleFormRef.value) return

  try {
    await roleFormRef.value.validate()
    saving.value = true

    // 准备保存数据，确保权限字段正确
    const data = {
      name: roleForm.name,
      description: roleForm.description,
      permissions: roleForm.permissions || [],
      navigation_permissions: roleForm.navigation_permissions || [],
      device_sub_permissions: roleForm.device_sub_permissions || [],
      level_scope: roleForm.level_scope || 1,
      can_manage_users: roleForm.can_manage_users || false,
      can_manage_devices: roleForm.can_manage_devices || false,
      can_view_reports: roleForm.can_view_reports || false
    }

    console.log('保存角色数据:', data)

    if (isEdit.value) {
      const response = await updateRole(roleForm.id, data)
      console.log('角色更新响应:', response)
      ElMessage.success('角色更新成功')

      // 确保异步操作按正确顺序执行
      try {
        // 1. 首先触发权限更新事件，通知其他组件
        await notifyPermissionUpdate(roleForm.id, data)

        // 2. 等待一小段时间确保事件处理完成
        await new Promise(resolve => setTimeout(resolve, 100))

        // 3. 最后刷新角色权限显示
        await refreshRolePermissions(roleForm.id)

        console.log('角色权限同步流程完成:', roleForm.id)
      } catch (syncError) {
        console.error('权限同步过程中出现错误:', syncError)
        ElMessage.warning('权限更新成功，但同步显示可能有延迟')
      }
    } else {
      const response = await createRole(data)
      console.log('角色创建响应:', response)
      ElMessage.success('角色创建成功')
    }

    // 重新加载角色列表
    await loadRoles()

    // 关闭对话框
    dialogVisible.value = false

  } catch (error) {
    ElMessage.error(isEdit.value ? '角色更新失败' : '角色创建失败')
    console.error('Save role error:', error)
  } finally {
    saving.value = false
  }
}

const handleDeleteRole = async (role) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除角色 "${role.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteRole(role.id)
    ElMessage.success('角色删除成功')
    await loadRoles()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('角色删除失败')
      console.error('Delete role error:', error)
    }
  }
}

const getPermissionName = (permission) => {
  return permissionDescriptions[permission] || permission
}

// 获取导航权限名称
const getNavigationPermissionName = (navKey) => {
  const nav = navigationPermissions.find(n => n.key === navKey)
  return nav ? nav.name : navKey
}

// 获取导航权限图标
const getNavigationPermissionIcon = (navKey) => {
  const nav = navigationPermissions.find(n => n.key === navKey)
  return nav ? nav.icon : House
}

// 获取导航权限类型（用于标签颜色）
const getNavigationPermissionType = (navKey) => {
  const typeMap = {
    'dashboard': 'primary',
    'applications': 'success',
    'org-users': 'warning',
    'user-registration': 'info',
    'device-center': 'danger',
    'client-management': 'primary',
    'role-management': 'warning',
    'system-settings': 'danger',
    'data-dashboard': 'info'
  }
  return typeMap[navKey] || 'primary'
}

// 获取设备子权限名称
const getDeviceSubPermissionName = (deviceKey) => {
  const device = deviceSubPermissions.find(d => d.key === deviceKey)
  return device ? device.name : deviceKey
}

// 从详情页面跳转到编辑
const editFromDetail = () => {
  if (selectedRole.value) {
    detailDialogVisible.value = false
    editRole(selectedRole.value)
  }
}

// 判断是否为受保护的角色 - 简化逻辑，只保护全域管理员
const isProtectedRole = (role) => {
  // 只有全域管理员是受保护的
  return role.name === '全域管理员'
}

// 判断是否为系统默认角色 - 这些角色不能删除
const isSystemDefaultRole = (role) => {
  const systemDefaultRoles = ['全域管理员', '超级管理员', '管理员', '普通用户', '新用户']
  return systemDefaultRoles.includes(role.name)
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 权限更新通知方法
const notifyPermissionUpdate = async (roleId, roleData) => {
  try {
    // 发送全局权限更新事件
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('rolePermissionUpdated', {
        detail: {
          roleId: roleId,
          roleName: roleData.name,
          permissions: {
            navigation: roleData.navigation_permissions || [],
            device: roleData.device_sub_permissions || [],
            management: {
              can_manage_users: roleData.can_manage_users,
              can_manage_devices: roleData.can_manage_devices,
              can_view_reports: roleData.can_view_reports
            }
          },
          timestamp: new Date().toISOString()
        }
      }))
    }

    console.log('权限更新事件已发送:', roleId, roleData.name)
  } catch (error) {
    console.error('发送权限更新事件失败:', error)
  }
}

const refreshRolePermissions = async (roleId) => {
  try {
    if (!roleId) return

    console.log('开始刷新角色权限:', roleId)

    // 重新获取角色详情以确保权限显示正确
    const response = await getRoleById(roleId)

    // 处理API响应格式 - 支持多种响应格式
    let roleDetail = null
    if (response && response.success && response.data) {
      roleDetail = response.data
    } else if (response && response.id) {
      roleDetail = response
    } else {
      throw new Error('API响应格式异常')
    }

    console.log('获取角色详情:', roleDetail)

    // 验证必要字段
    if (!roleDetail || !roleDetail.id) {
      throw new Error('角色数据不完整')
    }

    // 更新当前编辑的角色表单数据
    if (isEdit.value && roleForm.id === roleId) {
      // 使用深拷贝创建新的表单数据，确保所有权限字段都被正确处理
      const updatedForm = {
        id: roleDetail.id,
        name: roleDetail.name || '',
        description: roleDetail.description || '',
        permissions: Array.isArray(roleDetail.permissions) ? [...roleDetail.permissions] : [],
        navigation_permissions: Array.isArray(roleDetail.navigation_permissions) ? [...roleDetail.navigation_permissions] : [],
        device_sub_permissions: Array.isArray(roleDetail.device_sub_permissions) ? [...roleDetail.device_sub_permissions] : [],
        level_scope: roleDetail.level_scope || 1,
        can_manage_users: Boolean(roleDetail.can_manage_users),
        can_manage_devices: Boolean(roleDetail.can_manage_devices),
        can_view_reports: Boolean(roleDetail.can_view_reports)
      }

      // 完全重新赋值表单数据，确保响应式更新
      Object.keys(updatedForm).forEach(key => {
        if (key in roleForm) {
          roleForm[key] = updatedForm[key]
        }
      })

      // 更新权限备份数据
      originalPermissions.navigation_permissions = [...updatedForm.navigation_permissions]
      originalPermissions.device_sub_permissions = [...updatedForm.device_sub_permissions]

      console.log('表单数据已完全更新:', {
        roleId: roleId,
        roleName: roleForm.name,
        navigationPermissions: roleForm.navigation_permissions,
        devicePermissions: roleForm.device_sub_permissions,
        managementPermissions: {
          can_manage_users: roleForm.can_manage_users,
          can_manage_devices: roleForm.can_manage_devices,
          can_view_reports: roleForm.can_view_reports
        }
      })
    }

    // 更新角色列表中的对应项
    const roleIndex = roles.value.findIndex(role => role.id === roleId)
    if (roleIndex !== -1) {
      roles.value[roleIndex] = {
        ...roles.value[roleIndex],
        ...roleDetail
      }
    }

    console.log('角色权限刷新完成:', roleId)
    ElMessage.success('权限数据已同步')
  } catch (error) {
    console.error('刷新角色权限失败:', error)
    ElMessage.error(`刷新角色权限失败: ${error.message}`)
  }
}

// 权限同步监听器
const setupPermissionSyncListener = () => {
  if (typeof window !== 'undefined') {
    // 监听权限更新事件
    window.addEventListener('rolePermissionUpdated', (event) => {
      const { roleId, roleName, permissions, timestamp } = event.detail

      console.log('接收到权限更新事件:', { roleId, roleName, timestamp })

      // 更新角色列表中的权限显示
      const roleIndex = roles.value.findIndex(role => role.id === roleId)
      if (roleIndex !== -1) {
        roles.value[roleIndex] = {
          ...roles.value[roleIndex],
          navigation_permissions: permissions.navigation,
          device_sub_permissions: permissions.device,
          ...permissions.management
        }
      }

      // 如果当前正在编辑这个角色，更新表单数据
      if (isEdit.value && roleForm.id === roleId) {
        Object.assign(roleForm, {
          ...roleForm,
          navigation_permissions: permissions.navigation,
          device_sub_permissions: permissions.device,
          ...permissions.management
        })
      }

      ElMessage.success(`角色"${roleName}"权限已同步更新`)
    })

    // 监听设备权限变更事件
    window.addEventListener('devicePermissionChanged', (event) => {
      const { userId, deviceId, action, timestamp } = event.detail
      console.log('设备权限变更:', { userId, deviceId, action, timestamp })

      // 触发相关角色权限的重新加载
      loadRoles()
    })
  }
}

// 清理监听器
const cleanupPermissionSyncListener = () => {
  if (typeof window !== 'undefined') {
    window.removeEventListener('rolePermissionUpdated', () => {})
    window.removeEventListener('devicePermissionChanged', () => {})
  }
}

// 生命周期
onMounted(() => {
  loadRoles()
  setupPermissionSyncListener()
})

onUnmounted(() => {
  cleanupPermissionSyncListener()
})
</script>

<style scoped>
.role-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.header-content p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.role-list {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.role-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.permission-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.permission-group {
  margin-bottom: 16px;
}

.permission-group h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 14px;
}

.permission-group .el-checkbox {
  margin-right: 16px;
  margin-bottom: 8px;
}

.role-detail .permission-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  background: #fafafa;
}

/* 新的权限配置样式 */
.navigation-permissions {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 16px;
  background-color: #fafbfc;
}

.navigation-permissions h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.navigation-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-bottom: 20px;
}

/* 三列网格布局 */
.navigation-group-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  margin-bottom: 16px;
}

.nav-permission-item-grid {
  margin: 0;
  padding: 8px 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: white;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  width: 100%;
  cursor: pointer;
}

.nav-permission-item-grid:not(.is-disabled) {
  cursor: pointer;
}

.nav-permission-item-grid.is-disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.nav-permission-item-grid:hover:not(.is-disabled) {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.nav-permission-item {
  margin: 0;
  padding: 8px 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: white;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  width: 100%;
  cursor: pointer;
}

.nav-permission-item:not(.is-disabled) {
  cursor: pointer;
}

.nav-permission-item.is-disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.nav-permission-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.nav-permission-item.is-checked {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.nav-permission-item .el-checkbox__input {
  margin-right: 8px;
}

.nav-permission-item .el-checkbox__label {
  padding-left: 0;
}

.nav-permission-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-icon {
  color: #409eff;
  font-size: 16px;
}

.sub-permissions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

.sub-permission-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 8px;
}

.sub-permission-item {
  margin: 0;
  padding: 6px 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: white;
  font-size: 13px;
  display: flex;
  align-items: center;
  width: 100%;
  cursor: pointer;
}

.sub-permission-item:not(.is-disabled) {
  cursor: pointer;
}

.sub-permission-item.is-disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.sub-permission-item:hover {
  border-color: #67c23a;
  background-color: #f0f9ff;
}

.sub-permission-item.is-checked {
  border-color: #67c23a;
  background-color: #f0f9ff;
}

.sub-permission-item .el-checkbox__input {
  margin-right: 6px;
}

.sub-permission-item .el-checkbox__label {
  padding-left: 0;
}

/* 权限操作按钮样式 */
.permission-actions {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  gap: 12px;
}

.change-indicator {
  color: #e6a23c;
  font-size: 12px;
  font-weight: 500;
  margin-left: 8px;
}

/* 全域管理员权限样式 */
.global-admin-permissions {
  background: linear-gradient(135deg, #fff9e6 0%, #fff2cc 100%);
  border: 2px solid #f0c040;
  border-radius: 8px;
}

.global-admin-notice {
  margin-bottom: 16px;
}

.global-admin-notice .el-alert {
  border-radius: 6px;
}

.global-admin-item {
  background: linear-gradient(135deg, #fff9e6 0%, #fff2cc 100%) !important;
  border-color: #f0c040 !important;
  opacity: 0.9;
}

.global-admin-item:hover {
  background: linear-gradient(135deg, #fff2cc 0%, #ffe6b3 100%) !important;
  border-color: #e6a23c !important;
}

.global-admin-icon {
  color: #e6a23c !important;
}

.admin-badge {
  color: #f56c6c;
  font-size: 14px;
  margin-left: 4px;
}

.global-admin-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 6px;
  color: #409eff;
  font-size: 13px;
  margin-top: 16px;
}

.info-icon {
  font-size: 16px;
  color: #409eff;
}

/* 权限详情样式 */
.permission-details {
  background-color: #fafbfc;
  border-radius: 6px;
  padding: 16px;
  border: 1px solid #e4e7ed;
}

.permission-details h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.permission-section {
  margin-bottom: 12px;
}

.permission-section h5 {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 600;
  margin: 0;
}

.permission-tags {
  margin-top: 8px;
  min-height: 24px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.permission-tags .el-tag {
  display: flex;
  align-items: center;
}

.no-permission {
  color: #909399;
  font-size: 12px;
  font-style: italic;
  padding: 4px 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px dashed #dcdfe6;
}

.role-detail .dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
